package com.ruoyi.common.utils;

import org.slf4j.LoggerFactory;

/**
 * 日志工具类，基于SLF4J API
 *
 * <AUTHOR>
 */
public class Logger {

    /**
     * 获取指定类的日志记录器
     *
     * @param clazz 类
     * @return 日志记录器
     */
    public static org.slf4j.Logger getLogger(Class<?> clazz) {
        return LoggerFactory.getLogger(clazz);
    }

    /**
     * 获取指定名称的日志记录器
     *
     * @param name 名称
     * @return 日志记录器
     */
    public static org.slf4j.Logger getLogger(String name) {
        return LoggerFactory.getLogger(name);
    }

    /**
     * 记录调试级别的日志
     *
     * @param logger 日志记录器
     * @param format 格式化字符串
     * @param args 参数
     */
    public static void debug(org.slf4j.Logger logger, String format, Object... args) {
        if (logger.isDebugEnabled()) {
            logger.debug(format, args);
        }
    }

    /**
     * 记录信息级别的日志
     *
     * @param logger 日志记录器
     * @param format 格式化字符串
     * @param args 参数
     */
    public static void info(org.slf4j.Logger logger, String format, Object... args) {
        if (logger.isInfoEnabled()) {
            logger.info(format, args);
        }
    }

    /**
     * 记录警告级别的日志
     *
     * @param logger 日志记录器
     * @param format 格式化字符串
     * @param args 参数
     */
    public static void warn(org.slf4j.Logger logger, String format, Object... args) {
        if (logger.isWarnEnabled()) {
            logger.warn(format, args);
        }
    }

    /**
     * 记录错误级别的日志
     *
     * @param logger 日志记录器
     * @param format 格式化字符串
     * @param args 参数
     */
    public static void error(org.slf4j.Logger logger, String format, Object... args) {
        if (logger.isErrorEnabled()) {
            logger.error(format, args);
        }
    }

    /**
     * 记录错误级别的日志，包含异常信息
     *
     * @param logger 日志记录器
     * @param e 异常
     * @param format 格式化字符串
     * @param args 参数
     */
    public static void error(org.slf4j.Logger logger, Throwable e, String format, Object... args) {
        if (logger.isErrorEnabled()) {
            logger.error(String.format(format, args), e);
        }
    }

    /**
     * 记录调试级别的日志，使用当前类作为日志记录器
     *
     * @param format 格式化字符串
     * @param args 参数
     */
    public static void debug(String format, Object... args) {
        StackTraceElement[] stackTrace = Thread.currentThread().getStackTrace();
        if (stackTrace.length >= 3) {
            String className = stackTrace[2].getClassName();
            try {
                Class<?> clazz = Class.forName(className);
                debug(getLogger(clazz), format, args);
            } catch (ClassNotFoundException e) {
                debug(getLogger(className), format, args);
            }
        }
    }

    /**
     * 记录信息级别的日志，使用当前类作为日志记录器
     *
     * @param format 格式化字符串
     * @param args 参数
     */
    public static void info(String format, Object... args) {
        StackTraceElement[] stackTrace = Thread.currentThread().getStackTrace();
        if (stackTrace.length >= 3) {
            String className = stackTrace[2].getClassName();
            try {
                Class<?> clazz = Class.forName(className);
                info(getLogger(clazz), format, args);
            } catch (ClassNotFoundException e) {
                info(getLogger(className), format, args);
            }
        }
    }

    /**
     * 记录警告级别的日志，使用当前类作为日志记录器
     *
     * @param format 格式化字符串
     * @param args 参数
     */
    public static void warn(String format, Object... args) {
        StackTraceElement[] stackTrace = Thread.currentThread().getStackTrace();
        if (stackTrace.length >= 3) {
            String className = stackTrace[2].getClassName();
            try {
                Class<?> clazz = Class.forName(className);
                warn(getLogger(clazz), format, args);
            } catch (ClassNotFoundException e) {
                warn(getLogger(className), format, args);
            }
        }
    }

    /**
     * 记录错误级别的日志，使用当前类作为日志记录器
     *
     * @param format 格式化字符串
     * @param args 参数
     */
    public static void error(String format, Object... args) {
        StackTraceElement[] stackTrace = Thread.currentThread().getStackTrace();
        if (stackTrace.length >= 3) {
            String className = stackTrace[2].getClassName();
            try {
                Class<?> clazz = Class.forName(className);
                error(getLogger(clazz), format, args);
            } catch (ClassNotFoundException e) {
                error(getLogger(className), format, args);
            }
        }
    }

    /**
     * 记录错误级别的日志，包含异常信息，使用当前类作为日志记录器
     *
     * @param e 异常
     * @param format 格式化字符串
     * @param args 参数
     */
    public static void error(Throwable e, String format, Object... args) {
        StackTraceElement[] stackTrace = Thread.currentThread().getStackTrace();
        if (stackTrace.length >= 3) {
            String className = stackTrace[2].getClassName();
            try {
                Class<?> clazz = Class.forName(className);
                error(getLogger(clazz), e, format, args);
            } catch (ClassNotFoundException e1) {
                error(getLogger(className), e, format, args);
            }
        }
    }
}
