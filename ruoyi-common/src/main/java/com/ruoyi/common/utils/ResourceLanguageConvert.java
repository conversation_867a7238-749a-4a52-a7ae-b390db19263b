package com.ruoyi.common.utils;

import com.alibaba.fastjson2.JSONObject;

import java.lang.reflect.Field;

/**
 * 资源多语言属性转换
 */
public class ResourceLanguageConvert {
    public static void convert(Object obj, String type) {
       try {
           if (obj == null || StringUtils.isEmpty(type)) {
               return;
           }
           // 获取对象的类类型
           Class<?> clazz = obj.getClass();
           Field lang = clazz.getDeclaredField("lang");
           lang.setAccessible(true);

           // 检查lang字段值是否为null
           Object langValue = lang.get(obj);
           if (langValue == null) {
               return;
           }

           // 确保lang字段是String类型
           if (!(langValue instanceof String)) {
               return;
           }

           JSONObject jsonObject = JSONObject.parse((String) langValue);

           // 检查type是否存在
           if (!jsonObject.containsKey(type)) {
               return;
           }

           JSONObject jsonObj = jsonObject.getJSONObject(type);
           if (jsonObj == null) {
               return;
           }

           //获取这个字段的值
           // jsonObj
           for (String key : jsonObj.keySet()) {
               try {
                   // jsonObj
                   java.lang.reflect.Field objField = clazz.getDeclaredField(key);
                   // jsonObj
                   Object value = jsonObj.get(objField.getName());
                   if (value != null) {
                       objField.setAccessible(true);
                       objField.set(obj, value);
                   }
               } catch (NoSuchFieldException | IllegalAccessException e) {
                   // 字段不存在或者无法访问时跳过
                   continue;
               }
           }
       }catch (Exception e){
           e.printStackTrace();
       }
    }
}
