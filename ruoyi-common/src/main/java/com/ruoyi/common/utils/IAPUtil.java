package com.ruoyi.common.utils;

import com.alibaba.fastjson2.JSONObject;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.concurrent.TimeUnit;

/**
 * 内购工具类
 */
@Slf4j
@Component
public class IAPUtil {

    // 苹果沙盒验证接口
    private static final String apple_sandboxUrl = "https://sandbox.itunes.apple.com/verifyReceipt";

    // 苹果生产环境验证接口
    private static final String apple_productionUrl = "https://buy.itunes.apple.com/verifyReceipt";

    // 苹果订阅参数
    private static final String sharedSecret = null;

    // 最大重试次数
    private static final Integer maxRetries = 3;

    @PostConstruct
    public void init() {
        log.info("IAP配置初始化完成 - sandbox: {}, production: {}, hasSharedSecret: {}",
                apple_sandboxUrl, apple_productionUrl, StringUtils.hasText(sharedSecret));
    }

    /**
     * 验证苹果内购票据
     *
     * @param extra 内购票据
     * @return
     */
    public static String verifyApple(String extra, Boolean isSandbox) {
        // 传递当前重试次数（初始为0）
        return verifyAppleWithRetry(extra, isSandbox, 0);
    }

    /**
     * 验证苹果内购票据
     *
     * @param extra      内购票据
     * @param isSandbox  是否是沙盒环境
     * @param againCount 重试次数
     * @return
     */
    public static String verifyAppleWithRetry(String extra, Boolean isSandbox, int againCount) {
        String verifyUrl = isSandbox ? apple_sandboxUrl : apple_productionUrl;
        log.info("【{}】开始验证苹果内购收据(第{}次尝试)，URL: {}", isSandbox ? "沙盒环境" : "生产环境", againCount + 1, verifyUrl);

        String receiptData = extra.replace(" ", "+");

        String result = getAppleVerifyResult(receiptData, verifyUrl);
        if (StringUtils.isEmpty(result)) {
            return result;
        }
        JSONObject receipt = JSONObject.parseObject(result);

        if (receipt != null) {
            Integer status = receipt.getInteger("status");
            log.warn("苹果内购收据返回状态码:【{}】", status);

            switch (status) {
                case 0:
                    // 验证成功
                    log.info("【{}】验证成功", isSandbox ? "沙盒环境" : "生产环境");
                    return result;
                case 21000:
                    // App Store无法读取提供的JSON对象
                    log.error("【状态码21000】App Store无法读取提供的JSON对象");
                    return null;
                case 21002:
                    // 收据数据不符合格式
                    log.error("【状态码21002】收据数据不符合格式");
                    return null;
                case 21003:
                    // 收据无法验证
                    log.error("【状态码21003】收据无法验证");
                    return null;
                case 21004:
                    // 提供的共享密钥与账户的共享密钥不匹配
                    log.error("【状态码21004】提供的共享密钥与账户的共享密钥不匹配");
                    return null;
                case 21005:
                    // 收据服务器暂时不可用
                    log.warn("【状态码21005】苹果服务器不可用，执行次数 ({}/{})", againCount + 1, maxRetries);
                    againCount += 1;
                    // 添加延迟再重试
                    try {
                        Thread.sleep(1000 * againCount); // 递增延迟
                    } catch (InterruptedException e) {
                        log.error("重试等待被中断", e);
                        Thread.currentThread().interrupt();
                    }
                    if (againCount >= maxRetries) {
                        return null;
                    }
                    return verifyAppleWithRetry(extra, isSandbox, againCount); // 递归重试
                case 21006:
                    // 收据有效，但订阅已过期
                    log.warn("【状态码21006】收据有效，但订阅已过期");
                    // 对于订阅，这个状态也是有用的，返回结果让业务层处理过期逻辑
                    return result; // 返回结果让业务层处理过期逻辑
                case 21007:
                    // 收据来自测试环境，但发送至生产环境验证
                    return verifyAppleWithRetry(extra, true, againCount);
                case 21008:
                    // 收据来自生产环境，但发送至测试环境验证
                    log.info("【状态码21008】生产环境收据，切换到生产环境验证");
                    return verifyAppleWithRetry(extra, false, againCount);
                case 21009:
                    // 内部数据访问错误
                    log.error("【状态码21009】内部数据访问错误");
                    return null;
                case 21010:
                    // 用户账号无法找到或已被删除
                    log.error("【状态码21010】用户账号无法找到或已被删除");
                    return null;
                default:
                    log.error("【未知状态码】苹果服务器返回未知状态码: {}", status);
                    return null;
            }
        } else {
            log.error("【验证失败】请求苹果服务器失败或返回数据解析失败");
            return null;
        }
    }

    /**
     * 调用苹果验证接口获取结果
     *
     * @param receiptData 收据数据
     * @param verifyUrl   验证URL
     * @return 验证结果
     */
    private static String getAppleVerifyResult(String receiptData, String verifyUrl) {
        try {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("receipt-data", receiptData);

            // 如果有共享密钥，添加到请求中（用于订阅产品验证）
            if (StringUtils.hasText(sharedSecret)) {
                jsonObject.put("password", sharedSecret);
                log.debug("使用共享密钥进行验证");
            }
            // 对于订阅，我们希望获取最新的交易信息
            jsonObject.put("exclude-old-transactions", true);

            OkHttpClient okHttpClient = new OkHttpClient.Builder()
                    .connectTimeout(30, TimeUnit.SECONDS)
                    .readTimeout(30, TimeUnit.SECONDS)
                    .build();
            MediaType mediaType = MediaType.parse("application/json;charset=utf-8");
            RequestBody stringBody = RequestBody.create(mediaType, jsonObject.toString());
            Request request = new Request.Builder().url(verifyUrl).post(stringBody).build();

            Response response = okHttpClient.newCall(request).execute();
            if (response.isSuccessful() && response.body() != null) {
                String responseBody = response.body().string();
                log.info("苹果内购验证API请求成功，响应状态码: {}", response.code());
                return responseBody;
            } else {
                log.error("苹果内购验证API请求失败，状态码: {}", response.code());
                return null;
            }
        } catch (Exception e) {
            log.error("苹果内购验证异常", e);
            return null;
        }
    }
}
