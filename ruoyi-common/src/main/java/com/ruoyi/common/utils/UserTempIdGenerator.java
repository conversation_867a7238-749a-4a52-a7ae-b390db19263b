package com.ruoyi.common.utils;

import java.security.SecureRandom;

public class UserTempIdGenerator {

    private static final String BASE62 = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz";
    private static final int ID_LENGTH = 11;
    private static final SecureRandom secureRandom = new SecureRandom();

    public static String generateId() {
        // 1. 获取当前时间戳（毫秒），再取后几位（减少长度）
        long timeMillis = System.currentTimeMillis();
        String timePart = Long.toString(timeMillis % 1000000000L); // 最多9位

        // 2. 生成 3 位随机数（用来混合时间，增加不重复性）
        String randomPart = String.format("%03d", secureRandom.nextInt(1000)); // 000 - 999

        // 3. 拼接
        String combined = timePart + randomPart;

        // 4. 转为 long，再 Base62 编码
        long number = Long.parseLong(combined);

        StringBuilder sb = new StringBuilder();
        while (number > 0) {
            sb.append(BASE62.charAt((int) (number % 62)));
            number /= 62;
        }

        sb.reverse(); // Base62 编码是倒序的

        // 5. 补全或截断为11位
        while (sb.length() < ID_LENGTH) {
            sb.insert(0, BASE62.charAt(secureRandom.nextInt(62)));
        }

        if (sb.length() > ID_LENGTH) {
            return sb.substring(0, ID_LENGTH); // 截断多余部分
        }

        return sb.toString();
    }

    public static void main(String[] args) {
        for (int i = 0; i < 10; i++) {
            System.out.println(generateId());
        }
    }
}
