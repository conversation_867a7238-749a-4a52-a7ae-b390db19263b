package com.ruoyi.common.utils.mobile;

import javax.servlet.http.HttpServletRequest;

import com.ruoyi.common.constant.Constants;
import com.ruoyi.common.constant.HttpStatus;
import com.ruoyi.common.core.domain.model.mobile.AppLoginUser;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.ServletUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.spring.SpringUtils;

/**
 * APP安全服务工具类
 * 
 * <AUTHOR>
 */
public class AppSecurityUtils
{
    /**
     * 获取APP用户ID
     **/
    public static Long getAppUserId()
    {
        try
        {
            return getAppLoginUser().getUserId();
        }
        catch (Exception e)
        {
            throw new ServiceException("获取APP用户ID异常", HttpStatus.UNAUTHORIZED);
        }
    }

    /**
     * 获取APP用户账户类型
     **/
    public static String getAppAccountType()
    {
        try
        {
            return getAppLoginUser().getAccountType();
        }
        catch (Exception e)
        {
            throw new ServiceException("获取APP用户账户类型异常", HttpStatus.UNAUTHORIZED);
        }
    }

    /**
     * 获取APP用户昵称
     **/
    public static String getAppNickname()
    {
        try
        {
            return getAppLoginUser().getNickname();
        }
        catch (Exception e)
        {
            throw new ServiceException("获取APP用户昵称异常", HttpStatus.UNAUTHORIZED);
        }
    }

    /**
     * 获取APP登录用户
     **/
    public static AppLoginUser getAppLoginUser()
    {
        try
        {
            AppTokenService tokenService = SpringUtils.getBean(AppTokenService.class);
            HttpServletRequest request = ServletUtils.getRequest();
            return tokenService.getLoginUser(request);
        }
        catch (Exception e)
        {
            throw new ServiceException("获取APP用户信息异常", HttpStatus.UNAUTHORIZED);
        }
    }

    /**
     * 获取APP Token
     **/
    public static String getAppToken()
    {
        HttpServletRequest request = ServletUtils.getRequest();
        String token = request.getHeader(Constants.TOKEN);
        if (StringUtils.isNotEmpty(token) && token.startsWith(Constants.TOKEN_PREFIX))
        {
            token = token.replace(Constants.TOKEN_PREFIX, "");
        }
        return token;
    }

    /**
     * 是否为游客用户
     * 
     * @return 结果
     */
    public static boolean isGuest()
    {
        try
        {
            String accountType = getAppAccountType();
            return "GUEST".equals(accountType);
        }
        catch (Exception e)
        {
            return false;
        }
    }

    /**
     * 是否为注册用户
     * 
     * @return 结果
     */
    public static boolean isRegistered()
    {
        try
        {
            String accountType = getAppAccountType();
            return "REGISTERED".equals(accountType);
        }
        catch (Exception e)
        {
            return false;
        }
    }
}