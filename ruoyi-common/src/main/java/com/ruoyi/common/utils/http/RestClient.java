package com.ruoyi.common.utils.http;

import org.springframework.http.*;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

/**
 * <AUTHOR>
 * @Description rest请求
 * @Version 1.0.0
 * @Date 2023/5/3 9:07 AM
 */
public class RestClient {

    private volatile static RestTemplate restTemplate;
    private HttpHeaders headers;
    private MultiValueMap<String, String> params;

    public RestClient() {

        if (restTemplate == null){
            synchronized (RestClient.class){
                if (restTemplate == null){
                    restTemplate = new RestTemplate();
                }
            }
        }

        headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        params = new LinkedMultiValueMap<>();
    }

    /**
     * 构造请求头的ContentType
     * @param mediaType
     */
    public RestClient(MediaType mediaType) {
        headers = new HttpHeaders();
        headers.setContentType(mediaType);
        params = new LinkedMultiValueMap<>();
    }

    public void addHeader(String key, String value) {
        headers.set(key, value);
    }

    public void addParam(String key, String value) {
        params.add(key, value);
    }

    public <T> T request(String url,HttpMethod method,Object request, Class<T> responseType) {
        HttpEntity<?> entity = new HttpEntity<>(request, headers);
        ResponseEntity<T> response = restTemplate.exchange(url, method, entity, responseType, params);
        return response.getBody();
    }

    public <T> T get(String url, Class<T> responseType) {
        HttpEntity<?> entity = new HttpEntity<>(headers);
        ResponseEntity<T> response = restTemplate.exchange(url, HttpMethod.GET, entity, responseType, params);
        return response.getBody();
    }

    public <T> T post(String url, Object request, Class<T> responseType) {
        HttpEntity<?> entity = new HttpEntity<>(request, headers);
        ResponseEntity<T> response = restTemplate.exchange(url, HttpMethod.POST, entity, responseType);
        return response.getBody();
    }

    public <T> T put(String url, Object request, Class<T> responseType) {
        HttpEntity<?> entity = new HttpEntity<>(request, headers);
        ResponseEntity<T> response = restTemplate.exchange(url, HttpMethod.PUT, entity, responseType);
        return response.getBody();
    }

    public <T> T delete(String url, Class<T> responseType) {
        HttpEntity<?> entity = new HttpEntity<>(headers);
        ResponseEntity<T> response = restTemplate.exchange(url, HttpMethod.DELETE, entity, responseType);
        return response.getBody();
    }
}
