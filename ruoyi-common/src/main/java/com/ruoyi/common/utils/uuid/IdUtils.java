package com.ruoyi.common.utils.uuid;

import com.ruoyi.common.utils.spring.SpringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * ID生成器工具类
 * 
 * <AUTHOR>
 */
public class IdUtils
{
    private static final Logger log = LoggerFactory.getLogger(IdUtils.class);

    /**
     * 获取雪花算法生成的ID
     * 
     * @return 雪花算法ID
     */
    public static long snowflakeId()
    {
        try {
            SnowflakeIdWorker worker = SpringUtils.getBean(SnowflakeIdWorker.class);
            return worker.nextId();
        } catch (Exception e) {
            log.error("获取雪花算法ID失败，使用备用方案", e);
            // 备用方案：使用时间戳 + 随机数
            return System.currentTimeMillis() * 1000 + (long)(Math.random() * 1000);
        }
    }

    /**
     * 获取雪花算法监控信息
     * 
     * @return 监控信息
     */
    public static SnowflakeIdWorker.SnowflakeMonitorInfo getSnowflakeMonitorInfo()
    {
        try {
            SnowflakeIdWorker worker = SpringUtils.getBean(SnowflakeIdWorker.class);
            return worker.getMonitorInfo();
        } catch (Exception e) {
            log.error("获取雪花算法监控信息失败", e);
            return null;
        }
    }

    /**
     * 重置雪花算法监控统计
     */
    public static void resetSnowflakeMonitor()
    {
        try {
            SnowflakeIdWorker worker = SpringUtils.getBean(SnowflakeIdWorker.class);
            worker.resetMonitorInfo();
            log.info("雪花算法监控统计已重置");
        } catch (Exception e) {
            log.error("重置雪花算法监控统计失败", e);
        }
    }

    /**
     * 为不同服务创建专用的雪花算法生成器
     * 
     * @param workerId 机器ID
     * @param datacenterId 数据中心ID
     * @return 雪花算法生成器
     */
    public static SnowflakeIdWorker createSnowflakeWorker(long workerId, long datacenterId)
    {
        return new SnowflakeIdWorker(workerId, datacenterId);
    }

    /**
     * 获取随机UUID
     * 
     * @return 随机UUID
     */
    public static String randomUUID()
    {
        return UUID.randomUUID().toString();
    }

    /**
     * 简化的UUID，去掉了横线
     * 
     * @return 简化的UUID，去掉了横线
     */
    public static String simpleUUID()
    {
        return UUID.randomUUID().toString(true);
    }

    /**
     * 获取随机UUID，使用性能更好的ThreadLocalRandom生成UUID
     * 
     * @return 随机UUID
     */
    public static String fastUUID()
    {
        return UUID.fastUUID().toString();
    }

    /**
     * 简化的UUID，去掉了横线，使用性能更好的ThreadLocalRandom生成UUID
     * 
     * @return 简化的UUID，去掉了横线
     */
    public static String fastSimpleUUID()
    {
        return UUID.fastUUID().toString(true);
    }
}
