package com.ruoyi.common.utils;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.utils.sign.Base64;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.nio.charset.StandardCharsets;

/**
 * 第三方JWT Token解析工具类
 * 用于解析Google ID Token和Apple Identity Token
 * 
 * <AUTHOR>
 */
public class ThirdPartyJwtUtils
{
    private static final Logger log = LoggerFactory.getLogger(ThirdPartyJwtUtils.class);
    
    /**
     * 解析JWT Token获取Claims
     * 注意：此方法不验证签名，仅用于提取Claims信息
     * 
     * @param token JWT Token
     * @return Claims JSON对象，解析失败返回null
     */
    public static JSONObject parseTokenClaims(String token)
    {
        if (StringUtils.isEmpty(token))
        {
            log.warn("JWT Token为空");
            return null;
        }
        
        try
        {
            // JWT格式：header.payload.signature
            String[] parts = token.split("\\.");
            if (parts.length != 3)
            {
                log.error("JWT Token格式无效，部分数量: {}", parts.length);
                return null;
            }
            
            // 解码payload部分（Base64URL编码）
            String payload = parts[1];
            
            // 处理Base64URL编码（可能缺少填充）
            String normalizedPayload = normalizeBase64Url(payload);
            
            // Base64解码
            byte[] decodedBytes = Base64.decode(normalizedPayload);
            if (decodedBytes == null)
            {
                log.error("JWT Token payload Base64解码失败");
                return null;
            }
            
            String decodedPayload = new String(decodedBytes, StandardCharsets.UTF_8);
            
            // 解析JSON
            JSONObject claims = JSON.parseObject(decodedPayload);
            
            log.debug("JWT Token解析成功，claims keys: {}", claims.keySet());
            return claims;
        }
        catch (Exception e)
        {
            log.error("解析JWT Token异常: {}", e.getMessage(), e);
            return null;
        }
    }
    
    /**
     * 从JWT Token中提取用户唯一标识（sub字段）
     * 
     * @param token JWT Token
     * @return 用户唯一标识，提取失败返回null
     */
    public static String extractSubject(String token)
    {
        JSONObject claims = parseTokenClaims(token);
        if (claims == null)
        {
            return null;
        }
        
        String subject = claims.getString("sub");
        if (StringUtils.isEmpty(subject))
        {
            log.warn("JWT Token中未找到sub字段");
            return null;
        }
        
        return subject;
    }
    
    /**
     * 从JWT Token中提取邮箱
     * 
     * @param token JWT Token
     * @return 邮箱地址，提取失败返回null
     */
    public static String extractEmail(String token)
    {
        JSONObject claims = parseTokenClaims(token);
        if (claims == null)
        {
            return null;
        }
        
        return claims.getString("email");
    }
    
    /**
     * 从JWT Token中提取用户名
     * 
     * @param token JWT Token
     * @return 用户名，提取失败返回null
     */
    public static String extractName(String token)
    {
        JSONObject claims = parseTokenClaims(token);
        if (claims == null)
        {
            return null;
        }
        
        return claims.getString("name");
    }
    
    /**
     * 验证JWT Token是否过期
     * 
     * @param token JWT Token
     * @return true-已过期，false-未过期或无过期时间
     */
    public static boolean isTokenExpired(String token)
    {
        JSONObject claims = parseTokenClaims(token);
        if (claims == null)
        {
            return true; // 解析失败视为过期
        }
        
        Long exp = claims.getLong("exp");
        if (exp == null)
        {
            return false; // 无过期时间
        }
        
        // exp是秒级时间戳
        long currentTime = System.currentTimeMillis() / 1000;
        return currentTime > exp;
    }
    
    /**
     * 验证JWT Token的颁发者
     * 
     * @param token JWT Token
     * @param expectedIssuer 期望的颁发者
     * @return true-颁发者匹配，false-不匹配
     */
    public static boolean validateIssuer(String token, String expectedIssuer)
    {
        JSONObject claims = parseTokenClaims(token);
        if (claims == null)
        {
            return false;
        }
        
        String issuer = claims.getString("iss");
        return expectedIssuer.equals(issuer);
    }
    
    /**
     * 验证JWT Token的受众
     * 
     * @param token JWT Token
     * @param expectedAudience 期望的受众
     * @return true-受众匹配，false-不匹配
     */
    public static boolean validateAudience(String token, String expectedAudience)
    {
        JSONObject claims = parseTokenClaims(token);
        if (claims == null)
        {
            return false;
        }
        
        String audience = claims.getString("aud");
        return expectedAudience.equals(audience);
    }
    
    /**
     * 规范化Base64URL编码字符串
     * Base64URL编码可能缺少填充字符，需要补齐
     *
     * @param base64Url Base64URL编码字符串
     * @return 规范化的Base64编码字符串
     */
    private static String normalizeBase64Url(String base64Url)
    {
        // 将Base64URL字符替换为Base64字符
        String base64 = base64Url.replace('-', '+').replace('_', '/');

        // 补齐填充字符
        int padding = 4 - (base64.length() % 4);
        if (padding != 4)
        {
            // 使用StringBuilder来构建填充字符，兼容Java 8
            StringBuilder sb = new StringBuilder(base64);
            for (int i = 0; i < padding; i++)
            {
                sb.append('=');
            }
            base64 = sb.toString();
        }

        return base64;
    }
    
    /**
     * 获取Google ID Token的预期颁发者
     * 
     * @return Google颁发者URL
     */
    public static String getGoogleIssuer()
    {
        return "https://accounts.google.com";
    }
    
    /**
     * 获取Apple Identity Token的预期颁发者
     * 
     * @return Apple颁发者URL
     */
    public static String getAppleIssuer()
    {
        return "https://appleid.apple.com";
    }
}
