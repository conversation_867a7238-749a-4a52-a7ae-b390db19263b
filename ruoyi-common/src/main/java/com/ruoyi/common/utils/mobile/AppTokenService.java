package com.ruoyi.common.utils.mobile;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import javax.servlet.http.HttpServletRequest;

import com.ruoyi.common.exception.ServiceException;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import com.ruoyi.common.constant.CacheConstants;
import com.ruoyi.common.constant.Constants;
import com.ruoyi.common.core.domain.model.mobile.AppLoginUser;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.utils.ServletUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.ip.AddressUtils;
import com.ruoyi.common.utils.ip.IpUtils;
import eu.bitwalker.useragentutils.UserAgent;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;

/**
 * APP token验证处理
 *
 * <AUTHOR>
 */
@Component
public class AppTokenService {
    private static final Logger log = LoggerFactory.getLogger(AppTokenService.class);

    // 令牌自定义标识
    @Value("${token.header}")
    private String header;

    // 令牌秘钥
    @Value("${token.secret}")
    private String secret;

    // 令牌有效期（默认30分钟）
    @Value("${token.expireTime}")
    private int expireTime;

    // 令牌有效期（默认30分钟）
    @Value("${token.refreshExpireTime}")
    private int refreshExpireTime;

    protected static final long MILLIS_SECOND = 1000;

    protected static final long MILLIS_MINUTE = 60 * MILLIS_SECOND;

    @Autowired
    private RedisCache redisCache;

    /**
     * 获取APP用户身份信息
     *
     * @return APP用户信息
     */
    public AppLoginUser getLoginUser(HttpServletRequest request) {
        // 获取请求携带的令牌
        String token = getToken(request);
        if (StringUtils.isNotEmpty(token)) {
            try {
                Claims claims = parseToken(token);

                // 验证token的基本信息
                if (claims == null) {
                    log.warn("Token解析失败，claims为空");
                    return null;
                }

                // 解析对应的APP用户信息
                String userId = ((Long) claims.get("userId"))+"";
                String deviceId = (String) claims.get("deviceId");
                if (StringUtils.isEmpty(userId) || StringUtils.isEmpty(deviceId)) {
                    log.warn("Token中缺少用户标识");
                    return null;
                }

                String userKey = getTokenKey(userId, deviceId);
                AppLoginUser appUser = redisCache.getCacheObject(userKey);

                // 验证用户信息有效性
                if (appUser == null) {
                    log.warn("用户信息不存在或已过期，userId: {}, deviceId: {}", userId, deviceId);
                    return null;
                }

                // 验证token是否匹配
                if (!token.equals(appUser.getToken())) {
                    log.warn("Token不匹配，可能被篡改");
                    return null;
                }

                return appUser;
            } catch (Exception e) {
                log.error("获取APP用户信息异常'{}'", e.getMessage());
            }
        }
        return null;
    }

    /**
     * 删除APP用户身份信息
     */
    public void delLoginUser(String token) {
        if (StringUtils.isEmpty(token)) {
            throw new RuntimeException("token不能为空");
        }
        // 从JWT token中解析出UUID
        Claims claims = parseToken(token);
        // 解析对应的APP用户信息
        String userId = ((Long) claims.get("userId"))+"";
        String deviceId = (String) claims.get("deviceId");
        if (StringUtils.isEmpty(userId) || StringUtils.isEmpty(deviceId)) {
            throw new RuntimeException("Token中缺少用户标识");
        }
        delLoginUser(userId, deviceId);

    }

    public void delToken(String userId, String deviceId) {
        if (StringUtils.isEmpty(userId) || StringUtils.isEmpty(deviceId)) {
            throw new RuntimeException("userId或deviceId不能为空");
        }
        String userKey = getTokenKey(userId, deviceId);
        AppLoginUser appUser = redisCache.getCacheObject(userKey);
        if (appUser == null) {
            return;
        }
        redisCache.deleteObject(userKey);
    }

    /**
     * 删除APP用户身份信息
     */
    public void delLoginUser(String userId, String deviceId) {
        delToken(userId, deviceId);
    }



    /**
     * 创建令牌
     *
     * @param appLoginUser APP用户信息
     * @return 令牌
     */
    public TokenVo createToken(AppLoginUser appLoginUser, boolean isTop) {
        Long userId = appLoginUser.getUserId();
        String deviceId = appLoginUser.getDeviceId();
        delLoginUser(userId+"",deviceId);

        setUserAgent(appLoginUser);

        Map<String, Object> claims = new HashMap<>();
        claims.put("userId", userId);
        claims.put("accountType", appLoginUser.getAccountType());
        claims.put("deviceId", deviceId);
        // 生成完整的JWT token
        String accessToken = createToken(claims, Constants.ACCESS_TOKEN_KEY, new Date(System.currentTimeMillis() + expireTime * MILLIS_MINUTE));
        // 将完整的JWT token存储到用户对象中
        appLoginUser.setToken(accessToken);
        String refreshToken = createToken(claims, Constants.REFRESH_TOKEN_KEY, new Date(System.currentTimeMillis() + refreshExpireTime * MILLIS_MINUTE));
        appLoginUser.setRefreshToken(refreshToken);
        // 存储用户信息到redis缓存
        redisCache.setCacheObject(getTokenKey(appLoginUser.getUserId() + "", appLoginUser.getDeviceId()), appLoginUser, expireTime, TimeUnit.MINUTES);
        return new TokenVo(accessToken, refreshToken,isTop);
    }


    /**
     * 设置用户代理信息
     *
     * @param appLoginUser APP登录信息
     */
    public void setUserAgent(AppLoginUser appLoginUser) {
        UserAgent userAgent = UserAgent.parseUserAgentString(ServletUtils.getRequest().getHeader("User-Agent"));
        String ip = IpUtils.getIpAddr();
        appLoginUser.setIpaddr(ip);
        appLoginUser.setLoginLocation(AddressUtils.getRealAddressByIP(ip));
        appLoginUser.setBrowser(userAgent.getBrowser().getName());
        appLoginUser.setOs(userAgent.getOperatingSystem().getName());
    }

    /**
     * 从数据声明生成令牌
     *
     * @param claims    数据声明
     * @param tokenType token类型
     * @return
     */
    private String createToken(Map<String, Object> claims, String tokenType, Date expirationTime) {

        claims.put("tokenType", tokenType);
        return Jwts.builder()
                .setClaims(claims)
                .setExpiration(expirationTime)
                .signWith(SignatureAlgorithm.HS512, secret).compact();
    }

    /**
     * 从令牌中获取数据声明
     *
     * @param token 令牌
     * @return 数据声明
     */
    private Claims parseToken(String token) {
        return Jwts.parser()
                .setSigningKey(secret)
                .parseClaimsJws(token)
                .getBody();
    }

    /**
     * 从令牌中获取用户ID
     *
     * @param token 令牌
     * @return 用户ID
     */
    public Long getUserIdFromToken(String token) {
        Claims claims = parseToken(token);
        return (Long) claims.get("userId");
    }

    /**
     * 从令牌中获取token类型
     *
     * @param token 令牌
     * @return token类型
     */
    public String getTokenTypeFromToken(String token) {
        try {
            Claims claims = parseToken(token);
            return claims.get("tokenType").toString();
        } catch (Exception e) {
            throw new ServiceException("token解析失败");
        }
    }

    /**
     * 从令牌中获取设备ID
     *
     * @param token 令牌
     * @return 设备ID
     */
    public String getDeviceIdFromToken(String token) {
        Claims claims = parseToken(token);
        return claims.get("deviceId").toString();
    }

    /**
     * 获取请求token
     *
     * @param request
     * @return token
     */
    private String getToken(HttpServletRequest request) {
        String token = request.getHeader(header);
        if (StringUtils.isNotEmpty(token) && token.startsWith(Constants.TOKEN_PREFIX)) {
            token = token.replace(Constants.TOKEN_PREFIX, "");
        }
        return token;
    }

    private String getTokenKey(String userId, String deviceId) {
        return CacheConstants.LOGIN_TOKEN_KEY + userId + deviceId;
    }


    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class TokenVo {
        private String accessToken;
        private String refreshToken;
        private boolean isTop;
    }
}