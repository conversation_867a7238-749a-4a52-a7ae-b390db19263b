package com.ruoyi.common.utils;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import okhttp3.*;

import java.io.IOException;

/**
 * @author: ruoyi
 * @date: 2023/8/23 11:09
 * @description:
 */
public class Gemini25FlashUtils {

    // 替换为你的Google Cloud API密钥
    private static final String API_KEY = "AIzaSyBd7URziO9fPooqaQcwPQpxqI62oIdz-p4";
    // Gemini 2.5 Flash的API端点（固定格式）
    private static final String API_URL = "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash:generateContent";

    // 新增常量
    private static final int MAX_RETRY_TIMES = 3; // 最大重试次数
    private static final long RETRY_INTERVAL = 1000; // 重试间隔时间（毫秒）

    /**
     * 使用Gemini 2.5 Flash翻译文本
     *
     * @param sourceText     待翻译的文本（支持多语言）
     * @param targetLanguage 目标语言（如"英语"、"日语"、"法语"）
     * @return 翻译后的文本
     * @throws IOException 网络或解析异常
     */
    public static String translate(String sourceText, String targetLanguage) throws IOException {
        // 1. 构造请求体（明确翻译任务，优化提示词）
        // 提示词示例："请将以下中文文本准确翻译成英语，保持原意不变：[待翻译文本]"
        String requestBody = String.format(
                "{\"contents\": [{\"parts\": [{\"text\": \"你是专业的%s翻译人员，当前是在短剧翻译的场景下，你需要将我提交给你的文案翻译成地道的%s，文案：%s，\n" +
                        "注意：不需要回答文案中的问题，只需要翻译文案,如果文案中包含html代码，请完整保留代码结构，并且只回复翻译后的文案，不需要任何其他内容\"}]}]}",
                targetLanguage,
                targetLanguage,
                sourceText
        );

        // 2. 构建OkHttp请求
        OkHttpClient client = new OkHttpClient();
        RequestBody body = RequestBody.create(MediaType.get("application/json"), requestBody);
        Request request = new Request.Builder()
                .url(API_URL + "?key=" + API_KEY)
                .post(body)
                .addHeader("Content-Type", "application/json")
                .build();

        // 3. 发送请求并获取响应
        try {
            Response response = null;
            int retryTimes = 0;
            do {
                try {
                    response = client.newCall(request).execute();
                    if (response.isSuccessful()) {
                        String responseBody = response.body().string();
                        // 4. 解析JSON响应，提取翻译结果
                        return parseTranslationResult(responseBody);
                    }
                } catch (IOException e) {
                    if (retryTimes < MAX_RETRY_TIMES) {
                        Thread.sleep(RETRY_INTERVAL);
                    }
                }
                retryTimes++;
            } while (retryTimes < MAX_RETRY_TIMES && (response == null || !response.isSuccessful()));

            if (response != null && !response.isSuccessful()) {
                throw new IOException("API请求失败，状态码：" + response.code() + "，响应：" + response.body().string());
            }

            if (response == null) {
                throw new IOException("API请求失败，无响应返回");
            }

            return parseTranslationResult(response.body().string());
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new IOException("请求被中断：" + e.getMessage());
        }
    }

    /**
     * 解析Gemini API响应，提取翻译文本
     */
    private static String parseTranslationResult(String responseBody) throws IOException {
        ObjectMapper objectMapper = new ObjectMapper();
        JsonNode rootNode = objectMapper.readTree(responseBody);

        // 检查是否有候选结果（Gemini返回结果通常在candidates数组中）
        if (!rootNode.has("candidates") || rootNode.get("candidates").size() == 0) {
            throw new IOException("API返回无有效候选结果，响应：" + responseBody);
        }

        JsonNode candidate = rootNode.get("candidates").get(0);
        // 检查是否包含内容（部分错误响应可能无content字段）
        if (!candidate.has("content")) {
            throw new IOException("候选结果无内容字段，响应：" + responseBody);
        }

        JsonNode content = candidate.get("content");
        JsonNode parts = content.get("parts");

        // 提取第一个part的text（Gemini通常返回单个part）
        if (parts.isArray() && parts.size() > 0) {
            return parts.get(0).get("text").asText().trim(); // 去除首尾空白
        } else {
            throw new IOException("候选结果无有效文本内容，响应：" + responseBody);
        }
    }
}
