package com.ruoyi.common.utils;

import com.alibaba.fastjson2.JSONObject;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.Base64;

public class SocialLoginVerifier {

    public enum Provider {
        GOOGLE, FACEBOOK, APPLE
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class SocialUserInfo {
        private boolean success;
        private String id;
        private String email;
        private String name;

        @Override
        public String toString() {
            return String.format("ID: %s\nEmail: %s\nName: %s", id, email, name);
        }


    }

    /**
     * 主入口：根据平台类型 + token 获取用户信息（access_token 或 id_token）
     */
    public static SocialUserInfo getUserInfo(Provider provider, String token) throws Exception {
        switch (provider) {
            case GOOGLE:
                return verifyGoogleToken(token);
            case FACEBOOK:
                return verifyFacebookToken(token);
            case APPLE:
                return verifyAppleToken(token);
            default:
                throw new IllegalArgumentException("Unsupported provider");
        }
    }

    // ---------------- Google ----------------
    private static SocialUserInfo verifyGoogleToken(String idToken) throws Exception {
        String url = "https://oauth2.googleapis.com/tokeninfo?id_token=" + idToken;
        String response = sendGet(url);

        JSONObject json = JSONObject.parse(response);

        SocialUserInfo info = new SocialUserInfo();
        info.setSuccess(json.getString("error") == null);
        info.setId(json.getString("sub"));
        info.setEmail(json.getString("email"));
        info.setName(json.getString("name")); // name 可能为 null
        return info;
    }

    // ---------------- Facebook ----------------
    private static SocialUserInfo verifyFacebookToken(String accessToken) throws Exception {
        String url = "https://graph.facebook.com/me?fields=id,name,email&access_token=" + accessToken;
        String response = sendGet(url);

        JSONObject json = JSONObject.parse(response);
        SocialUserInfo info = new SocialUserInfo();
        info.id = json.getString("id");
        info.name = json.getString("name");
        info.email = json.getString("email");
        return info;
    }

    // ---------------- Apple ----------------
    private static SocialUserInfo verifyAppleToken(String idToken) throws Exception {
        // Apple 不提供验证接口，只能自己解 JWT（未验证签名，仅解码）
        String[] parts = idToken.split("\\.");
        if (parts.length < 2) {
            throw new IllegalArgumentException("Invalid Apple ID token format");
        }

        String payloadJson = new String(Base64.getUrlDecoder().decode(parts[1]), "UTF-8");
        JSONObject payload = JSONObject.parse(payloadJson);

        SocialUserInfo info = new SocialUserInfo();
        info.id = payload.getString("sub");
        info.email = payload.getString("email");
        info.name = info.email; // Apple 不返回用户姓名
        return info;
    }

    // ---------------- HTTP 工具 ----------------
    private static String sendGet(String urlString) throws Exception {
        URL url = new URL(urlString);
        HttpURLConnection conn = (HttpURLConnection) url.openConnection();

        conn.setRequestMethod("GET");
        int status = conn.getResponseCode();

        BufferedReader in = new BufferedReader(new InputStreamReader(
                status >= 200 && status < 300 ? conn.getInputStream() : conn.getErrorStream(), "UTF-8"
        ));

        String inputLine;
        StringBuilder response = new StringBuilder();
        while ((inputLine = in.readLine()) != null) {
            response.append(inputLine);
        }

        in.close();
        return response.toString();
    }
}