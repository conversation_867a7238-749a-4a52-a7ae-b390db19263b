package com.ruoyi.common.core.domain.model;

import com.ruoyi.common.core.domain.entity.AppUserDevice;
import com.ruoyi.common.exception.ServiceException;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class AppLoginInfo {

    private String deviceId;

    private String deviceManufacturer;

    private String devicePlatformType;

    private String deviceSystemVersion;

    public void initDeviceInfo(AppUserDevice device, Long userId, String deviceId) {
        if (device == null || userId == null  || deviceId == null) {
            throw new ServiceException("设备信息或用户信息不能为空");
        }
        device.init(userId,deviceId, deviceManufacturer, devicePlatformType,deviceSystemVersion);
    }
}
