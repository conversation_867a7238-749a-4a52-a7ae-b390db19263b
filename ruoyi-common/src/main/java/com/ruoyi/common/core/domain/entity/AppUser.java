package com.ruoyi.common.core.domain.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import com.ruoyi.common.utils.ServletUtils;
import com.ruoyi.common.utils.UserTempIdGenerator;
import com.ruoyi.common.utils.ip.IpUtils;
import com.ruoyi.common.utils.uuid.IdUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;

/**
 * app_user表实体
 * 
 * <AUTHOR>
 */
@Setter
@Getter
@ApiModel(description = "APP用户实体")
public class AppUser extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 用户ID */
    @ApiModelProperty(value = "用户ID", example = "1001")
    @Excel(name = "用户ID", type = Excel.Type.EXPORT, cellType = Excel.ColumnType.NUMERIC, prompt = "用户编号")
    private Long id;

    /** 用户展示ID */
    @ApiModelProperty(value = "用户展示ID", example = "U123456")
    @Excel(name = "用户展示ID")
    private String userTempId;

    /** 用户昵称 */
    @ApiModelProperty(value = "用户昵称", example = "张三")
    @Excel(name = "用户昵称")
    private String nickname;

    /** 用户头像URL */
    @ApiModelProperty(value = "用户头像URL", example = "http://example.com/avatar.jpg")
    private String avatar;

    /** 用户邮箱 */
    @ApiModelProperty(value = "用户邮箱", example = "<EMAIL>")
    @Excel(name = "用户邮箱")
    private String email;

    /** 账户类型（GUEST=游客，THIRD_PARTY=第三方账户类型） */
    @ApiModelProperty(value = "账户类型", allowableValues = "GUEST,THIRD_PARTY", notes = "GUEST=游客，THIRD_PARTY=第三方账户类型")
    @Excel(name = "账户类型", readConverterExp = "GUEST=游客,THIRD_PARTY=第三方账户")
    private String accountType;

    /** 账户状态（0=正常，1=停用） */
    @ApiModelProperty(value = "账户状态",allowableValues = "0,1", notes = "0=正常，1=停用")
    @Excel(name = "账户状态", readConverterExp = "0=正常,1=停用")
    private String status;

    /** 认证类型（GOOGLE | APPLE | FACEBOOK） */
    @ApiModelProperty(value = "认证类型",allowableValues = "GOOGLE,APPLE,FACEBOOK")
    @Excel(name = "认证类型")
    private String authType;

    /** 三方平台ID */
    @ApiModelProperty(value = "三方平台ID", example = "google_12345")
    @Excel(name = "三方平台ID")
    private String platformOpenId;

    /** 认证凭证（密码、token等，加密存储） */
    @ApiModelProperty(value = "认证凭证", example = "encrypted_credential", notes = "密码、token等，加密存储")
    @Excel(name = "认证凭证")
    private String credential;

    /** 最后登录IP地址 */
    @ApiModelProperty(value = "最后登录IP地址", example = "***********")
    @Excel(name = "最后登录IP")
    private String lastLoginIp;

    /** 最后登录时间 */
    @ApiModelProperty(value = "最后登录时间", example = "2024-01-01 12:00:00")
    @Excel(name = "最后登录时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date lastLoginDate;

    /**
     * 首次登录的设备ID
     */
    @ApiModelProperty(value = "首次登录的设备ID", example = "device_123456")
    private String firstDeviceId;

    /**
     * 是否为付费用户 TODO
     */
    @ApiModelProperty(value = "是否为付费用户", example = "true")
    private Boolean isPaid;


    /**
     * 用户关联深链ID
     */
    @ApiModelProperty(value = "用户关联深链ID", example = "123456")
    private Long semLinkId;

    /**
     * 用户来源
     */
    @ApiModelProperty(value = "用户来源", example = "APP")
    private String source;

    /**
     * 像素ID
     */
    @ApiModelProperty(value = "像素ID", example = "123456")
    private String pixelId;

    /**
     * 广告ID
     */
    @ApiModelProperty(value = "广告ID", example = "123456")
    private String adId;

    /**
     * 广告信息
     */
    @ApiModelProperty(value = "广告信息", example = "广告信息")
    private String adInfo;






    /**
     * 初始化用户信息
     * @param firstDeviceId  首次登录的设备ID
     * @param accountType 账户类型 GUEST、GOOGLE、APPLE
     */
    public void init(String firstDeviceId, String accountType) {
        this.id = IdUtils.snowflakeId();
        this.userTempId = UserTempIdGenerator.generateId();
        this.accountType = accountType;
        this.status = "0";
        this.firstDeviceId = firstDeviceId;
        this.lastLoginDate = new Date();
        HttpServletRequest request = ServletUtils.getRequest();
        this.lastLoginIp = IpUtils.getIpAddr(request);
    }


    /**
     * 校验设备ID格式
     */
    public boolean hasValidDeviceId() {
        if (firstDeviceId == null || firstDeviceId.isEmpty()) {
            return false;
        }
        // 简单的设备ID格式校验：只允许字母、数字、短横线和下划线
        return firstDeviceId.matches("^[a-zA-Z0-9_-]+$");
    }

    public boolean hasEnabled(){
        return "0".equals(status);
    }

}