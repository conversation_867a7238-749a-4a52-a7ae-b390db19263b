package com.ruoyi.common.core.mapper;

import com.ruoyi.common.core.domain.entity.AppUser;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * APP用户数据访问层
 * 
 * <AUTHOR>
 */
@Mapper
public interface AppUserMapper
{
    /**
     * 根据设备ID查询用户信息
     * 通过设备绑定关系查找对应的用户
     * 
     * @param deviceId 设备唯一标识
     * @return 用户信息，如果未找到返回null
     */
    AppUser selectAppUserByDeviceId(@Param("deviceId") String deviceId);

    /**
     * 根据用户ID查询用户信息
     * 
     * @param userId 用户主键ID
     * @return 用户信息，如果未找到返回null
     */
    AppUser selectAppUserByUserId(Long userId);

    /**
     * 根据条件查询用户列表
     * 支持多条件组合查询，用于分页列表展示
     * 
     * @param appUser 查询条件对象（支持昵称、账户类型、状态等条件）
     * @return 符合条件的用户列表
     */
    List<AppUser> selectAppUserList(AppUser appUser);

    /**
     * 新增用户信息
     * 
     * @param appUser 用户信息对象
     * @return 影响行数，成功返回1
     */
    int insertAppUser(AppUser appUser);

    /**
     * 修改用户信息
     * 支持部分字段更新，自动更新修改时间
     * 
     * @param appUser 用户信息对象（必须包含userId）
     * @return 影响行数，成功返回1
     */
    int updateAppUser(AppUser appUser);

    /**
     * 删除用户信息（物理删除）
     * 
     * @param userId 用户主键ID
     * @return 影响行数，成功返回1
     */
    int deleteAppUserByUserId(Long userId);

    /**
     * 批量删除用户信息（物理删除）
     * 
     * @param userIds 用户主键ID数组
     * @return 影响行数
     */
    int deleteAppUserByUserIds(Long[] userIds);

    /**
     * 根据邮箱查询用户信息
     * 用于邮箱唯一性校验和邮箱登录
     * 
     * @param email 邮箱地址
     * @return 用户信息，如果未找到返回null
     */
    AppUser selectAppUserByEmail(@Param("email") String email);

    /**
     * 根据手机号查询用户信息
     * 用于手机号唯一性校验和手机号登录
     * 
     * @param phoneNumber 手机号码
     * @return 用户信息，如果未找到返回null
     */
    AppUser selectAppUserByPhoneNumber(@Param("phoneNumber") String phoneNumber);

    /**
     * 校验邮箱是否已存在
     * 
     * @param email 邮箱地址
     * @param userId 排除的用户ID（修改时使用，新增时传null）
     * @return 存在相同邮箱的用户数量
     */
    int checkEmailUnique(@Param("email") String email, @Param("userId") Long userId);

    /**
     * 校验手机号是否已存在
     * 
     * @param phoneNumber 手机号码
     * @param userId 排除的用户ID（修改时使用，新增时传null）
     * @return 存在相同手机号的用户数量
     */
    int checkPhoneNumberUnique(@Param("phoneNumber") String phoneNumber, @Param("userId") Long userId);

    /**
     * 统计用户总数
     * 可按条件统计，用于数据分析
     * 
     * @param appUser 统计条件（可为null表示统计全部）
     * @return 用户总数
     */
    int countAppUsers(AppUser appUser);

    /**
     * 逻辑删除用户（软删除）
     * 将deleted字段设置为1，保留数据记录
     * 
     * @param userId 用户主键ID
     * @return 影响行数，成功返回1
     */
    int softDeleteAppUserByUserId(Long userId);

    /**
     * 批量逻辑删除用户（软删除）
     * 
     * @param userIds 用户主键ID数组
     * @return 影响行数
     */
    int softDeleteAppUserByUserIds(Long[] userIds);

    AppUser selectByPlatformOpenId(@Param("authType") String authType, @Param("platformOpenId") String platformOpenId);

    AppUser selectAppUserByFirstDeviceId(@Param("firstDeviceId") String firstDeviceId);
}