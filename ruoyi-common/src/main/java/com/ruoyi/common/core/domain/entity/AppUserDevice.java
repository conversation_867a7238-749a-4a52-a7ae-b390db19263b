package com.ruoyi.common.core.domain.entity;

import com.ruoyi.common.core.domain.BaseEntity;
import com.ruoyi.common.utils.uuid.IdUtils;
import lombok.Getter;
import lombok.Setter;

/**
 * 用户设备关联表 (app_user_device)
 * 
 * <AUTHOR>
 */
@Setter
@Getter
public class AppUserDevice extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 设备关联ID */
    private Long id;

    /** 用户ID，关联app_user表 */
    private Long userId;

    /** 设备ID */
    private String deviceId;

    /** 设备厂商 */
    private String deviceManufacturer;

    /** 设备平台类型（android=安卓，ios=苹果，web=网页等） */
    private String devicePlatformType;

    /** 设备系统版本 **/
    private String deviceSystemVersion;

    public void init(Long userId, String deviceId, String deviceManufacturer, String devicePlatformType, String deviceSystemVersion) {
        this.id = IdUtils.snowflakeId();
        this.userId = userId;
        this.deviceId = deviceId;
        this.deviceManufacturer = deviceManufacturer;
        this.devicePlatformType = devicePlatformType;
        this.deviceSystemVersion = deviceSystemVersion;
    }
}
