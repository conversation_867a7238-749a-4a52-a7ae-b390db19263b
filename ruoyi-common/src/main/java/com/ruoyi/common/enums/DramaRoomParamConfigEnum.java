package com.ruoyi.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 短剧参数配置枚举
 *
 * <AUTHOR>
 */
@AllArgsConstructor
@Getter
public enum DramaRoomParamConfigEnum {

    /**
     * 短剧筛选配置-配置的可供筛选使用的分类
     */
    APP_CATEGORY_CONFIG("app_category_config", "短剧筛选配置-配置的可供筛选使用的分类"),

    /**
     * 推荐电影配置-配置的某一个分类
     */
    APP_RECOMMEND_MOVIE_CONFIG("app_recommend_movie_config", "推荐电影配置-配置的某一个分类"),

    /**
     * app首页配置-配置的分类id
     */
    APP_HOME_CONFIG("app_home_config", "app首页配置-配置的分类id");

    private final String code;
    private final String desc;
}
