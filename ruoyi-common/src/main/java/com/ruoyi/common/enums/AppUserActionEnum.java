package com.ruoyi.common.enums;


/**
 * app用户行为枚举
 *
 * <AUTHOR>
 */
public enum AppUserActionEnum {

    USER_BROWSE_PAGE("浏览页面"),
    USER_WATCH_EPISODE("观看集数"),
    USER_WATCH_MOVIE("观看的剧"),
    USER_JOIN_ACTIVITY("参与的活动"),
    USER_FEEDBACK("像素回传"),

    MOVIE_WATCH_TIME("观看时长"),

    USER_OPEN_PAY("用户打开支付"),

    USER_OPEN_UNPAY("用户打开未支付"),
    PLAY("播放"),

    COLLECT("收藏"),

    LIKE("点赞"),

    SEARCH("搜索");

    private String desc;

    AppUserActionEnum(String desc) {
        this.desc = desc;
    }

    public String getDesc() {
        return desc;
    }
}
