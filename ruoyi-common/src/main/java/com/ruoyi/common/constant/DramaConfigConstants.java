package com.ruoyi.common.constant;

/**
 * 短剧业务常量信息
 * 
 * <AUTHOR>
 */
public class DramaConfigConstants
{

    /**
     * 轮播图key
     */
    private final static String SLIDER_IMAGE = "slider_image";

    /**
     *热播剧
     */
    private final static String HOT_MOVIE = "hot_movie";


    private final static  String AREA_MOVIE = "area_movie";


    private final static String APP_LANGUAGE = "app_language";
    /**
     * 雪花算法机器ID配置
     */
    public static final class SnowflakeConfig
    {
        /** APP服务机器ID */
        public static final long APP_SERVICE_WORKER_ID = 1L;
        
        /** 运营中心机器ID */  
        public static final long ADMIN_SERVICE_WORKER_ID = 2L;
        
        /** 数据分析服务机器ID */
        public static final long ANALYSIS_SERVICE_WORKER_ID = 3L;
        
        /** 片源中心服务机器ID */
        public static final long CONTENT_SERVICE_WORKER_ID = 4L;
        
        /** 数据中心ID（可按机房/地区分配） */
        public static final long DATACENTER_ID = 1L;
    }

    /**
     * 用户相关常量
     */
    public static final class UserConstants
    {
        /** 游客账户类型 */
        public static final String ACCOUNT_TYPE_GUEST = "GUEST";
        
        /** 第三方账户类型 */
        public static final String ACCOUNT_TYPE_THIRD_PARTY = "THIRD_PARTY";
    }

    /**
     * 错误消息常量
     */
    public static final String DEVICE_ID_EMPTY = "设备ID不能为空";
    public static final String THIRD_PARTY_LOGIN_PARAM_EMPTY = "第三方登录参数不能为空";
    public static final String USER_CREATE_FAILED = "用户创建失败";
    public static final String USER_DISABLED = "用户已被禁用";
}
