package com.ruoyi.common.constant;

/**
 * 第三方登录相关常量
 * 
 * <AUTHOR>
 */
public class ThirdPartyConstants
{
    /** 支持的第三方提供商 */
    public static final String PROVIDER_GOOGLE = "GOOGLE";
    public static final String PROVIDER_APPLE = "APPLE";
    public static final String PROVIDER_FACEBOOK = "FACEBOOK";
    public static final String PROVIDER_WECHAT = "WECHAT";
    public static final String PROVIDER_QQ = "QQ";
    
    /** 认证类型 */
    public static final String AUTH_TYPE_EMAIL = "EMAIL";
    public static final String AUTH_TYPE_PHONE = "PHONE";
    public static final String AUTH_TYPE_GOOGLE = "GOOGLE";
    public static final String AUTH_TYPE_APPLE = "APPLE";
    public static final String AUTH_TYPE_FACEBOOK = "FACEBOOK";
    public static final String AUTH_TYPE_WECHAT = "WECHAT";
    public static final String AUTH_TYPE_QQ = "QQ";
    
    /** 账户类型 */
    public static final String ACCOUNT_TYPE_GUEST = "GUEST";
    public static final String ACCOUNT_TYPE_REGISTERED = "REGISTERED";
    public static final String ACCOUNT_TYPE_THIRD_PARTY = "THIRD_PARTY";
    
    /** 性别常量 */
    public static final String GENDER_MALE = "0";
    public static final String GENDER_FEMALE = "1";
    public static final String GENDER_UNKNOWN = "2";
    
    /** 用户状态 */
    public static final String USER_STATUS_NORMAL = "0";
    public static final String USER_STATUS_DISABLED = "1";
    
    /** 删除标志 */
    public static final String DELETE_FLAG_NORMAL = "0";
    public static final String DELETE_FLAG_DELETED = "1";
    
    /** 设备状态 */
    public static final String DEVICE_STATUS_BOUND = "0";
    public static final String DEVICE_STATUS_UNBOUND = "1";
    
    /** 设备类型 */
    public static final String DEVICE_TYPE_ANDROID = "ANDROID";
    public static final String DEVICE_TYPE_IOS = "IOS";
    public static final String DEVICE_TYPE_WEB = "WEB";
    public static final String DEVICE_TYPE_UNKNOWN = "UNKNOWN";
    
    /** 默认配置值 */
    public static final int DEFAULT_TIMEOUT = 10000;
    public static final int DEFAULT_RETRY = 3;
    public static final String DEFAULT_LANGUAGE = "en";
    
    /** 第三方API URL常量 */
    public static final String GOOGLE_USERINFO_URL = "https://www.googleapis.com/oauth2/v2/userinfo";
    public static final String GOOGLE_TOKEN_INFO_URL = "https://oauth2.googleapis.com/tokeninfo";
    public static final String APPLE_KEYS_URL = "https://appleid.apple.com/auth/keys";
    public static final String APPLE_ISSUER = "https://appleid.apple.com";
    
    /** 错误信息常量 */
    public static final String ERROR_PROVIDER_NOT_SUPPORTED = "不支持的第三方登录提供商";
    public static final String ERROR_PROVIDER_DISABLED = "第三方登录提供商已禁用";
    public static final String ERROR_TOKEN_INVALID = "访问令牌无效";
    public static final String ERROR_TOKEN_EXPIRED = "访问令牌已过期";
    public static final String ERROR_USER_INFO_FETCH_FAILED = "获取第三方用户信息失败";
    public static final String ERROR_PARAMS_EMPTY = "第三方登录参数为空";
    public static final String ERROR_DEVICE_ID_EMPTY = "设备ID不能为空";
    public static final String ERROR_DEVICE_ID_INVALID = "设备ID格式无效";
    
    /** 正则表达式 */
    public static final String DEVICE_ID_PATTERN = "^[a-zA-Z0-9_-]+$";
    public static final String EMAIL_PATTERN = "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$";
    // 世界各地的手机号码格式
    public static final String PHONE_PATTERN = "^\\+?[0-9]{1,3}\\-?[0-9]{10,11}$";
    
    /** JWT相关常量 */
    public static final String JWT_HEADER_SEPARATOR = ".";
    public static final int JWT_PARTS_COUNT = 3;
    public static final String JWT_CLAIM_ISS = "iss";
    public static final String JWT_CLAIM_AUD = "aud";
    public static final String JWT_CLAIM_EXP = "exp";
    public static final String JWT_CLAIM_EMAIL = "email";
    public static final String JWT_CLAIM_NAME = "name";
    
    /** 缓存相关常量 */
    public static final String CACHE_KEY_PREFIX_USER_TOKEN = "app:user:token:";
    public static final String CACHE_KEY_PREFIX_DEVICE = "app:device:";
    public static final String CACHE_KEY_PREFIX_THIRD_PARTY = "app:third:party:";
    
    /** 时间相关常量（秒） */
    public static final long TOKEN_EXPIRE_TIME = 7 * 24 * 60 * 60; // 7天
    public static final long DEVICE_EXPIRE_TIME = 30 * 24 * 60 * 60; // 30天
    public static final long RETRY_DELAY_BASE = 1000; // 1秒
    
    /** 限制数量常量 */
    public static final int MAX_DEVICE_COUNT_PER_USER = 5;
    public static final int MAX_RETRY_COUNT = 3;
    public static final int MAX_NICKNAME_LENGTH = 128;
    public static final int MAX_EMAIL_LENGTH = 255;
    public static final int MAX_PHONE_LENGTH = 55;
    public static final int MAX_DEVICE_ID_LENGTH = 128;
} 