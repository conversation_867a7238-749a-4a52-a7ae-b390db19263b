package com.ruoyi.common.drama.vo;


import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import software.amazon.awssdk.services.s3.endpoints.internal.Value;

import java.util.Date;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "短剧VO")
public class MovieVO extends BaseEntity {

    /**
     * 短剧ID
     */
    @ApiModelProperty("短剧ID")
    private Long id;

    /**
     * 短剧标题
     */
    @ApiModelProperty("短剧标题")
    private String title;

    /**
     * 短剧名称
     */
    @ApiModelProperty("短剧名称")
    private String name;

    /**
     * 封面图片
     */
    @ApiModelProperty("封面图片")
    private String coverImage;

    /**
     * 发布日期
     */
    @ApiModelProperty("发布日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date releaseDate;

    /**
     * 总集数
     */
    @ApiModelProperty("总集数")
    private Long totalVideos;

    /**
     * 点赞次数
     */
    @ApiModelProperty("点赞次数")
    private Long likeCount;

    /**
     * 收藏次数
     */
    @ApiModelProperty("收藏次数")
    private Long collectCount;

    /**
     * 搜索次数
     */
    @ApiModelProperty("搜索次数")
    private Long searchCount;

    /**
     * 播放次数
     */
    @ApiModelProperty("热度")
    private Long playCount;

    /**
     * 标签列表
     */
    private List<MovieTagVO> tags;
}

