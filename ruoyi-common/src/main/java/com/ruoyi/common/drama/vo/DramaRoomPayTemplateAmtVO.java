package com.ruoyi.common.drama.vo;

import com.ruoyi.common.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@ApiModel(description = "内购商品金额")
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DramaRoomPayTemplateAmtVO {

    /** 主键ID */
    @ApiModelProperty("主键ID")
    private Long id;

    /** 价格 */
    @ApiModelProperty("价格")
    @Excel(name = "价格")
    private BigDecimal price;

    /** 金币数量 */
    @ApiModelProperty("金币数量")
    @Excel(name = "金币数量")
    private Long coin;

    /** 苹果内购商品id */
    @ApiModelProperty("苹果内购商品id")
    @Excel(name = "苹果内购商品id")
    private String appleProductId;

    /** 谷歌内购商品id */
    @ApiModelProperty("谷歌内购商品id")
    @Excel(name = "谷歌内购商品id")
    private String googleProductId;
}
