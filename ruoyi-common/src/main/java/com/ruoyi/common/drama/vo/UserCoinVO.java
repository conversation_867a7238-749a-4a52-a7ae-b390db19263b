package com.ruoyi.common.drama.vo;


import com.ruoyi.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "用户金币VO")
public class UserCoinVO extends BaseEntity {

    /**
     * ID
     */
    @ApiModelProperty("ID")
    private Long id;

    /**
     * 用户id
     */
    @ApiModelProperty("用户id")
    private Long userId;

    /**
     * 金币数量
     */
    @ApiModelProperty("金币数量")
    private Long coins;

    /** 奖励金币数量 */
    @ApiModelProperty(name = "奖励金币数量")
    private Long rewardCoins;

    /** 状态（1启用 0停用） */
    @ApiModelProperty(name = "状态：1=启用,0=停用")
    private String status;

}

