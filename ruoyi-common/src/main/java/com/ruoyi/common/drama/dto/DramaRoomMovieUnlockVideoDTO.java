package com.ruoyi.common.drama.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@ApiModel(description = "短剧查询对象")
@Data
public class DramaRoomMovieUnlockVideoDTO {

    /** 短剧ID */
    @ApiModelProperty(value = "短剧ID", required = true)
    @NotNull(message = "短剧ID不能为空")
    private Long movieId;

    /** 视频ID */
    @ApiModelProperty(value = "视频ID", required = true)
    @NotNull(message = "视频ID不能为空")
    private Long videoId;
}
