package com.ruoyi.common.drama.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@ApiModel(description = "短剧查询对象")
@Data
public class DramaRoomMovieQueryDTO {

    @ApiModelProperty(value = "分类ID")
    private  Long categoryId;

    @ApiModelProperty(value = "地区ID")
    private Long areaId;

    @ApiModelProperty(value = "音频ID")
    private Long audioId;

    @ApiModelProperty(value = "排序字段(热度-hot 最新-new)")
    private String sort;

    @ApiModelProperty(value = "短剧ID列表")
    private List<Long> movieIdList;
}
