package com.ruoyi.common.drama.dto;

import com.ruoyi.common.core.domain.model.AppLoginInfo;
import com.ruoyi.common.utils.SocialLoginVerifier;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

/**
 * 游客登录DTO
 *
 * <AUTHOR>
 */
@Data
@ApiModel(description = "游客登录DTO")
public class GuestLoginDto {

    @ApiModelProperty(value = "设备ID", required = true, example = "1234567890123456",notes = "设备ID，长度不能超过128字符")
    @NotBlank(message = "设备ID不能为空")
    @Size(max = 128, message = "设备ID长度不能超过128字符")
    private String deviceId;

    /** 设备厂商 */
    @ApiModelProperty(value = "设备厂商", required = true, example = "华为",notes = "设备厂商，长度不能超过64字符")
    @NotBlank(message = "设备厂商不能为空")
    @Size(max = 64, message = "设备厂商长度不能超过64字符")
    private String deviceManufacturer;

    /** 设备平台类型（android=安卓，ios=苹果，web=网页等） */

    @ApiModelProperty(value = "设备平台类型", required = true, example = "android",
            allowableValues = "android,ios,web", notes = "设备平台类型，长度不能超过32字符")
    @NotBlank(message = "设备平台类型不能为空")
    @Size(max = 32, message = "设备平台类型长度不能超过32字符")
    private String devicePlatformType;

    /** 设备系统版本 */
    @ApiModelProperty(value = "设备系统版本", required = true, example = "8.0.0",notes = "设备系统版本，长度不能超过32字符")
    @NotBlank(message = "设备系统版本不能为空")
    @Size(max = 32, message = "设备系统版本长度不能超过32字符")
    private String deviceSystemVersion;

    public AppLoginInfo initAppLoginInfo() {
        return new AppLoginInfo(deviceId, deviceManufacturer, devicePlatformType, deviceSystemVersion);
    }
}
