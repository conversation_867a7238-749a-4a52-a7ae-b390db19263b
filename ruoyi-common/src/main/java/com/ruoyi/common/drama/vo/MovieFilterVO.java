package com.ruoyi.common.drama.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(description = "筛选VO")
public class MovieFilterVO {

    @ApiModelProperty("地区列表")
    private List<MovieAreasVO> areaId;

    @ApiModelProperty("音讯列表")
    private List<MovieAudioVO> audioId;

    @ApiModelProperty("分类列表")
    private List<MovieCategoryVO> categoryId;

    @ApiModelProperty("排序列表")
    private List<MovieSortVO> sort;
}
