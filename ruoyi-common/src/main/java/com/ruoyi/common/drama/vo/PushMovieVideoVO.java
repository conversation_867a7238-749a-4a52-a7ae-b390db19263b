package com.ruoyi.common.drama.vo;

import com.ruoyi.common.core.page.PageResult;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import software.amazon.awssdk.services.s3.endpoints.internal.Value;

@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
@ApiModel(description = "推荐短剧数据VO")
public class PushMovieVideoVO {

    /**
     * 短剧ID
     */
    @ApiModelProperty("短剧ID")
    private Long id;

    /**
     * 短剧标题
     */
    @ApiModelProperty("短剧标题")
    private String title;

    @ApiModelProperty("短剧名称")
    private String name;


    /**
     * 短剧描述
     */
    @ApiModelProperty("短剧描述")
    private String description;

    /**
     * 总集数
     */
    @ApiModelProperty("总集数")
    private Long totalVideos;

    /**
     * 点赞次数
     */
    @ApiModelProperty("点赞次数")
    private Long likeCount;

    /**
     * 收藏次数
     */
    @ApiModelProperty("收藏次数")
    private Long collectCount;

    /**
     * 短剧视频
     */
    @ApiModelProperty("短剧视频")
    private VideoVO video;
}
