package com.ruoyi.common.drama.dto;

import com.ruoyi.common.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@ApiModel(description = "短剧扣费规则配置对象")
@Data
public class DramaRoomMovieCoinRuleDTO {

    /** 主键ID */
    @ApiModelProperty(value = "主键ID")
    private Long id;

    /** 规则名字 */
    @ApiModelProperty(value = "规则名字")
    @Excel(name = "规则名字")
    private String name;

    /** 影片ID */
    @ApiModelProperty(value = "影片ID")
    @Excel(name = "影片ID")
    private Long movieId;


    /** 影片渠道金币配置 */
    @ApiModelProperty(value = "影片渠道金币配置")
    List<DramaRoomVideoChannelCoinDTO> videoChannelCoinList;
}
