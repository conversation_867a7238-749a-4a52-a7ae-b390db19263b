package com.ruoyi.common.drama.vo;


import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(description = "短剧详情VO")
public class MovieDetailVO {

    @ApiModelProperty("短剧id")
    private Long id;

    @ApiModelProperty("短剧标题")
    private String title;

    @ApiModelProperty("短剧名称")
    private String name;

    @ApiModelProperty("短剧描述")
    private String description;

    @ApiModelProperty("短剧图片")
    private String coverImage;

    @ApiModelProperty("导演")
    private String director;

    @ApiModelProperty("演员")
    private String actors;

    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty("上映时间")
    private Date releaseDate;

    @ApiModelProperty("总集数")
    private Long totalVideos;

    @ApiModelProperty("收藏次数")
    private Long collectCount;

    @ApiModelProperty("所有剧集播放次数")
    private Long playCount;

    @ApiModelProperty("所有剧集点赞次数")
    private Long likeCount;

    @ApiModelProperty("搜索次数")
    private Long searchCount;

    @ApiModelProperty("短剧地区")
    private MovieAreasVO area;

    @ApiModelProperty("短剧音讯")
    private MovieAudioVO audio;

    @ApiModelProperty("短剧标签对象列表")
    private List<MovieTagVO> tags;

    @ApiModelProperty("所有剧集")
    private List<VideoVO> videos;

    @ApiModelProperty("是否收藏")
    private Boolean isCollect;
}
