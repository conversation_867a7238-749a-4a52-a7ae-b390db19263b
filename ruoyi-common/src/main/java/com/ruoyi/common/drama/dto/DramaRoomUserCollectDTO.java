package com.ruoyi.common.drama.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class DramaRoomUserCollectDTO {

    /**
     * 用户ID
     */
    @ApiModelProperty(value = "用户ID")
    private Long userId;

    /**
     * 短剧ID
     */
    @ApiModelProperty(value = "短剧ID")
    private Long movieId;


    /**
     * 收藏状态：0收藏 1取消收藏
     */
    @ApiModelProperty(value = "收藏状态：0收藏 1取消收藏")
    private Long status;

}
