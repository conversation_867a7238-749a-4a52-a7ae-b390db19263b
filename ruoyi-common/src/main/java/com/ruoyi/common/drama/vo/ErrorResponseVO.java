package com.ruoyi.common.drama.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 错误响应VO
 * 用于Swagger文档展示统一的错误响应格式
 * 
 * <AUTHOR>
 */
@Data
@ApiModel(description = "统一错误响应格式")
public class ErrorResponseVO {
    
    @ApiModelProperty(value = "状态码", example = "400", notes = "HTTP状态码")
    private Integer code;
    
    @ApiModelProperty(value = "错误消息", example = "设备ID不能为空", notes = "用户可读的错误信息")
    private String msg;
    
    @ApiModelProperty(value = "错误详情", notes = "详细的错误信息，包含错误类型、是否可重试等")
    private ErrorDetailVO data;
    
    @Data
    @ApiModel(description = "错误详情")
    public static class ErrorDetailVO {
        
        @ApiModelProperty(value = "错误类型", example = "VALID", 
                         allowableValues = "AUTH,VALID,RATE,THIRD,INTERNAL", 
                         notes = "错误分类：AUTH-认证错误, VALID-参数错误, RATE-频率限制, THIRD-第三方服务, INTERNAL-系统错误")
        private String errorType;
        
        @ApiModelProperty(value = "错误代码", example = "400", notes = "具体的错误代码")
        private String errorCode;
        
        @ApiModelProperty(value = "是否可重试", example = "false", notes = "客户端是否可以重试此请求")
        private Boolean retryable;
        
        @ApiModelProperty(value = "重试等待时间(秒)", example = "60", notes = "如果可重试，建议等待的秒数")
        private Integer retryAfter;
    }
} 