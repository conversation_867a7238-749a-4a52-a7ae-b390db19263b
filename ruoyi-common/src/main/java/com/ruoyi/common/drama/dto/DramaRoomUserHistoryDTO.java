package com.ruoyi.common.drama.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class DramaRoomUserHistoryDTO {

    /** 用户id */
    @ApiModelProperty("用户id")
    @Excel(name = "用户id")
    private Long userId;

    /** 短剧Id */
    @ApiModelProperty("短剧Id")
    @Excel(name = "短剧Id")
    private Long movieId;

    /** 剧集id */
    @ApiModelProperty("剧集id")
    @Excel(name = "剧集id")
    private Long videoId;

    @ApiModelProperty("视频剧集")
    private Integer videoNum;

    /** 浏览时间 */
    @ApiModelProperty("浏览时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date viewDate;

    /** 浏览进度 */
    @ApiModelProperty("浏览进度")
    private Long progress;
}
