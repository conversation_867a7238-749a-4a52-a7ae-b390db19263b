package com.ruoyi.common.drama.vo;


import com.ruoyi.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "金币规则VO")
public class CoinRuleVO extends BaseEntity {

    /**
     * ID
     */
    @ApiModelProperty("ID")
    private Long id;

    /**
     * 0.充值1.
     */
    @ApiModelProperty("0.充值1.")
    private Long type;

    /**
     * 规则名称
     */
    @ApiModelProperty("规则名称")
    private String name;


    /** 说明 */
    @ApiModelProperty(name = "说明")
    private String content;

    /** 充值金额 */
    @ApiModelProperty(name = "充值金额")
    private BigDecimal rechargeAmount;

    /** 金币数量 */
    @ApiModelProperty(name = "金币数量")
    private Long coinAmount;

    /** 赠送金币数量 */
    @ApiModelProperty(name = "赠送金币数量")
    private Long giveCoinAmount;

    /** 状态（1启用 0停用） */
    @ApiModelProperty(name = "状态：1=启用,0=停用")
    private String status;

}

