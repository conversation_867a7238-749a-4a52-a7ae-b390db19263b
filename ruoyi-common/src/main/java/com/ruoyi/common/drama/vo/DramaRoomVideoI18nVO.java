package com.ruoyi.common.drama.vo;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 剧集多语言对象 drama_room_video_i18n
 * 
 * <AUTHOR>
 * @date 2025-08-05
 */
@Data
@ApiModel(description = "剧集多语言VO")
public class DramaRoomVideoI18nVO
{
    /** 主键ID */
    @ApiModelProperty(value = "主键ID")
    private Long id;

    /** video id */
    @ApiModelProperty(value = "剧集ID")
    private Long videoId;

    /** 多语言标识 */
    @ApiModelProperty(value = "多语言标识")
    private String languageCode;

    /** 字幕url */
    @ApiModelProperty(value = "字幕url")
    private String subtitleUrl;

    /** 翻译状态：0未审核，1通过，2拒绝 */
    @ApiModelProperty(value = "翻译状态：0未审核，1通过，2拒绝")
    private String status;
}
