package com.ruoyi.common.drama.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(description = "用户金币VO")
public class UserInfoVO {

    /** 用户ID */
    @ApiModelProperty("用户ID")
    private String userId;

    /** 用户头像URL */
    @ApiModelProperty("用户头像URL")
    private String avatar;

    /** 账户类型（GUEST=游客，THIRD_PARTY=第三方账户类型） */
    @ApiModelProperty("账户类型（GUEST=游客，THIRD_PARTY=第三方账户类型）")
    private String accountType;

}
