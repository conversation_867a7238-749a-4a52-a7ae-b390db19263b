package com.ruoyi.common.drama.dto;

import com.ruoyi.common.core.domain.entity.AppUserDevice;
import com.ruoyi.common.core.domain.model.AppLoginInfo;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.SocialLoginVerifier;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import javax.validation.constraints.Pattern;

/**
 * 第三方登录请求DTO
 * 
 * <AUTHOR>
 */
@Data
@ApiModel(description = "第三方登录（Google/Apple）请求参数")
public class ThirdPartyLoginDTO {
    
    @ApiModelProperty(value = "第三方提供商", required = true, example = "GOOGLE",
                     allowableValues = "GOOGLE,FACEBOOK,APPLE", notes = "支持Google和Apple、Facebook登录")
    @NotNull(message = "第三方提供商不能为空")
    private SocialLoginVerifier.Provider provider;
    
    @ApiModelProperty(value = "第三方访问令牌", required = true, example = "ya29.a0AfH6SMB...",
                     notes = "Google ID Token或Apple Identity Token")
    @NotBlank(message = "访问令牌不能为空")
    @Size(max = 5000, message = "访问令牌长度不能超过5000字符")
    private String accessToken;

    /** 设备厂商 */
    @ApiModelProperty(value = "设备厂商", required = true, example = "华为",notes = "设备厂商，长度不能超过64字符")
    @NotBlank(message = "设备厂商不能为空")
    @Size(max = 64, message = "设备厂商长度不能超过64字符")
    private String deviceManufacturer;

    /** 设备平台类型（android=安卓，ios=苹果，web=网页等） */

    @ApiModelProperty(value = "设备平台类型", required = true, example = "android",
            allowableValues = "android,ios,web", notes = "设备平台类型，长度不能超过32字符")
    @NotBlank(message = "设备平台类型不能为空")
    @Size(max = 32, message = "设备平台类型长度不能超过32字符")
    private String devicePlatformType;

    /** 设备系统版本 */
    @ApiModelProperty(value = "设备系统版本", required = true, example = "8.0.0",notes = "设备系统版本，长度不能超过32字符")
    @NotBlank(message = "设备系统版本不能为空")
    @Size(max = 32, message = "设备系统版本长度不能超过32字符")
    private String deviceSystemVersion;


    public void initDeviceInfo(AppUserDevice device,Long userId,String deviceId) {
        if (device == null || userId == null  || deviceId == null) {
            throw new ServiceException("设备信息或用户信息不能为空");
        }
        device.init(userId,deviceId, deviceManufacturer, devicePlatformType,deviceSystemVersion);
    }
} 