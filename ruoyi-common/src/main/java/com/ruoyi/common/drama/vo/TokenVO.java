package com.ruoyi.common.drama.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * Token响应VO
 * 
 * <AUTHOR>
 */
@Data
@ApiModel(description = "登录成功返回的Token信息")
public class TokenVO {
    
    @ApiModelProperty(value = "JWT访问令牌", example = "eyJhbGciOiJIUzUxMiJ9...", 
                     notes = "用于后续API调用的访问令牌")
    private String token;

    @ApiModelProperty(value = "JWT刷新访问令牌", example = "eyJhbGciOiJIUzUxMiJ9...",
                     notes = "用于后续刷新token调用的访问令牌")
    private String refreshToken;
    
    @ApiModelProperty(value = "令牌类型", example = "Bearer", 
                     notes = "固定值Bearer，用于Authorization头部")
    private String tokenType = "Bearer";
    
    @ApiModelProperty(value = "过期时间(秒)", example = "1800", 
                     notes = "令牌有效期，默认30分钟 * 12 = 6小时")
    private Long expiresIn = 1800L * 12;

    @ApiModelProperty(value = "是否被顶下线", example = "false",
                     notes = "true表示当前登录用户已被顶下线，需要重新登录")
    private boolean isTop;
    
    public TokenVO() {
    }
    
    public TokenVO(String token, String refreshToken) {
        this.token = token;
        this.refreshToken = refreshToken;
    }

    public TokenVO(String token, String refreshToken, boolean isTop) {
        this.token = token;
        this.refreshToken = refreshToken;
        this.isTop = isTop;
    }
    
    public TokenVO(String token, Long expiresIn) {
        this.token = token;
        this.expiresIn = expiresIn;
    }
} 