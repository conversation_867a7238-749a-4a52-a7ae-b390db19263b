package com.ruoyi.common.drama.vo;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(description = "剧集VO")
public class VideoVO {
    @ApiModelProperty("剧集id")
    private Long id;

    @ApiModelProperty("封面")
    private String coverImage;

    @ApiModelProperty("第几集")
    private Long videoNum;

    @ApiModelProperty("剧集地址")
    private String videoUrl;

    @ApiModelProperty("剧集时长(秒)")
    private String duration;

    @ApiModelProperty("剧集播放次数")
    private Long playCount;

    @ApiModelProperty("剧集点赞数")
    private Long likeCount;

    @ApiModelProperty("字幕地址")
    private List<DramaRoomVideoI18nVO> subtitleUrlList;

    @ApiModelProperty("解锁所需金币")
    private Integer unlockCoin;

    @ApiModelProperty("是否解锁")
    private boolean isUnlock;
}
