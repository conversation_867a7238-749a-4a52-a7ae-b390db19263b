package com.ruoyi.common.drama.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@ApiModel(description = "用户收藏信息")
@Data
public class UserCollectVO {

    private Long id;

    /**
     * 用户ID
     */
    @ApiModelProperty(value = "用户ID")
    @Excel(name = "用户ID")
    private Long userId;

    /**
     * 短剧ID
     */
    @ApiModelProperty(value = "短剧ID")
    @Excel(name = "短剧ID")
    private Long movieId;

    /**
     * 收藏状态：0收藏 1取消收藏
     */
    @ApiModelProperty(value = "收藏状态：0收藏 1取消收藏")
    @Excel(name = "收藏状态：0收藏 1取消收藏")
    private Long status;

    /**
     * 短剧标题
     */
    @ApiModelProperty("短剧标题")
    private String title;

    /**
     * 封面图片
     */
    @ApiModelProperty("封面图片")
    private String coverImage;

    /**
     * 发布日期
     */
    @ApiModelProperty("发布日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date releaseDate;

    /**
     * 总集数
     */
    @ApiModelProperty("总集数")
    private Long totalVideos;

    @ApiModelProperty("视频ID")
    private Long videoId;

    @ApiModelProperty("观看时间")
    private Date viewDate;

    @ApiModelProperty("观看进度")
    private String progress;
}
