package com.ruoyi.common.drama.vo;


import com.ruoyi.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "用户金币记录VO")
public class UserCoinLogVO extends BaseEntity {

    /**
     * ID
     */
    @ApiModelProperty("ID")
    private Long id;

    @ApiModelProperty("类型：0.充值1.赠送2.签到3.消费")
    private Long type;

    /**
     * 用户id
     */
    @ApiModelProperty("用户id")
    private Long userId;

    /**
     * 金币数量
     */
    @ApiModelProperty("金币数量")
    private Long coins;

    /** 状态（1启用 0停用） */
    @ApiModelProperty(name = "状态：1=启用,0=停用")
    private String status;

    @ApiModelProperty("日期")
    private String dateTime;

    @ApiModelProperty("签到类型：0.未签到，1.已签到，2.漏签")
    private Integer signStatus;

    @ApiModelProperty("天数：1-7")
    private Integer days;

    @ApiModelProperty("来源id")
    private Long sourceId;

    @ApiModelProperty("金币余额")
    private Long coinsBalance;

    @ApiModelProperty("规则明细id")
    private Long signInRuleInfoId;

}

