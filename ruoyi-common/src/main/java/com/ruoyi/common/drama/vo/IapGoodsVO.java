package com.ruoyi.common.drama.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@ApiModel(description = "内购商品")
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class IapGoodsVO {

    /**
     * 订阅列表
     */
    @ApiModelProperty("订阅列表")
    private List<DramaRoomPayTemplateSubscriptionVO> subscriptionList;

    /**
     * 金币列表（充值）
     */
    @ApiModelProperty("金币列表（充值）")
    private List<DramaRoomPayTemplateAmtVO> coinList;
}
