package com.ruoyi.common.drama.vo;

import com.ruoyi.common.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@ApiModel(description = "订阅模板")
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class DramaRoomPayTemplateSubscriptionVO {

    /**
     * id
     */
    @ApiModelProperty("id")
    private String id;

    /**
     * 订阅周期类型:1周，2月，3年
     */
    @ApiModelProperty("订阅周期类型:1周，2月，3年")
    @Excel(name = "订阅周期类型:1周，2月，3年")
    private Integer periodType;

    /**
     * 订阅等级，如1铜、2银、3金
     */
    @ApiModelProperty("订阅等级，如1铜、2银、3金")
    @Excel(name = "订阅等级，如1铜、2银、3金")
    private Integer level;

    /**
     * 该订阅的总有效天数，如周=7，月=30，年=365
     */
    @ApiModelProperty("该订阅的总有效天数，如周=7，月=30，年=365")
    @Excel(name = "该订阅的总有效天数，如周=7，月=30，年=365")
    private Integer durationDays;

    /**
     * 用户每天可领取的金币数
     */
    @ApiModelProperty("用户每天可领取的金币数")
    @Excel(name = "用户每天可领取的金币数")
    private Integer dailyCoins;

    /**
     * 该订阅周期内总共可获得的金币数
     */
    @ApiModelProperty("该订阅周期内总共可获得的金币数")
    @Excel(name = "该订阅周期内总共可获得的金币数")
    private Integer totalCoins;

    /**
     * 每日赠送用户金币数
     */
    @ApiModelProperty("每日赠送用户金币数")
    @Excel(name = "每日赠送用户金币数")
    private Integer dailySendCoins;

    /**
     * 该订阅周期内总共赠送金币数
     */
    @ApiModelProperty("该订阅周期内总共赠送金币数")
    @Excel(name = "该订阅周期内总共赠送金币数")
    private Integer totalSendCoins;

    /**
     * 前端展示名称，如“周卡（铜级）金币包”
     */
    @ApiModelProperty("前端展示名称，如“周卡（铜级）金币包”")
    @Excel(name = "前端展示名称，如“周卡", readConverterExp = "铜=级")
    private String displayName;

    /**
     * 订阅详情描述，如领取规则等
     */
    @ApiModelProperty("订阅详情描述，如领取规则等")
    @Excel(name = "订阅详情描述，如领取规则等")
    private String description;

    /**
     * 价格
     */
    @ApiModelProperty("价格")
    @Excel(name = "价格")
    private BigDecimal price;

    /**
     * 苹果内购订阅id
     */
    @ApiModelProperty("苹果内购订阅id")
    @Excel(name = "苹果内购订阅id")
    private String appleProductId;

    /**
     * 谷歌内购订阅id
     */
    @ApiModelProperty("谷歌内购订阅id")
    @Excel(name = "谷歌内购订阅id")
    private String googleProductId;
}
