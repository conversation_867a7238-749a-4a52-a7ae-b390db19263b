package com.ruoyi.common.drama.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Map;

@Data
@ApiModel(description = "内购通知参数")
public class IapNotifyDTO {

//    @ApiModelProperty(value = "渠道编码(APPLE_IAP、GOOGLE_IAP)")
//    private String channelCode;

    // notificationType
    @ApiModelProperty(value = "通知类型")
    private String notificationType;

    // notificationUUID
    @ApiModelProperty(value = "通知UUID")
    private String notificationUUID;

    // data
    @ApiModelProperty(value = "通知数据")
    private Map<String, Object> data;
}
