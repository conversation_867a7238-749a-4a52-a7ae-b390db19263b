package com.ruoyi.common.drama.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * APP 用户行为上报 DTO
 */
@ApiModel(description = "APP 用户行为上报 DTO")
@Data
public class UserBehaviorLogDTO {
    @ApiModelProperty(value = "行为类型", required = true, example = "USER_BROWSE_PAGE",
            allowableValues = "USER_BROWSE_PAGE,USER_WATCH_EPISODE,USER_WATCH_MOVIE,USER_JOIN_ACTIVITY,USER_FEEDBACK,USER_OPEN_PAY,USER_OPEN_UNPAY,PLAY,COLLECT,LIKE,SEARCH,MOVIE_WATCH_TIME",
            notes = "前端直接传枚举值（区分大小写与否由后端兼容，建议大写）")
    private String behaviorType;

    @ApiModelProperty(value = "行为前页面停留时间(毫秒)", dataType = "long", example = "1234")
    private Long stayTime;

    @ApiModelProperty(value = "用户访问的页面标识", dataType = "int", example = "101", notes = "与客户端页面映射约定")
    private String pageUrl;

    @ApiModelProperty(value = "设备类型", dataType = "int", example = "1", notes = "与客户端设备类型约定")
    private String deviceType;

    @ApiModelProperty(value = "设备操作系统", dataType = "int", example = "2", notes = "与客户端设备OS约定")
    private String deviceOs;

    @ApiModelProperty(value = "设备厂商", dataType = "int", example = "3", notes = "与客户端设备厂商约定")
    private String deviceBrand;

    @ApiModelProperty(value = "行为描述", dataType = "string", example = "从首页进入详情后播放")
    private String behaviorDesc;

    @ApiModelProperty(value = "其他操作信息", dataType = "string", example = "点击播放", notes = "建议不超过1000字符")
    private String actionDetail;
}


