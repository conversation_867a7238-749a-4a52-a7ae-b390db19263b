package com.ruoyi.common.drama.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.math.BigDecimal;

@Data
@ApiModel(description = "内购验证参数")
public class IapVerifyDTO {

    @ApiModelProperty(value = "渠道编码(APPLE_IAP、GOOGLE_IAP)")
    @NotBlank(message = "渠道编码不能为空")
    private String channelCode;

    // 内购凭证（苹果receipt/谷歌purchaseToken）
    @ApiModelProperty(value = "内购收据")
    @NotBlank(message = "内购凭证不能为空")
    private String receipt;

    @ApiModelProperty(value = "用户ID")
    @NotBlank(message = "用户ID不能为空")
    private Long userId;

    @ApiModelProperty(value = "订单编号")
    private String orderNo;

    @ApiModelProperty(value = "交易编号")
    private String tradeNo;

    @ApiModelProperty(value = "订单金额")
    private BigDecimal amount;

    @ApiModelProperty(value = "第三方交易号")
    private String transactionId; // 第三方交易号
}
