package com.ruoyi.common.drama.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class UserHistoryVO {

    /** 主键 */
    private Long id;

    /** 短剧Id */
    @ApiModelProperty("短剧Id")
    private Long movieId;

    /** 剧集id */
    @ApiModelProperty("剧集id")
    private Long videoId;

    @ApiModelProperty("视频剧集")
    private Integer videoNum;

    /** 浏览时间 */
    @ApiModelProperty("浏览时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date viewDate;

    /** 浏览进度 */
    @ApiModelProperty("浏览进度")
    private Long progress;

    /**
     * 短剧标题
     */
    @ApiModelProperty("短剧标题")
    private String title;

    /**
     * 封面图片
     */
    @ApiModelProperty("封面图片")
    private String coverImage;

    /**
     * 发布日期
     */
    @ApiModelProperty("发布日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date releaseDate;

    /**
     * 总集数
     */
    @ApiModelProperty("总集数")
    private Long totalVideos;

    /**
     * 是否收藏
     */
    @ApiModelProperty("是否收藏")
    private Boolean isCollect;
}
