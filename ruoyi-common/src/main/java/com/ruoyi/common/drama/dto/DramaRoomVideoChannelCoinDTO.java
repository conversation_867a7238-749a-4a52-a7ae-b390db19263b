package com.ruoyi.common.drama.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel(description = "视频渠道扣金币配置对象")
@Data
public class DramaRoomVideoChannelCoinDTO {

    /**
     * 金币
     */
    @ApiModelProperty(value = "金币")
    private Integer coin;

    /**
     * 开始剧集
     */
    @ApiModelProperty(value = "开始剧集")
    private Integer startNum;

    /**
     * 结束剧集
     */
    @ApiModelProperty(value = "结束剧集")
    private Integer endNum;
}
