package com.ruoyi.common.drama.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(description = "短剧分类数据VO")
public class MovieCategoryVO {

    @ApiModelProperty("分类id")
    private Long id;

    @ApiModelProperty("分类名称")
    private String name;

    @ApiModelProperty("分类下的短剧")
    private List<MovieVO> movies;

    @ApiModelProperty(value = "排序")
    private Long sort;
}
