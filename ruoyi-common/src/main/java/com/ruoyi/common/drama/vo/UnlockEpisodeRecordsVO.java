package com.ruoyi.common.drama.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
@ApiModel(description = "解锁剧集记录VO")
public class UnlockEpisodeRecordsVO {

    /**
     * ID
     */
    @ApiModelProperty("ID")
    private Long id;

    /**
     * 剧集ID
     */
    @ApiModelProperty("剧集ID")
    private Long videoId;

    /**
     * 短剧名称
     */
    @ApiModelProperty("短剧名称")
    private String videoName;

    /**
     * 短剧集数
     */
    @ApiModelProperty("短剧集数")
    private Integer episodeNum;

    /**
     * 解锁时间
     */
    @ApiModelProperty("解锁时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date unlockTime;

    /** 解锁类型 */
    @ApiModelProperty("解锁类型 (0-金币解锁，1-看广告解锁)")
    private Integer unlockType;

    /**
     * 解锁消耗金币
     */
    @ApiModelProperty("解锁消耗金币")
    private Integer unlockCoin;

}
