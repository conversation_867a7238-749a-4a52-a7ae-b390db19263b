package com.ruoyi.common.drama.domain;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class ShortAppPurchaseVerifyResult implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "id")
    private Long id;

    @ApiModelProperty(value = "应用类型")
    private String appType;

    @ApiModelProperty(value = "订单ID")
    private Long orderId;

    @ApiModelProperty(value = "订单类型")
    private String orderType;

    @ApiModelProperty(value = "票据验证状态码")
    private Integer statusCode;

    //票据生成的环境。Production表示生产环境，Sandbox表示沙盒环境
    @ApiModelProperty(value = "票据验证环境")
    private String environment;

    @ApiModelProperty(value = "应用的唯一标识符Apple ID")
    private String adamId;

    @ApiModelProperty(value = "应用程序的ID")
    private String appItemId;

    @ApiModelProperty(value = "应用包名")
    private String bundleId;

    @ApiModelProperty(value = "应用版本号")
    private String applicationVersion;

    @ApiModelProperty(value = "票据创建时间 UTC")
    private String receiptCreationDate;

    @ApiModelProperty(value = "验证票据时间 UTC")
    private String requestDate;

    @ApiModelProperty(value = "购买日期")
    private String purchaseDate;

    @ApiModelProperty(value = "购买数量")
    private String quantity;

    @ApiModelProperty(value = "产品标识符")
    private String productId;

    @ApiModelProperty(value = "交易ID")
    private String transactionId;

    @ApiModelProperty(value = "票据")
    private String receipt;

    @ApiModelProperty(value = "票据验证结果-完整json")
    private String jsonObject;

    @ApiModelProperty(value = "原始交易ID")
    private String originalTransactionId;

    @ApiModelProperty(value = "票据到期时间")
    private String expiresDate;

    @ApiModelProperty(value = "是否是测试票据")
    private String isTrialPeriod;

    @ApiModelProperty(value = "票据是否处于内购优惠期")
    private String isInIntroOfferPeriod;

    @ApiModelProperty(value = "订阅自动续费状态")
    private String subscriptionAutoRenewStatus;
}
