package com.ruoyi.common.drama.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
@ApiModel(description = "金币交易记录VO")
public class UserCoinLogTradeVO {

    /**
     * ID
     */
    @ApiModelProperty("ID")
    private Long id;

    /** 0.充值1.赠送2.签到3.消费 */
    @ApiModelProperty("0.充值1.赠送2.签到3.解锁视频")
    private Long type;

    /** 交易类型 **/
    @ApiModelProperty("交易类型 0:入账 1:出账")
    private Integer tradeType;

    /** 变动金币数量 */
    @ApiModelProperty("变动金币数量")
    private Long coins;

    /** 变动前金币数量 */
    @ApiModelProperty("变动前金币数量")
    private Long beforeCoins;

    /** 变动后金币数量 */
    @ApiModelProperty("变动后金币数量")
    private Long afterCoins;

    /** 说明 */
    @ApiModelProperty("说明")
    private String content;

    /** 交易时间 */
    @ApiModelProperty("交易时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date tradeTime;
}
