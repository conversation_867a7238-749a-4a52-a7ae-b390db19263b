package com.ruoyi.common.drama.vo;


import com.ruoyi.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "用户金币记录VO")
public class SignInRuleVO extends BaseEntity {

    /**
     * ID
     */
    @ApiModelProperty("ID")
    private Long id;

    @ApiModelProperty("签到类型：0.基础签到1.断签归零2.自定义签到")
    private Integer type;


    @ApiModelProperty("签到名称：\n" +
            "如：\n" +
            "轮询签到---类型0\n" +
            "断签归零---类型1\n" +
            "----以下对应类型2\n" +
            "绑定邮箱\n" +
            "支持我们\n" +
            "账户登录\n" +
            "在instagram关注我们\n" +
            "在facebook关注我们\n" +
            "在youtube关注我们\n" +
            "每日观看/ 7个？\n" +
            "观看完整剧集/20个？")
    private String name;


    @ApiModelProperty("金币数量")
    private Integer num;

    @ApiModelProperty("签到类型：0.未签到，1.已签到")
    private Integer signStatus;

    @ApiModelProperty("图标")
    private String icon;

    @ApiModelProperty("跳转链接")
    private String jumpUrl;

    @ApiModelProperty("规则明细id")
    private Long signInRuleInfoId;

    @ApiModelProperty("分钟数")
    private Integer minutes;


}

