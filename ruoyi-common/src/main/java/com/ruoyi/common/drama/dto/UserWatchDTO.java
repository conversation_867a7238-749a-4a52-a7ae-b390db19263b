package com.ruoyi.common.drama.dto;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel(description = "用户观看记录DTO")
@Data
public class UserWatchDTO {

    @ApiModelProperty("短剧id")
    private Long movieId;

    @ApiModelProperty("剧集id")
    private Long videoId;

    @ApiModelProperty("观看进度")
    private Long progress;

    @ApiModelProperty("是否完成观看")
    private Boolean isFinished;

    @ApiModelProperty("观看开始时间")
    private Long watchStartTime;

    @ApiModelProperty("观看结束时间")
    private Long watchEndTime;

    @ApiModelProperty("观看设备")
    private String watchDevice;

    @ApiModelProperty("观看ip")
    private String watchIp;
}
