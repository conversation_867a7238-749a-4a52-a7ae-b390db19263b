package com.ruoyi.common.config;

import com.ruoyi.common.utils.uuid.SnowflakeIdWorker;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 雪花算法自动配置类
 * 
 * <AUTHOR>
 */
@Configuration
public class SnowflakeAutoConfiguration
{
    private static final Logger log = LoggerFactory.getLogger(SnowflakeAutoConfiguration.class);

    @Autowired
    private SnowflakeConfig snowflakeConfig;

    /**
     * 创建雪花算法ID生成器Bean
     */
    @Bean
    @ConditionalOnMissingBean
    public SnowflakeIdWorker snowflakeIdWorker()
    {
        Long workerId = snowflakeConfig.getWorkerId();
        Long datacenterId = snowflakeConfig.getDatacenterId();
        
        log.info("初始化雪花算法ID生成器: workerId={}, datacenterId={}", workerId, datacenterId);
        
        SnowflakeIdWorker worker = new SnowflakeIdWorker(workerId, datacenterId);
        
        if (snowflakeConfig.getMonitorEnabled()) {
            log.info("雪花算法监控已启用");
        }
        
        return worker;
    }
} 