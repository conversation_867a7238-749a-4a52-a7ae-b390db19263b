package com.ruoyi.common.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 雪花算法配置类
 * 
 * <AUTHOR>
 */
@Component
@ConfigurationProperties(prefix = "snowflake")
public class SnowflakeConfig
{
    /** 机器ID (0-31) */
    private Long workerId = 1L;

    /** 数据中心ID (0-31) */
    private Long datacenterId = 1L;

    /** 是否启用监控 */
    private Boolean monitorEnabled = true;

    public Long getWorkerId()
    {
        return workerId;
    }

    public void setWorkerId(Long workerId)
    {
        this.workerId = workerId;
    }

    public Long getDatacenterId()
    {
        return datacenterId;
    }

    public void setDatacenterId(Long datacenterId)
    {
        this.datacenterId = datacenterId;
    }

    public Boolean getMonitorEnabled()
    {
        return monitorEnabled;
    }

    public void setMonitorEnabled(Boolean monitorEnabled)
    {
        this.monitorEnabled = monitorEnabled;
    }
} 