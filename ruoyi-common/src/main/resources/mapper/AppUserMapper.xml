<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.common.core.mapper.AppUserMapper">

    <resultMap type="com.ruoyi.common.core.domain.entity.AppUser" id="AppUserResult">
        <id     property="userId"           column="user_id"            />
        <result property="userTempId"       column="user_temp_id"       />
        <result property="nickname"         column="nickname"           />
        <result property="avatar"           column="avatar"             />
        <result property="email"            column="email"              />
        <result property="phoneNumber"      column="phone_number"       />
        <result property="accountType"      column="account_type"       />
        <result property="status"           column="status"             />
        <result property="deleted"          column="deleted"            />
        <result property="authType"         column="auth_type"          />
        <result property="platformOpenId"   column="platform_open_id"   />
        <result property="credential"       column="credential"         />
        <result property="lastLoginIp"      column="last_login_ip"      />
        <result property="lastLoginDate"    column="last_login_date"    />
        <result property="firstDeviceId"    column="first_device_id"    />
        <result property="createBy"         column="create_by"          />
        <result property="createTime"       column="create_time"        />
        <result property="updateBy"         column="update_by"          />
        <result property="updateTime"       column="update_time"        />
        <result property="remark"           column="remark"             />
    </resultMap>

    <sql id="selectAppUserVo">
        select user_id, user_temp_id, nickname, avatar, email, phone_number, account_type, status, deleted, auth_type, platform_open_id, credential, last_login_ip, last_login_date, first_device_id, create_by, create_time, update_by, update_time, remark from app_user
    </sql>

    <select id="selectAppUserByDeviceId" parameterType="String" resultMap="AppUserResult">
        select u.user_id, u.user_temp_id, u.nickname, u.avatar, u.email, u.phone_number, u.account_type, u.status, u.deleted, u.auth_type, u.platform_open_id, u.credential, u.last_login_ip, u.last_login_date, u.first_device_id, u.create_by, u.create_time, u.update_by, u.update_time, u.remark 
        from app_user u 
        inner join app_user_device d on u.user_id = d.user_id 
        where d.device_id = #{deviceId} and u.deleted = '0'
    </select>

    <select id="selectAppUserList" parameterType="com.ruoyi.common.core.domain.entity.AppUser" resultMap="AppUserResult">
        <include refid="selectAppUserVo"/>
        <where>
            <if test="userTempId != null and userTempId != ''">
                and user_temp_id = #{userTempId}
            </if>
            <if test="nickname != null and nickname != ''">
                and nickname like concat('%', #{nickname}, '%')
            </if>
            <if test="email != null and email != ''">
                and email = #{email}
            </if>
            <if test="phoneNumber != null and phoneNumber != ''">
                and phone_number = #{phoneNumber}
            </if>
            <if test="accountType != null and accountType != ''">
                and account_type = #{accountType}
            </if>
            <if test="status != null and status != ''">
                and status = #{status}
            </if>
            <if test="deleted != null and deleted != ''">
                and deleted = #{deleted}
            </if>
            <if test="authType != null and authType != ''">
                and auth_type = #{authType}
            </if>
            <if test="platformOpenId != null and platformOpenId != ''">
                and platform_open_id = #{platformOpenId}
            </if>
            <if test="firstDeviceId != null and firstDeviceId != ''">
                and first_device_id = #{firstDeviceId}
            </if>
        </where>
        order by create_time desc
    </select>

    <select id="selectAppUserByUserId" parameterType="Long" resultMap="AppUserResult">
        <include refid="selectAppUserVo"/>
        where user_id = #{userId} and deleted = '0'
    </select>

    <select id="selectAppUserByEmail" parameterType="String" resultMap="AppUserResult">
        <include refid="selectAppUserVo"/>
        where email = #{email} and deleted = '0'
    </select>

    <select id="selectAppUserByPhoneNumber" parameterType="String" resultMap="AppUserResult">
        <include refid="selectAppUserVo"/>
        where phone_number = #{phoneNumber} and deleted = '0'
    </select>

    <select id="selectAppUserByPlatformOpenId" parameterType="String" resultMap="AppUserResult">
        <include refid="selectAppUserVo"/>
        where platform_open_id = #{platformOpenId} and deleted = '0'
    </select>

    <select id="checkEmailUnique" resultType="int">
        select count(1) from app_user
        where email = #{email} and deleted = '0'
        <if test="userId != null">
            and user_id != #{userId}
        </if>
        limit 1
    </select>

    <select id="checkPhoneNumberUnique" resultType="int">
        select count(1) from app_user
        where phone_number = #{phoneNumber} and deleted = '0'
        <if test="userId != null">
            and user_id != #{userId}
        </if>
        limit 1
    </select>

    <select id="checkPlatformOpenIdUnique" resultType="int">
        select count(1) from app_user
        where platform_open_id = #{platformOpenId} and deleted = '0'
        <if test="userId != null">
            and user_id != #{userId}
        </if>
        limit 1
    </select>

    <select id="countAppUsers" parameterType="com.ruoyi.common.core.domain.entity.AppUser" resultType="int">
        select count(1) from app_user
        <where>
            <if test="accountType != null and accountType != ''">
                and account_type = #{accountType}
            </if>
            <if test="status != null and status != ''">
                and status = #{status}
            </if>
            <if test="deleted != null and deleted != ''">
                and deleted = #{deleted}
            </if>
            <if test="authType != null and authType != ''">
                and auth_type = #{authType}
            </if>
        </where>
    </select>

    <insert id="insertAppUser" parameterType="com.ruoyi.common.core.domain.entity.AppUser">
        insert into app_user
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">user_id,</if>
            <if test="userTempId != null and userTempId != ''">user_temp_id,</if>
            <if test="nickname != null and nickname != ''">nickname,</if>
            <if test="avatar != null and avatar != ''">avatar,</if>
            <if test="email != null and email != ''">email,</if>
            <if test="phoneNumber != null and phoneNumber != ''">phone_number,</if>
            <if test="accountType != null and accountType != ''">account_type,</if>
            <if test="status != null and status != ''">status,</if>
            <if test="deleted != null and deleted != ''">deleted,</if>
            <if test="authType != null and authType != ''">auth_type,</if>
            <if test="platformOpenId != null and platformOpenId != ''">platform_open_id,</if>
            <if test="credential != null and credential != ''">credential,</if>
            <if test="lastLoginIp != null and lastLoginIp != ''">last_login_ip,</if>
            <if test="lastLoginDate != null">last_login_date,</if>
            <if test="firstDeviceId != null and firstDeviceId != ''">first_device_id,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="remark != null and remark != ''">remark,</if>
            create_time,
            update_time
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">#{userId},</if>
            <if test="userTempId != null and userTempId != ''">#{userTempId},</if>
            <if test="nickname != null and nickname != ''">#{nickname},</if>
            <if test="avatar != null and avatar != ''">#{avatar},</if>
            <if test="email != null and email != ''">#{email},</if>
            <if test="phoneNumber != null and phoneNumber != ''">#{phoneNumber},</if>
            <if test="accountType != null and accountType != ''">#{accountType},</if>
            <if test="status != null and status != ''">#{status},</if>
            <if test="deleted != null and deleted != ''">#{deleted},</if>
            <if test="authType != null and authType != ''">#{authType},</if>
            <if test="platformOpenId != null and platformOpenId != ''">#{platformOpenId},</if>
            <if test="credential != null and credential != ''">#{credential},</if>
            <if test="lastLoginIp != null and lastLoginIp != ''">#{lastLoginIp},</if>
            <if test="lastLoginDate != null">#{lastLoginDate},</if>
            <if test="firstDeviceId != null and firstDeviceId != ''">#{firstDeviceId},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="remark != null and remark != ''">#{remark},</if>
            CURRENT_TIMESTAMP(3),
            CURRENT_TIMESTAMP(3)
        </trim>
    </insert>

    <update id="updateAppUser" parameterType="com.ruoyi.common.core.domain.entity.AppUser">
        update app_user
        <trim prefix="SET" suffixOverrides=",">
            <if test="userTempId != null and userTempId != ''">user_temp_id = #{userTempId},</if>
            <if test="nickname != null and nickname != ''">nickname = #{nickname},</if>
            <if test="avatar != null">avatar = #{avatar},</if>
            <if test="email != null">email = #{email},</if>
            <if test="phoneNumber != null">phone_number = #{phoneNumber},</if>
            <if test="accountType != null and accountType != ''">account_type = #{accountType},</if>
            <if test="status != null and status != ''">status = #{status},</if>
            <if test="deleted != null and deleted != ''">deleted = #{deleted},</if>
            <if test="authType != null and authType != ''">auth_type = #{authType},</if>
            <if test="platformOpenId != null">platform_open_id = #{platformOpenId},</if>
            <if test="credential != null">credential = #{credential},</if>
            <if test="lastLoginIp != null">last_login_ip = #{lastLoginIp},</if>
            <if test="lastLoginDate != null">last_login_date = #{lastLoginDate},</if>
            <if test="firstDeviceId != null">first_device_id = #{firstDeviceId},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            <if test="remark != null">remark = #{remark},</if>
            update_time = CURRENT_TIMESTAMP(3)
        </trim>
        where user_id = #{userId}
    </update>

    <update id="updateLoginInfo" parameterType="com.ruoyi.common.core.domain.entity.AppUser">
        update app_user set 
            last_login_ip = #{lastLoginIp},
            last_login_date = #{lastLoginDate},
            update_time = CURRENT_TIMESTAMP(3)
        where user_id = #{userId}
    </update>

    <delete id="deleteAppUserByUserId" parameterType="Long">
        delete from app_user where user_id = #{userId}
    </delete>

    <delete id="deleteAppUserByUserIds" parameterType="Long">
        delete from app_user where user_id in
        <foreach item="userId" collection="array" open="(" separator="," close=")">
            #{userId}
        </foreach>
    </delete>

    <update id="softDeleteAppUserByUserId" parameterType="Long">
        update app_user set deleted = '1', update_time = CURRENT_TIMESTAMP(3) where user_id = #{userId}
    </update>

    <update id="softDeleteAppUserByUserIds" parameterType="Long">
        update app_user set deleted = '1', update_time = CURRENT_TIMESTAMP(3) where user_id in
        <foreach item="userId" collection="array" open="(" separator="," close=")">
            #{userId}
        </foreach>
    </update>

    <select id="selectByPlatformOpenId" parameterType="String" resultMap="AppUserResult">
        <include refid="selectAppUserVo"/>
        where platform_open_id = #{platformOpenId} and auth_type = #{authType} and deleted = '0'
    </select>

    <select id="selectAppUserByFirstDeviceId" parameterType="String" resultMap="AppUserResult">
        <include refid="selectAppUserVo"/>
        where first_device_id = #{firstDeviceId} and deleted = '0' order by create_time limit 1
    </select>


</mapper>