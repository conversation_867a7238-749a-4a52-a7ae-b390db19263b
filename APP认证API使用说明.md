# DramaRoom APP 认证系统 API 使用说明

## 🎯 系统架构

### 认证方式
- **游客登录**: 基于设备ID的临时访问
- **第三方登录**: 支持Google、Apple等主流平台

### 数据库表结构
- `app_user`: 用户基本信息
- `app_user_auth`: 认证信息（支持多种认证方式）
- `app_user_device`: 设备绑定信息

## 📱 API 接口

### 1. 游客登录
```http
POST /mobile/auth/guest-login
Content-Type: application/json

{
  "deviceId": "unique_device_identifier"
}
```

**响应**:
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "token": "eyJhbGciOiJIUzI1NiJ9..."
  }
}
```

### 2. 第三方登录（Google/Apple）
```http
POST /mobile/auth/third-party-login
Content-Type: application/json

{
  "provider": "google",        // google 或 apple
  "openId": "google_user_id",  // 第三方用户标识
  "accessToken": "access_token_from_google",
  "deviceId": "device_id"
}
```

**响应**:
```json
{
  "code": 200,
  "msg": "操作成功", 
  "data": {
    "token": "eyJhbGciOiJIUzI1NiJ9..."
  }
}
```

### 3. 退出登录
```http
POST /mobile/auth/logout
Authorization: Bearer {token}
```

## 🔐 Token 使用

### 请求头设置
```http
Authorization: Bearer {token}
```

### 在代码中获取用户信息
```java
// 获取当前用户ID
Long userId = AppSecurityUtils.getAppUserId();

// 获取当前用户信息
AppLoginUser user = AppSecurityUtils.getAppLoginUser();

// 判断是否为游客
boolean isGuest = AppSecurityUtils.isGuest();
```

## 🛠️ 配置说明

### Token 配置 (application.yml)
```yaml
token:
  header: Authorization
  secret: abcdefghijklmnopqrstuvwxyz
  expireTime: 30  # 分钟
```

### 过滤器自动排除的路径
- `/mobile/auth/guest-login`
- `/mobile/auth/third-party-login`
- `/mobile/config/app-config`

## 🔧 第三方登录集成

### Google 登录集成
1. 前端使用 Google Sign-In SDK 获取 access_token
2. 调用 `/mobile/auth/third-party-login` 接口
3. 后端可通过 Google API 获取用户信息

### Apple 登录集成
1. 前端使用 Apple Sign-In 获取 ID Token
2. 后端解析 ID Token 获取用户信息
3. 调用认证接口完成登录

## 📊 账户类型说明

- `GUEST`: 游客用户（通过设备ID登录）
- `THIRD_PARTY`: 第三方登录用户（Google、Apple等）

## 🚀 使用流程

1. **首次访问**: 调用游客登录获取基础访问权限
2. **用户注册**: 通过第三方登录升级为正式用户
3. **后续访问**: 使用Token访问需要认证的接口
4. **退出登录**: 清除Token和缓存信息

## ⚠️ 注意事项

- Token 有效期为30分钟，会自动刷新
- 一个用户可以绑定多个设备
- 游客用户和第三方用户的权限可能不同
- 所有移动端接口都需要Token验证（除了登录接口）