package com.ruoyi.web.exception;

import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.exception.AppBusinessException;
import com.ruoyi.common.utils.mobile.AppErrorHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.BindException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import javax.validation.ConstraintViolationException;
import javax.validation.ConstraintViolation;
import java.util.stream.Collectors;

/**
 * APP全局异常处理器
 * 专门处理APP相关的异常，提供统一的错误响应格式
 * 
 * <AUTHOR>
 */
@RestControllerAdvice(basePackages = "com.ruoyi.web.controller.mobile")
public class AppGlobalExceptionHandler
{
    private static final Logger log = LoggerFactory.getLogger(AppGlobalExceptionHandler.class);

    @Autowired
    private AppErrorHandler appErrorHandler;

    /**
     * 处理APP业务异常
     */
    @ExceptionHandler(AppBusinessException.class)
    public R<Object> handleAppBusinessException(AppBusinessException e)
    {
        log.warn("APP业务异常: {}", e.toString());
        return appErrorHandler.handleBusinessException(e);
    }

    /**
     * 处理@Valid参数验证异常（@RequestBody）
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public R<Object> handleMethodArgumentNotValidException(MethodArgumentNotValidException e)
    {
        log.warn("参数验证异常: {}", e.getMessage());
        String message = e.getBindingResult().getFieldError().getDefaultMessage();
        return AppErrorHandler.parameterError(message);
    }

    /**
     * 处理@Validated参数验证异常（@RequestParam）
     */
    @ExceptionHandler(ConstraintViolationException.class)
    public R<Object> handleConstraintViolationException(ConstraintViolationException e)
    {
        log.warn("参数约束验证异常: {}", e.getMessage());
        String message = e.getConstraintViolations().stream()
            .map(ConstraintViolation::getMessage)
            .collect(Collectors.joining(", "));
        return AppErrorHandler.parameterError(message);
    }

    /**
     * 处理参数绑定异常
     */
    @ExceptionHandler(BindException.class)
    public R<Object> handleBindException(BindException e)
    {
        log.warn("参数绑定异常: {}", e.getMessage());
        String message = e.getAllErrors().get(0).getDefaultMessage();
        return AppErrorHandler.parameterError(message);
    }

    /**
     * 处理非法参数异常
     */
    @ExceptionHandler(IllegalArgumentException.class)
    public R<Object> handleIllegalArgumentException(IllegalArgumentException e)
    {
        log.warn("参数异常: {}", e.getMessage());
        return AppErrorHandler.parameterError(e.getMessage());
    }

    /**
     * 处理空指针异常
     */
    @ExceptionHandler(NullPointerException.class)
    public R<Object> handleNullPointerException(NullPointerException e)
    {
        log.error("空指针异常", e);
        return appErrorHandler.handleGeneralException(e);
    }

    /**
     * 处理其他未捕获的异常
     */
    @ExceptionHandler(Exception.class)
    public R<Object> handleGenericException(Exception e)
    {
        log.error("未捕获异常: {}", e.getMessage(), e);
        return appErrorHandler.handleGeneralException(e);
    }
}