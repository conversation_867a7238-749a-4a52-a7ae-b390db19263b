package com.ruoyi.web.config;

import com.ruoyi.web.filter.AppJwtAuthenticationTokenFilter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * APP过滤器配置
 * 
 * <AUTHOR>
 */
@Configuration
public class AppFilterConfig
{
    @Autowired
    private AppJwtAuthenticationTokenFilter appJwtAuthenticationTokenFilter;

    /**
     * 注册APP JWT认证过滤器
     */
    @Bean
    public FilterRegistrationBean<AppJwtAuthenticationTokenFilter> appJwtFilterRegistration()
    {
        FilterRegistrationBean<AppJwtAuthenticationTokenFilter> registration = new FilterRegistrationBean<>();
        registration.setFilter(appJwtAuthenticationTokenFilter);
        registration.addUrlPatterns("/mobile/*");
        registration.setName("appJwtAuthenticationTokenFilter");
        registration.setOrder(1);
        return registration;
    }
}