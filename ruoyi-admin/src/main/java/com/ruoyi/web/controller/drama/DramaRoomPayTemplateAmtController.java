package com.ruoyi.web.controller.drama;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.drama.domain.DramaRoomPayTemplateAmt;
import com.ruoyi.drama.service.IDramaRoomPayTemplateAmtService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 金额模板Controller
 * 
 * <AUTHOR>
 * @date 2025-08-13
 */
@Api(value = "金额模板管理", tags = "金额模板")
@RestController
@RequestMapping("/drama/payTemplateAmt")
public class DramaRoomPayTemplateAmtController extends BaseController<DramaRoomPayTemplateAmt>
{
    @Autowired
    private IDramaRoomPayTemplateAmtService dramaRoomPayTemplateAmtService;

    /**
     * 查询金额模板列表
     */
    @ApiOperation(value = "分页获取金额模板列表")
    @PreAuthorize("@ss.hasPermi('drama:payTemplateAmt:list')")
    @GetMapping("/list")
    public TableDataInfo<DramaRoomPayTemplateAmt> list(DramaRoomPayTemplateAmt dramaRoomPayTemplateAmt)
    {
        startPage();
        List<DramaRoomPayTemplateAmt> list = dramaRoomPayTemplateAmtService.selectDramaRoomPayTemplateAmtList(dramaRoomPayTemplateAmt);
        return getDataTable(list);
    }

    @ApiOperation(value = "获取所有金额模板列表")
    @PreAuthorize("@ss.hasPermi('drama:payTemplateAmt:listAll')")
    @GetMapping("/listAll")
    public AjaxResult listAll()
    {
        List<DramaRoomPayTemplateAmt> list = dramaRoomPayTemplateAmtService.selectDramaRoomPayTemplateAmtList(new DramaRoomPayTemplateAmt());
        return AjaxResult.success(list);
    }

    /**
     * 导出金额模板列表
     */
    @ApiOperation(value = "导出金额模板列表")
    @PreAuthorize("@ss.hasPermi('drama:payTemplateAmt:export')")
    @Log(title = "金额模板", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DramaRoomPayTemplateAmt dramaRoomPayTemplateAmt)
    {
        List<DramaRoomPayTemplateAmt> list = dramaRoomPayTemplateAmtService.selectDramaRoomPayTemplateAmtList(dramaRoomPayTemplateAmt);
        ExcelUtil<DramaRoomPayTemplateAmt> util = new ExcelUtil<DramaRoomPayTemplateAmt>(DramaRoomPayTemplateAmt.class);
        util.exportExcel(response, list, "金额模板数据");
    }

    /**
     * 获取金额模板详细信息
     */
    @ApiOperation(value = "获取金额模板详细信息")
    @PreAuthorize("@ss.hasPermi('drama:payTemplateAmt:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(dramaRoomPayTemplateAmtService.selectDramaRoomPayTemplateAmtById(id));
    }

    /**
     * 新增金额模板
     */
    @ApiOperation(value = "新增金额模板")
    @PreAuthorize("@ss.hasPermi('drama:payTemplateAmt:add')")
    @Log(title = "金额模板", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DramaRoomPayTemplateAmt dramaRoomPayTemplateAmt)
    {
        return toAjax(dramaRoomPayTemplateAmtService.insertDramaRoomPayTemplateAmt(dramaRoomPayTemplateAmt));
    }

    /**
     * 修改金额模板
     */
    @ApiOperation(value = "修改金额模板")
    @PreAuthorize("@ss.hasPermi('drama:payTemplateAmt:edit')")
    @Log(title = "金额模板", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DramaRoomPayTemplateAmt dramaRoomPayTemplateAmt)
    {
        return toAjax(dramaRoomPayTemplateAmtService.updateDramaRoomPayTemplateAmt(dramaRoomPayTemplateAmt));
    }

    /**
     * 删除金额模板
     */
    @ApiOperation(value = "删除金额模板")
    @PreAuthorize("@ss.hasPermi('drama:payTemplateAmt:remove')")
    @Log(title = "金额模板", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(dramaRoomPayTemplateAmtService.deleteDramaRoomPayTemplateAmtByIds(ids));
    }
}