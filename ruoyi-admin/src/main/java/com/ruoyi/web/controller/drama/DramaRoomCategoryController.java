package com.ruoyi.web.controller.drama;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.drama.domain.DramaRoomCategory;
import com.ruoyi.drama.service.IDramaRoomCategoryService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 分类Controller
 *
 * <AUTHOR>
 * @date 2025-07-03
 */
@Api(tags = "短剧分类")
@RestController
@RequestMapping("/drama/category")
public class DramaRoomCategoryController extends BaseController<DramaRoomCategory> {
    @Autowired
    private IDramaRoomCategoryService dramaRoomCategoryService;

    /**
     * 查询分类列表
     */
    @ApiOperation("获取分类列表")
    @PreAuthorize("@ss.hasPermi('system:category:list')")
    @GetMapping("/list")
    public TableDataInfo<DramaRoomCategory> list(DramaRoomCategory dramaRoomCategory) {
        startPage();
        List<DramaRoomCategory> list = dramaRoomCategoryService.selectDramaRoomCategoryList(dramaRoomCategory);
        return getDataTable(list);
    }

    /**
     * 导出分类列表
     */
    @PreAuthorize("@ss.hasPermi('system:category:export')")
    @Log(title = "分类", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DramaRoomCategory dramaRoomCategory) {
        List<DramaRoomCategory> list = dramaRoomCategoryService.selectDramaRoomCategoryList(dramaRoomCategory);
        ExcelUtil<DramaRoomCategory> util = new ExcelUtil<DramaRoomCategory>(DramaRoomCategory.class);
        util.exportExcel(response, list, "分类数据");
    }

    /**
     * 获取分类详细信息
     */
    @ApiOperation("获取分类详细信息")
    @PreAuthorize("@ss.hasPermi('system:category:query')")
    @GetMapping(value = "/{id}")
    public R<DramaRoomCategory> getInfo(@PathVariable("id") Long id) {
        return R.ok(dramaRoomCategoryService.selectDramaRoomCategoryById(id));
    }

    /**
     * 新增分类
     */
    @ApiOperation("新增分类")
    @PreAuthorize("@ss.hasPermi('system:category:add')")
    @Log(title = "分类", businessType = BusinessType.INSERT)
    @PostMapping
    public R<Integer> add(@RequestBody DramaRoomCategory dramaRoomCategory) {
        return R.toR(dramaRoomCategoryService.insertDramaRoomCategory(dramaRoomCategory));
    }

    /**
     * 修改分类
     */
    @ApiOperation("修改分类")
    @PreAuthorize("@ss.hasPermi('system:category:edit')")
    @Log(title = "分类", businessType = BusinessType.UPDATE)
    @PutMapping
    public R<Integer> edit(@RequestBody DramaRoomCategory dramaRoomCategory) {
        return R.toR(dramaRoomCategoryService.updateDramaRoomCategory(dramaRoomCategory));
    }

    /**
     * 删除分类
     */
    @ApiOperation("删除分类")
    @PreAuthorize("@ss.hasPermi('system:category:remove')")
    @Log(title = "分类", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Integer> remove(@PathVariable Long[] ids) {
        return R.toR(dramaRoomCategoryService.deleteDramaRoomCategoryByIds(ids));
    }

    /**
     * 获取所有分类
     */
    @ApiOperation("获取所有分类")
    @PreAuthorize("@ss.hasPermi('system:category:listAll')")
    @GetMapping("/listAll")
    public R<List<DramaRoomCategory>> listAll() {
        List<DramaRoomCategory> list = dramaRoomCategoryService.selectDramaRoomCategoryList(new DramaRoomCategory());
        return R.ok(list);
    }
}
