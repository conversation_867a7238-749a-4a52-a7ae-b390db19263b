package com.ruoyi.web.controller.drama;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.core.domain.R;
import com.ruoyi.drama.domain.DramaRoomVideo;
import com.ruoyi.drama.service.IDramaRoomVideoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

import static com.ruoyi.common.core.domain.R.toR;

/**
 * 剧集Controller
 *
 * <AUTHOR>
 * @date 2025-07-04
 */
@Api(tags = "剧集接口")
@RestController
@RequestMapping("/drama/video")
public class DramaRoomVideoController extends BaseController<DramaRoomVideo>
{
    @Autowired
    private IDramaRoomVideoService dramaRoomVideoService;

    /**
     * 查询剧集列表
     */
    @ApiOperation("获取剧集列表")
    @PreAuthorize("@ss.hasPermi('system:video:list')")
    @GetMapping("/list")
    public TableDataInfo<DramaRoomVideo> list(DramaRoomVideo dramaRoomVideo)
    {
        startPage();
        List<DramaRoomVideo> list = dramaRoomVideoService.selectDramaRoomVideoList(dramaRoomVideo);
        return getDataTable(list);
    }

    /**
     * 导出剧集列表
     */
    @ApiOperation("导出剧集列表")
    @PreAuthorize("@ss.hasPermi('system:video:export')")
    @Log(title = "剧集", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DramaRoomVideo dramaRoomVideo)
    {
        List<DramaRoomVideo> list = dramaRoomVideoService.selectDramaRoomVideoList(dramaRoomVideo);
        ExcelUtil<DramaRoomVideo> util = new ExcelUtil<DramaRoomVideo>(DramaRoomVideo.class);
        util.exportExcel(response, list, "剧集数据");
    }

    /**
     * 获取剧集详细信息
     */
    @ApiOperation("获取剧集详细信息")
    @PreAuthorize("@ss.hasPermi('system:video:query')")
    @GetMapping(value = "/{id}")
    public R<DramaRoomVideo> getInfo(@PathVariable("id") Long id)
    {
        return R.ok(dramaRoomVideoService.selectDramaRoomVideoById(id));
    }

    /**
     * 新增剧集
     */
    @ApiOperation("新增剧集")
    @PreAuthorize("@ss.hasPermi('system:video:add')")
    @Log(title = "剧集", businessType = BusinessType.INSERT)
    @PostMapping
    public R<Integer> add(@RequestBody DramaRoomVideo dramaRoomVideo)
    {
        return R.toR(dramaRoomVideoService.insertDramaRoomVideo(dramaRoomVideo));
    }

    /**
     * 修改剧集
     */
    @ApiOperation("修改剧集")
    @PreAuthorize("@ss.hasPermi('system:video:edit')")
    @Log(title = "剧集", businessType = BusinessType.UPDATE)
    @PutMapping
    public R<Integer> edit(@RequestBody DramaRoomVideo dramaRoomVideo)
    {
        return R.toR(dramaRoomVideoService.updateDramaRoomVideo(dramaRoomVideo));
    }

    /**
     * 删除剧集
     */
    @ApiOperation("删除剧集")
    @PreAuthorize("@ss.hasPermi('system:video:remove')")
    @Log(title = "剧集", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public R<Integer> remove(@PathVariable Long[] ids)
    {
        return R.toR(dramaRoomVideoService.deleteDramaRoomVideoByIds(ids));
    }

    @ApiOperation("根据movieId获取剧集列表")
    @GetMapping("/getByMovieId/{movieId}")
    public R<List<DramaRoomVideo>> getByMovieId(@PathVariable("movieId") Long movieId)
    {
        return R.ok(dramaRoomVideoService.getVideoListByMovieId(movieId));
    }
}
