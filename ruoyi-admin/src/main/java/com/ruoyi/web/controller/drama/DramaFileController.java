package com.ruoyi.web.controller.drama;

import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.drama.service.impl.S3StorageService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

@RestController
@RequestMapping("/drama/file")
public class DramaFileController {


    private static final Logger log = LoggerFactory.getLogger(DramaFileController.class);

    @Autowired
    private S3StorageService s3StorageService;

    @PostMapping("/upload")
    public AjaxResult uploadFileToS3(@RequestParam("file") MultipartFile file) {
        try {
            if (file.isEmpty()) {
                return AjaxResult.error("请选择要上传的文件");
            }

            String url = s3StorageService.uploadFile(file);
            if (StringUtils.isEmpty(url)) {
                return AjaxResult.error("文件上传失败");
            }
            return AjaxResult.success("文件上传成功").put("url", url);
        } catch (Exception e) {
            log.error("文件上传失败", e);
            return AjaxResult.error(e.getMessage());
        }
    }
}
