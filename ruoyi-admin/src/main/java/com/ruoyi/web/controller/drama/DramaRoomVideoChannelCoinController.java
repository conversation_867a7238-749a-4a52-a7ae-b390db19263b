package com.ruoyi.web.controller.drama;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.drama.domain.DramaRoomVideoChannelCoin;
import com.ruoyi.drama.service.IDramaRoomVideoChannelCoinService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 视频渠道扣金币配置Controller
 * 
 * <AUTHOR>
 * @date 2025-08-05
 */
@RestController
@RequestMapping("/drama/videoCoin")
public class DramaRoomVideoChannelCoinController extends BaseController<DramaRoomVideoChannelCoin>
{
    @Autowired
    private IDramaRoomVideoChannelCoinService dramaRoomVideoChannelCoinService;

    /**
     * 查询视频渠道扣金币配置列表
     */
    @ApiModelProperty("分页获取视频渠道扣金币配置列表")
    @PreAuthorize("@ss.hasPermi('drama:videoCoin:list')")
    @GetMapping("/list")
    public TableDataInfo<DramaRoomVideoChannelCoin> list(DramaRoomVideoChannelCoin dramaRoomVideoChannelCoin)
    {
        startPage();
        List<DramaRoomVideoChannelCoin> list = dramaRoomVideoChannelCoinService.selectDramaRoomVideoChannelCoinList(dramaRoomVideoChannelCoin);
        return getDataTable(list);
    }

    /**
     * 导出视频渠道扣金币配置列表
     */
    @ApiOperation("导出视频渠道扣金币配置列表")
    @PreAuthorize("@ss.hasPermi('drama:videoCoin:export')")
    @Log(title = "视频渠道扣金币配置", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DramaRoomVideoChannelCoin dramaRoomVideoChannelCoin)
    {
        List<DramaRoomVideoChannelCoin> list = dramaRoomVideoChannelCoinService.selectDramaRoomVideoChannelCoinList(dramaRoomVideoChannelCoin);
        ExcelUtil<DramaRoomVideoChannelCoin> util = new ExcelUtil<DramaRoomVideoChannelCoin>(DramaRoomVideoChannelCoin.class);
        util.exportExcel(response, list, "视频渠道扣金币配置数据");
    }

    /**
     * 获取视频渠道扣金币配置详细信息
     */
    @ApiOperation("获取视频渠道扣金币配置详细信息")
    @PreAuthorize("@ss.hasPermi('drama:videoCoin:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(dramaRoomVideoChannelCoinService.selectDramaRoomVideoChannelCoinById(id));
    }

    /**
     * 新增视频渠道扣金币配置
     */
    @ApiOperation("新增视频渠道扣金币配置")
    @PreAuthorize("@ss.hasPermi('drama:videoCoin:add')")
    @Log(title = "视频渠道扣金币配置", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DramaRoomVideoChannelCoin dramaRoomVideoChannelCoin)
    {
        return toAjax(dramaRoomVideoChannelCoinService.insertDramaRoomVideoChannelCoin(dramaRoomVideoChannelCoin));
    }

    /**
     * 修改视频渠道扣金币配置
     */
    @ApiOperation("修改视频渠道扣金币配置")
    @PreAuthorize("@ss.hasPermi('drama:videoCoin:edit')")
    @Log(title = "视频渠道扣金币配置", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DramaRoomVideoChannelCoin dramaRoomVideoChannelCoin)
    {
        return toAjax(dramaRoomVideoChannelCoinService.updateDramaRoomVideoChannelCoin(dramaRoomVideoChannelCoin));
    }

    /**
     * 删除视频渠道扣金币配置
     */
    @ApiOperation("删除视频渠道扣金币配置")
    @PreAuthorize("@ss.hasPermi('drama:videoCoin:remove')")
    @Log(title = "视频渠道扣金币配置", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(dramaRoomVideoChannelCoinService.deleteDramaRoomVideoChannelCoinByIds(ids));
    }
}
