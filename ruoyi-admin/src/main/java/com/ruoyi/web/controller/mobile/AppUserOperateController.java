package com.ruoyi.web.controller.mobile;

import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.drama.dto.DramaRoomUserCollectDTO;
import com.ruoyi.common.drama.dto.DramaRoomUserHistoryDTO;
import com.ruoyi.common.drama.vo.MovieVO;
import com.ruoyi.common.drama.vo.UserCollectVO;
import com.ruoyi.common.drama.vo.UserHistoryVO;
import com.ruoyi.drama.service.IDramaRoomUserCollectService;
import com.ruoyi.drama.service.IDramaRoomUserHistoryService;
import com.ruoyi.drama.service.IDramaRoomUserLikeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Api(tags = "app用户操作相关接口")
@RestController
@RequestMapping("/mobile/operate")
public class AppUserOperateController extends BaseController<MovieVO> {

    @Autowired
    private IDramaRoomUserCollectService dramaRoomUserCollectService;

    @Autowired
    private IDramaRoomUserHistoryService dramaRoomUserHistoryService;

    @Autowired
    private IDramaRoomUserLikeService dramaRoomUserLikeService;

    //短剧收藏接口
    @ApiOperation("收藏短剧")
    @PostMapping("/collect")
    public R<Integer> collect(@RequestBody DramaRoomUserCollectDTO dramaRoomUserFavoriteDTO) {
        return R.toR(dramaRoomUserCollectService.collectMovie(dramaRoomUserFavoriteDTO));
    }

    //短剧收藏记录查看接口
    @ApiOperation("查看用户短剧收藏记录")
    @GetMapping("/collect/list")
    public R<List<UserCollectVO>> collectList(@RequestHeader("languageCode") String languageCode) {
        return R.ok(dramaRoomUserCollectService.getUserCollect(languageCode));
    }

    //删除短剧收藏记录
    @ApiOperation("删除用户短剧收藏记录")
    @DeleteMapping("/collect/delete/{ids}")
    public R<Integer> deleteCollect(@PathVariable Long[] ids){
        return R.toR(dramaRoomUserCollectService.deleteDramaRoomUserCollectByIds(ids));
    }

    //上报短剧浏览历史记录
    @ApiOperation("上报短剧浏览历史记录")
    @PostMapping("/history")
    public R<Integer> reportViewHistory(@RequestBody DramaRoomUserHistoryDTO dramaRoomUserHistory) {
        return R.toR(dramaRoomUserHistoryService.reportViewHistory(dramaRoomUserHistory));
    }

    //查看用户浏览记录
    @ApiOperation("查看用户浏览记录")
    @GetMapping("/history/list")
    public R<List<UserHistoryVO>> getUserHistory(@RequestHeader("languageCode") String languageCode) {
        return R.ok(dramaRoomUserHistoryService.getUserHistory(languageCode));
    }

    //删除用户浏览记录
    @ApiOperation("删除用户浏览记录")
    @DeleteMapping("/history/delete/{ids}")
    public R<Integer> deleteHistory(@PathVariable Long[] ids){
        return R.toR(dramaRoomUserHistoryService.deleteDramaRoomUserHistoryByIds(ids));
    }


    @ApiOperation("用户点赞剧集")
    @GetMapping("/like")
    public R<Integer> like(Long videoId) {
        return R.toR(dramaRoomUserLikeService.likeVideo(videoId));
    }
}
