package com.ruoyi.web.controller.mobile;

import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.drama.dto.DramaRoomUserHistoryDTO;
import com.ruoyi.common.drama.dto.UserCoinDTO;
import com.ruoyi.common.drama.vo.*;
import com.ruoyi.drama.domain.AppUserCoinLog;
import com.ruoyi.drama.service.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.List;

@Api(tags = "充值金币相关接口")
@RestController
@RequestMapping("/mobile/coin")
public class UserCoinController extends BaseController<CoinRuleVO> {

    @Autowired
    private IDramaRoomCoinRuleService iDramaRoomCoinRuleService;

    @Autowired
    private IAppUserCoinService appUserCoinService;

    @Autowired
    private IUserCoinService iUserCoinService;

    @Autowired
    private IAppUserCoinLogService iAppUserCoinLogService;

    @Autowired
    private IDramaRoomUserUnlockVideoService dramaRoomUserUnlockVideoService;


    //查询充值规则
    @ApiOperation("查询充值规则")
    @GetMapping("/getCoinRulelist")
    public R<List<CoinRuleVO>> getCoinRulelist(@RequestParam Integer type) {
        Integer reType = (type != null) ? type : 0;
        return R.ok(iDramaRoomCoinRuleService.getCoinRuleList(reType));
    }

    //充值
    @ApiOperation("充值")
    @PostMapping("/recharge")
    public R<Integer> recharge(@RequestBody UserCoinDTO userCoinDTO) {
        return R.toR(iUserCoinService.recharge(userCoinDTO));
    }

    //查询用户金币
    @ApiOperation("查询用户金币")
    @GetMapping("/getUserCoinByUserId")
    public R<UserCoinVO> getUserCoinByUserId() {
        return R.ok(appUserCoinService.getUserCoinByUserId(null));
    }

    //充值记录
    @ApiOperation("充值记录")
    @GetMapping("/recharge-record")
    public R<List<UserCoinLogTradeVO>> rechargeRecord() {
        return R.ok(iAppUserCoinLogService.rechargeRecord());
    }

    //奖励记录
    @ApiOperation("奖励记录")
    @GetMapping("/reward-records")
    public R<List<UserCoinLogTradeVO>> rewardRecords() {
        return R.ok(iAppUserCoinLogService.rewardRecords());
    }

    //解锁剧集记录
    @ApiOperation("解锁剧集记录")
    @GetMapping("/unlock-episode-records")
    public R<List<UnlockEpisodeRecordsVO>> unlockEpisodeRecords() {
        return R.ok(dramaRoomUserUnlockVideoService.unlockEpisodeRecords());
    }

    //查询本周签到
    @ApiOperation("查询本周签到")
    @GetMapping("/getSignInByUserId")
    public R<List<UserCoinLogVO>> getSignInByUserId() {
        return R.ok(iUserCoinService.getSignInByUserId());
    }

    //用户福利
    @ApiOperation("用户福利-type类型：2.自定义签到,3.观看时长")
    @GetMapping("/getUserBenefits")
    public R<List<SignInRuleVO>> getUserBenefits(@RequestParam Integer type) {
        return R.ok(iUserCoinService.getUserBenefits(type));
    }


    //签到
    @ApiOperation("签到")
    @PostMapping("/signIn")
    public R<Integer> signIn(@RequestBody UserCoinDTO userCoinDTO){
        return R.toR(iUserCoinService.signIn(userCoinDTO));
    }


}
