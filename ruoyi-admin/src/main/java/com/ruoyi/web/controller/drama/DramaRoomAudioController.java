package com.ruoyi.web.controller.drama;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.drama.domain.DramaRoomAudio;
import com.ruoyi.drama.service.IDramaRoomAudioService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 音讯Controller
 *
 * <AUTHOR>
 * @date 2025-07-28
 */
@Api(tags = "音讯")
@RestController
@RequestMapping("/drama/audio")
public class DramaRoomAudioController extends BaseController
{
    @Autowired
    private IDramaRoomAudioService dramaRoomAudioService;

    /**
     * 查询音讯列表
     */
    @ApiOperation("查询音讯列表")
    @PreAuthorize("@ss.hasPermi('system:audio:list')")
    @GetMapping("/list")
    public TableDataInfo list(DramaRoomAudio dramaRoomAudio)
    {
        startPage();
        List<DramaRoomAudio> list = dramaRoomAudioService.selectDramaRoomAudioList(dramaRoomAudio);
        return getDataTable(list);
    }

    /**
     * 导出音讯列表
     */
    @ApiOperation("导出音讯列表")
    @PreAuthorize("@ss.hasPermi('system:audio:export')")
    @Log(title = "音讯", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DramaRoomAudio dramaRoomAudio)
    {
        List<DramaRoomAudio> list = dramaRoomAudioService.selectDramaRoomAudioList(dramaRoomAudio);
        ExcelUtil<DramaRoomAudio> util = new ExcelUtil<DramaRoomAudio>(DramaRoomAudio.class);
        util.exportExcel(response, list, "音讯数据");
    }

    /**
     * 获取音讯详细信息
     */
    @ApiOperation("获取音讯详细信息")
    @PreAuthorize("@ss.hasPermi('system:audio:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(dramaRoomAudioService.selectDramaRoomAudioById(id));
    }

    /**
     * 新增音讯
     */
    @ApiOperation("新增音讯")
    @PreAuthorize("@ss.hasPermi('system:audio:add')")
    @Log(title = "音讯", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DramaRoomAudio dramaRoomAudio)
    {
        return toAjax(dramaRoomAudioService.insertDramaRoomAudio(dramaRoomAudio));
    }

    /**
     * 修改音讯
     */
    @ApiOperation("修改音讯")
    @PreAuthorize("@ss.hasPermi('system:audio:edit')")
    @Log(title = "音讯", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DramaRoomAudio dramaRoomAudio)
    {
        return toAjax(dramaRoomAudioService.updateDramaRoomAudio(dramaRoomAudio));
    }

    /**
     * 删除音讯
     */
    @ApiOperation("删除音讯")
    @PreAuthorize("@ss.hasPermi('system:audio:remove')")
    @Log(title = "音讯", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(dramaRoomAudioService.deleteDramaRoomAudioByIds(ids));
    }
}
