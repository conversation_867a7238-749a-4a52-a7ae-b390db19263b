package com.ruoyi.web.controller.drama;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.core.domain.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.drama.domain.DramaRoomMovie;
import com.ruoyi.drama.service.IDramaRoomMovieService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 短剧Controller
 *
 * <AUTHOR>
 * @date 2025-07-04
 */
@Api(tags = "短剧相关接口")
@RestController
@RequestMapping("/drama/movie")
public class DramaRoomMovieController extends BaseController<DramaRoomMovie>
{
    @Autowired
    private IDramaRoomMovieService dramaRoomMovieService;

    /**
     * 查询短剧列表
     */
    @ApiOperation("获取短剧列表")
    @PreAuthorize("@ss.hasPermi('system:movie:list')")
    @GetMapping("/list")
    public TableDataInfo<DramaRoomMovie> list(DramaRoomMovie dramaRoomMovie)
    {
        startPage();
        List<DramaRoomMovie> list = dramaRoomMovieService.selectDramaRoomMovieList(dramaRoomMovie);
        return getDataTable(list);
    }

    /**
     * 导出短剧列表
     */
    @ApiOperation("导出短剧列表")
    @PreAuthorize("@ss.hasPermi('system:movie:export')")
    @Log(title = "短剧", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DramaRoomMovie dramaRoomMovie)
    {
        List<DramaRoomMovie> list = dramaRoomMovieService.selectDramaRoomMovieList(dramaRoomMovie);
        ExcelUtil<DramaRoomMovie> util = new ExcelUtil<DramaRoomMovie>(DramaRoomMovie.class);
        util.exportExcel(response, list, "短剧数据");
    }

    /**
     * 获取短剧详细信息
     */
    @ApiOperation("获取短剧详细信息")
    @PreAuthorize("@ss.hasPermi('system:movie:query')")
    @GetMapping(value = "/{id}")
    public R<DramaRoomMovie> getInfo(@PathVariable("id") Long id)
    {
        return R.ok(dramaRoomMovieService.selectDramaRoomMovieById(id));
    }

    /**
     * 新增短剧
     */
    @ApiOperation("新增短剧")
    @PreAuthorize("@ss.hasPermi('system:movie:add')")
    @Log(title = "短剧", businessType = BusinessType.INSERT)
    @PostMapping
    public R<Integer> add(@RequestBody DramaRoomMovie dramaRoomMovie)
    {
        return R.toR(dramaRoomMovieService.insertDramaRoomMovie(dramaRoomMovie));
    }

    /**
     * 修改短剧
     */
    @ApiOperation("修改短剧")
    @PreAuthorize("@ss.hasPermi('system:movie:edit')")
    @Log(title = "短剧", businessType = BusinessType.UPDATE)
    @PutMapping
    public R<Integer> edit(@RequestBody DramaRoomMovie dramaRoomMovie)
    {
        return R.toR(dramaRoomMovieService.updateDramaRoomMovie(dramaRoomMovie));
    }

    /**
     * 删除短剧
     */
    @ApiOperation("删除短剧")
    @PreAuthorize("@ss.hasPermi('system:movie:remove')")
    @Log(title = "短剧", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public R<Integer> remove(@PathVariable Long[] ids)
    {
        return R.toR(dramaRoomMovieService.deleteDramaRoomMovieByIds(ids));
    }
}
