package com.ruoyi.web.controller.mobile;


import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.drama.dto.DramaRoomUserCollectDTO;
import com.ruoyi.common.drama.vo.MovieCategoryVO;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.drama.domain.DramaRoomFeedback;
import com.ruoyi.drama.service.IDramaRoomFeedbackService;
import com.ruoyi.drama.service.IDramaRoomParamConfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 用户反馈接口
 *
 * <AUTHOR>
 */
@Api(tags = "用户反馈接口")
@RestController
@RequestMapping("/mobile/feedback")
public class FeedbackController {

    @Autowired
    private IDramaRoomFeedbackService feedbackService;

    //新增用户反馈
    @ApiOperation("新增用户反馈")
    @PostMapping("/add")
    public R<Integer> collect(@RequestBody DramaRoomFeedback DramaRoomFeedback) {
        Long userId = SecurityUtils.getUserId();
        DramaRoomFeedback.setUserId(userId);
        return R.toR(feedbackService.insertDramaRoomFeedback(DramaRoomFeedback));
    }
}
