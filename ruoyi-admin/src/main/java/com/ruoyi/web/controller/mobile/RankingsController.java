package com.ruoyi.web.controller.mobile;

import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.drama.vo.MovieVO;
import com.ruoyi.drama.service.IDramaRoomMovieService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 排行榜接口
 *
 * <AUTHOR>
 */
@Api(tags = "排行榜接口")
@RestController
@RequestMapping("/mobile/rankings")
public class RankingsController {

    @Autowired
    private IDramaRoomMovieService dramaRoomMovieService;

    /**
     * 获取热播榜短剧
     */
    @ApiOperation("获取热播榜短剧")
    @ApiImplicitParam(name = "Authorization", value = "授权token", required = true, paramType = "header")
    @GetMapping("/popular")
    public R<List<MovieVO>> getHotRecommend(@RequestHeader("languageCode") String languageCode) {
        List<MovieVO> hotRecommend = dramaRoomMovieService.getHotRecommend(languageCode);
        return R.ok(hotRecommend);
    }

    /**
     * 获取热搜榜短剧
     */
    @ApiOperation("获取热搜榜短剧")
    @ApiImplicitParam(name = "Authorization", value = "授权token", required = true, paramType = "header")
    @GetMapping("/search")
    public R<List<MovieVO>> getHotSearch(@RequestHeader("languageCode") String languageCode) {
        List<MovieVO> hotSearch = dramaRoomMovieService.getHotSearch(languageCode);
        return R.ok(hotSearch);
    }

    /**
     * 获取新番榜短剧
     */
    @ApiOperation("获取新番榜短剧")
    @ApiImplicitParam(name = "Authorization", value = "授权token", required = true, paramType = "header")
    @GetMapping("/new")
    public R<List<MovieVO>> getNew(@RequestHeader("languageCode") String languageCode) {
        List<MovieVO> newMovies = dramaRoomMovieService.getNewList(languageCode);
        return R.ok(newMovies);
    }
}
