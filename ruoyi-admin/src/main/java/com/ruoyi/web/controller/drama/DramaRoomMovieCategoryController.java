package com.ruoyi.web.controller.drama;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.drama.domain.DramaRoomMovie;
import com.ruoyi.drama.domain.DramaRoomMovieCategory;
import com.ruoyi.drama.domain.vo.DramaRoomMovieBindCategoryVO;
import com.ruoyi.drama.service.IDramaRoomMovieCategoryService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 【请填写功能名称】Controller
 *
 * <AUTHOR>
 * @date 2025-07-04
 */
@Api(tags = "短剧分类关联接口")
@RestController
@RequestMapping("/drama/movieCategory")
public class DramaRoomMovieCategoryController extends BaseController<DramaRoomMovieCategory> {
    @Autowired
    private IDramaRoomMovieCategoryService dramaRoomMovieCategoryService;

    /**
     * 查询【请填写功能名称】列表
     */
    @ApiOperation("获取分类列表")
    @PreAuthorize("@ss.hasPermi('system:category:list')")
    @GetMapping("/list")
    public TableDataInfo<DramaRoomMovieCategory> list(DramaRoomMovieCategory dramaRoomMovieCategory) {
        startPage();
        List<DramaRoomMovieCategory> list = dramaRoomMovieCategoryService.selectDramaRoomMovieCategoryList(dramaRoomMovieCategory);
        return getDataTable(list);
    }

    /**
     * 导出【请填写功能名称】列表
     */
    @ApiOperation("导出分类列表")
    @PreAuthorize("@ss.hasPermi('system:category:export')")
    @Log(title = "【请填写功能名称】", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DramaRoomMovieCategory dramaRoomMovieCategory) {
        List<DramaRoomMovieCategory> list = dramaRoomMovieCategoryService.selectDramaRoomMovieCategoryList(dramaRoomMovieCategory);
        ExcelUtil<DramaRoomMovieCategory> util = new ExcelUtil<DramaRoomMovieCategory>(DramaRoomMovieCategory.class);
        util.exportExcel(response, list, "【请填写功能名称】数据");
    }

    /**
     * 获取【请填写功能名称】详细信息
     */
    @ApiOperation("获取分类详细信息")
    @PreAuthorize("@ss.hasPermi('system:category:query')")
    @GetMapping(value = "/{id}")
    public R<DramaRoomMovieCategory> getInfo(@PathVariable("id") Long id) {
        return R.ok(dramaRoomMovieCategoryService.selectDramaRoomMovieCategoryById(id));
    }

    /**
     * 新增【请填写功能名称】
     */
    @ApiOperation("新增分类")
    @PreAuthorize("@ss.hasPermi('system:category:add')")
    @Log(title = "【请填写功能名称】", businessType = BusinessType.INSERT)
    @PostMapping
    public R<Integer> add(@RequestBody DramaRoomMovieCategory dramaRoomMovieCategory) {
        return R.toR(dramaRoomMovieCategoryService.insertDramaRoomMovieCategory(dramaRoomMovieCategory));
    }

    /**
     * 修改【请填写功能名称】
     */
    @ApiOperation("修改分类")
    @PreAuthorize("@ss.hasPermi('system:category:edit')")
    @Log(title = "【请填写功能名称】", businessType = BusinessType.UPDATE)
    @PutMapping
    public R<Integer> edit(@RequestBody DramaRoomMovieCategory dramaRoomMovieCategory) {
        return R.toR(dramaRoomMovieCategoryService.updateDramaRoomMovieCategory(dramaRoomMovieCategory));
    }

    /**
     * 删除【请填写功能名称】
     */
    @ApiOperation("删除分类")
    @PreAuthorize("@ss.hasPermi('system:category:remove')")
    @Log(title = "【请填写功能名称】", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Integer> remove(@PathVariable Long[] ids) {
        return R.toR(dramaRoomMovieCategoryService.deleteDramaRoomMovieCategoryByIds(ids));
    }

    /**
     * 根据分类id查询所有绑定短剧
     */
    @ApiOperation("根据分类id查询所有绑定短剧")
    @PreAuthorize("@ss.hasPermi('system:category:movieList')")
    @GetMapping("/list/movies/{categoryId}")
    public R<List<DramaRoomMovieBindCategoryVO>> list(@PathVariable Long categoryId) {
        return R.ok(dramaRoomMovieCategoryService.selectDramaRoomMovieCategoryListByCategoryId(categoryId));
    }

    /**
     * 修改分类绑定短剧数据
     */
    @ApiOperation("修改分类绑定短剧数据")
    @PreAuthorize("@ss.hasPermi('system:category:movieUpdate')")
    @PutMapping("/bind/movies/{categoryId}")
    public R<String> updateBindMovies(@PathVariable("categoryId") Long categoryId, @RequestBody List<Long> movieIds) {
        dramaRoomMovieCategoryService.updateBindMovies(categoryId, movieIds);
        return R.ok("绑定成功");
    }
}
