package com.ruoyi.web.controller.drama;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.drama.domain.AppUserCoinLog;
import com.ruoyi.drama.service.IAppUserCoinLogService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 金币记录Controller
 *
 * <AUTHOR>
 * @date 2025-07-29
 */
@Api(tags = "金币记录")
@RestController
@RequestMapping("/drama/userCoinLog")
public class AppUserCoinLogController extends BaseController
{
    @Autowired
    private IAppUserCoinLogService appUserCoinLogService;

    /**
     * 查询金币记录列表
     */
    @ApiOperation("查询金币记录列表")
    @PreAuthorize("@ss.hasPermi('system:log:list')")
    @GetMapping("/list")
    public TableDataInfo list(AppUserCoinLog appUserCoinLog)
    {
        startPage();
        List<AppUserCoinLog> list = appUserCoinLogService.selectAppUserCoinLogList(appUserCoinLog);
        return getDataTable(list);
    }

    /**
     * 导出金币记录列表
     */
    @ApiOperation("导出金币记录列表")
    @PreAuthorize("@ss.hasPermi('system:log:export')")
    @Log(title = "金币记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, AppUserCoinLog appUserCoinLog)
    {
        List<AppUserCoinLog> list = appUserCoinLogService.selectAppUserCoinLogList(appUserCoinLog);
        ExcelUtil<AppUserCoinLog> util = new ExcelUtil<AppUserCoinLog>(AppUserCoinLog.class);
        util.exportExcel(response, list, "金币记录数据");
    }

    /**
     * 获取金币记录详细信息
     */
    @ApiOperation("获取金币记录详细信息")
    @PreAuthorize("@ss.hasPermi('system:log:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(appUserCoinLogService.selectAppUserCoinLogById(id));
    }

    /**
     * 新增金币记录
     */
    @ApiOperation("新增金币记录")
    @PreAuthorize("@ss.hasPermi('system:log:add')")
    @Log(title = "金币记录", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody AppUserCoinLog appUserCoinLog)
    {
        return toAjax(appUserCoinLogService.insertAppUserCoinLog(appUserCoinLog));
    }

    /**
     * 修改金币记录
     */
    @ApiOperation("修改金币记录")
    @PreAuthorize("@ss.hasPermi('system:log:edit')")
    @Log(title = "金币记录", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody AppUserCoinLog appUserCoinLog)
    {
        return toAjax(appUserCoinLogService.updateAppUserCoinLog(appUserCoinLog));
    }

    /**
     * 删除金币记录
     */
    @ApiOperation("删除金币记录")
    @PreAuthorize("@ss.hasPermi('system:log:remove')")
    @Log(title = "金币记录", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(appUserCoinLogService.deleteAppUserCoinLogByIds(ids));
    }
}
