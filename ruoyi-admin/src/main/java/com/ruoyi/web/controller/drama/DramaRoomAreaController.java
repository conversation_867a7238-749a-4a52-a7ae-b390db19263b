package com.ruoyi.web.controller.drama;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.drama.domain.DramaRoomArea;
import com.ruoyi.drama.service.IDramaRoomAreaService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 短剧地区Controller
 *
 * <AUTHOR>
 * @date 2025-07-28
 */
@Api(tags = "短剧地区")
@RestController
@RequestMapping("/drama/area")
public class DramaRoomAreaController extends BaseController
{
    @Autowired
    private IDramaRoomAreaService dramaRoomAreaService;

    /**
     * 查询短剧地区列表
     */
    @ApiOperation("查询短剧地区列表")
    @PreAuthorize("@ss.hasPermi('system:area:list')")
    @GetMapping("/list")
    public TableDataInfo list(DramaRoomArea dramaRoomArea)
    {
        startPage();
        List<DramaRoomArea> list = dramaRoomAreaService.selectDramaRoomAreaList(dramaRoomArea);
        return getDataTable(list);
    }

    /**
     * 导出短剧地区列表
     */
    @ApiOperation("导出短剧地区列表")
    @PreAuthorize("@ss.hasPermi('system:area:export')")
    @Log(title = "短剧地区", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DramaRoomArea dramaRoomArea)
    {
        List<DramaRoomArea> list = dramaRoomAreaService.selectDramaRoomAreaList(dramaRoomArea);
        ExcelUtil<DramaRoomArea> util = new ExcelUtil<DramaRoomArea>(DramaRoomArea.class);
        util.exportExcel(response, list, "短剧地区数据");
    }

    /**
     * 获取短剧地区详细信息
     */
    @ApiOperation("获取短剧地区详细信息")
    @PreAuthorize("@ss.hasPermi('system:area:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(dramaRoomAreaService.selectDramaRoomAreaById(id));
    }

    /**
     * 新增短剧地区
     */
    @ApiOperation("新增短剧地区")
    @PreAuthorize("@ss.hasPermi('system:area:add')")
    @Log(title = "短剧地区", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DramaRoomArea dramaRoomArea)
    {
        return toAjax(dramaRoomAreaService.insertDramaRoomArea(dramaRoomArea));
    }

    /**
     * 修改短剧地区
     */
    @ApiOperation("修改短剧地区")
    @PreAuthorize("@ss.hasPermi('system:area:edit')")
    @Log(title = "短剧地区", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DramaRoomArea dramaRoomArea)
    {
        return toAjax(dramaRoomAreaService.updateDramaRoomArea(dramaRoomArea));
    }

    /**
     * 删除短剧地区
     */
    @ApiOperation("删除短剧地区")
    @PreAuthorize("@ss.hasPermi('system:area:remove')")
    @Log(title = "短剧地区", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(dramaRoomAreaService.deleteDramaRoomAreaByIds(ids));
    }
}
