package com.ruoyi.web.controller.drama;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.drama.vo.DramaRoomVideoI18nVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.drama.domain.DramaRoomVideoI18n;
import com.ruoyi.drama.service.IDramaRoomVideoI18nService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 剧集多语言Controller
 * 
 * <AUTHOR>
 * @date 2025-08-05
 */
@Api(tags = "剧集多语言接口")
@RestController
@RequestMapping("/drama/videoI18n")
public class DramaRoomVideoI18nController extends BaseController<DramaRoomVideoI18n>
{
    @Autowired
    private IDramaRoomVideoI18nService dramaRoomVideoI18nService;

    /**
     * 查询剧集多语言列表
     */
    @PreAuthorize("@ss.hasPermi('drama:videoI18n:list')")
    @GetMapping("/list")
    public TableDataInfo<DramaRoomVideoI18n> list(DramaRoomVideoI18n dramaRoomVideoI18n)
    {
        startPage();
        List<DramaRoomVideoI18n> list = dramaRoomVideoI18nService.selectDramaRoomVideoI18nList(dramaRoomVideoI18n);
        return getDataTable(list);
    }

    /**
     * 导出剧集多语言列表
     */
    @PreAuthorize("@ss.hasPermi('drama:videoI18n:export')")
    @Log(title = "剧集多语言", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DramaRoomVideoI18n dramaRoomVideoI18n)
    {
        List<DramaRoomVideoI18n> list = dramaRoomVideoI18nService.selectDramaRoomVideoI18nList(dramaRoomVideoI18n);
        ExcelUtil<DramaRoomVideoI18n> util = new ExcelUtil<DramaRoomVideoI18n>(DramaRoomVideoI18n.class);
        util.exportExcel(response, list, "剧集多语言数据");
    }

    /**
     * 获取剧集多语言详细信息
     */
    @PreAuthorize("@ss.hasPermi('drama:videoI18n:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(dramaRoomVideoI18nService.selectDramaRoomVideoI18nById(id));
    }

    /**
     * 新增剧集多语言
     */
    @PreAuthorize("@ss.hasPermi('drama:videoI18n:add')")
    @Log(title = "剧集多语言", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DramaRoomVideoI18n dramaRoomVideoI18n)
    {
        return toAjax(dramaRoomVideoI18nService.insertDramaRoomVideoI18n(dramaRoomVideoI18n));
    }

    /**
     * 修改剧集多语言
     */
    @ApiOperation("修改剧集多语言")
    @PreAuthorize("@ss.hasPermi('drama:videoI18n:edit')")
    @Log(title = "剧集多语言", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DramaRoomVideoI18n dramaRoomVideoI18n)
    {
        return toAjax(dramaRoomVideoI18nService.updateDramaRoomVideoI18n(dramaRoomVideoI18n));
    }

    /**
     * 删除剧集多语言
     */
    @ApiOperation("删除剧集多语言")
    @PreAuthorize("@ss.hasPermi('drama:videoI18n:remove')")
    @Log(title = "剧集多语言", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(dramaRoomVideoI18nService.deleteDramaRoomVideoI18nByIds(ids));
    }

    @ApiOperation("获取剧集多语言列表")
    @GetMapping("/getVideoI18nList/{movieId}")
    public R<List<DramaRoomVideoI18nVO>> getVideoI18nList(@PathVariable("movieId") Long movieId) {
        List<DramaRoomVideoI18n> list = dramaRoomVideoI18nService.getVideoI18nList(movieId);
        return R.ok(DramaRoomVideoI18n.toVOList(list));
    }
}
