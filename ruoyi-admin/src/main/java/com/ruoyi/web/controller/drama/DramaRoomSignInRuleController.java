package com.ruoyi.web.controller.drama;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.drama.domain.DramaRoomSignInRule;
import com.ruoyi.drama.service.IDramaRoomSignInRuleService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 签到规则Controller
 *
 * <AUTHOR>
 * @date 2025-07-29
 */
@Api(tags = "签到规则")
@RestController
@RequestMapping("/drama/signInRule")
public class DramaRoomSignInRuleController extends BaseController
{
    @Autowired
    private IDramaRoomSignInRuleService dramaRoomSignInRuleService;

    /**
     * 查询签到规则列表
     */
    @ApiOperation("查询签到规则列表")
    @PreAuthorize("@ss.hasPermi('system:rule:list')")
    @GetMapping("/list")
    public TableDataInfo list(DramaRoomSignInRule dramaRoomSignInRule)
    {
        startPage();
        List<DramaRoomSignInRule> list = dramaRoomSignInRuleService.selectDramaRoomSignInRuleList(dramaRoomSignInRule);
        return getDataTable(list);
    }

    /**
     * 导出签到规则列表
     */
    @ApiOperation("导出签到规则列表")
    @PreAuthorize("@ss.hasPermi('system:rule:export')")
    @Log(title = "签到规则", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DramaRoomSignInRule dramaRoomSignInRule)
    {
        List<DramaRoomSignInRule> list = dramaRoomSignInRuleService.selectDramaRoomSignInRuleList(dramaRoomSignInRule);
        ExcelUtil<DramaRoomSignInRule> util = new ExcelUtil<DramaRoomSignInRule>(DramaRoomSignInRule.class);
        util.exportExcel(response, list, "签到规则数据");
    }

    /**
     * 获取签到规则详细信息
     */
    @ApiOperation("获取签到规则详细信息")
    @PreAuthorize("@ss.hasPermi('system:rule:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(dramaRoomSignInRuleService.selectDramaRoomSignInRuleById(id));
    }

    /**
     * 新增签到规则
     */
    @ApiOperation("新增签到规则")
    @PreAuthorize("@ss.hasPermi('system:rule:add')")
    @Log(title = "签到规则", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DramaRoomSignInRule dramaRoomSignInRule)
    {
        return toAjax(dramaRoomSignInRuleService.insertDramaRoomSignInRule(dramaRoomSignInRule));
    }

    /**
     * 修改签到规则
     */
    @ApiOperation("修改签到规则")
    @PreAuthorize("@ss.hasPermi('system:rule:edit')")
    @Log(title = "签到规则", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DramaRoomSignInRule dramaRoomSignInRule)
    {
        return toAjax(dramaRoomSignInRuleService.updateDramaRoomSignInRule(dramaRoomSignInRule));
    }

    /**
     * 删除签到规则
     */
    @ApiOperation("删除签到规则")
    @PreAuthorize("@ss.hasPermi('system:rule:remove')")
    @Log(title = "签到规则", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(dramaRoomSignInRuleService.deleteDramaRoomSignInRuleByIds(ids));
    }
}
