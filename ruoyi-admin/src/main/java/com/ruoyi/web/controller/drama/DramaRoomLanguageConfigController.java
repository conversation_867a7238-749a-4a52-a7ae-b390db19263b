package com.ruoyi.web.controller.drama;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.core.domain.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.drama.domain.DramaRoomLanguageConfig;
import com.ruoyi.drama.service.IDramaRoomLanguageConfigService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 大包多语言配置Controller
 * 
 * <AUTHOR>
 * @date 2025-08-05
 */
@Api(tags = "大包多语言配置接口")
@RestController
@RequestMapping("/drama/languageConfig")
public class DramaRoomLanguageConfigController extends BaseController<DramaRoomLanguageConfig>
{
    @Autowired
    private IDramaRoomLanguageConfigService dramaRoomLanguageConfigService;

    /**
     * 查询大包多语言配置列表
     */
    @PreAuthorize("@ss.hasPermi('drama:language:list')")
    @GetMapping("/list")
    public TableDataInfo<DramaRoomLanguageConfig> list(DramaRoomLanguageConfig dramaRoomLanguageConfig)
    {
        startPage();
        List<DramaRoomLanguageConfig> list = dramaRoomLanguageConfigService.selectDramaRoomLanguageConfigList(dramaRoomLanguageConfig);
        return getDataTable(list);
    }

    /**
     * 导出大包多语言配置列表
     */
    @PreAuthorize("@ss.hasPermi('drama:language:export')")
    @Log(title = "大包多语言配置", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DramaRoomLanguageConfig dramaRoomLanguageConfig)
    {
        List<DramaRoomLanguageConfig> list = dramaRoomLanguageConfigService.selectDramaRoomLanguageConfigList(dramaRoomLanguageConfig);
        ExcelUtil<DramaRoomLanguageConfig> util = new ExcelUtil<DramaRoomLanguageConfig>(DramaRoomLanguageConfig.class);
        util.exportExcel(response, list, "大包多语言配置数据");
    }

    /**
     * 获取大包多语言配置详细信息
     */
    @PreAuthorize("@ss.hasPermi('drama:language:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(dramaRoomLanguageConfigService.selectDramaRoomLanguageConfigById(id));
    }

    /**
     * 新增大包多语言配置
     */
    @PreAuthorize("@ss.hasPermi('drama:language:add')")
    @Log(title = "大包多语言配置", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DramaRoomLanguageConfig dramaRoomLanguageConfig)
    {
        return toAjax(dramaRoomLanguageConfigService.insertDramaRoomLanguageConfig(dramaRoomLanguageConfig));
    }

    /**
     * 修改大包多语言配置
     */
    @PreAuthorize("@ss.hasPermi('drama:language:edit')")
    @Log(title = "大包多语言配置", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DramaRoomLanguageConfig dramaRoomLanguageConfig)
    {
        return toAjax(dramaRoomLanguageConfigService.updateDramaRoomLanguageConfig(dramaRoomLanguageConfig));
    }

    /**
     * 删除大包多语言配置
     */
    @PreAuthorize("@ss.hasPermi('drama:language:remove')")
    @Log(title = "大包多语言配置", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(dramaRoomLanguageConfigService.deleteDramaRoomLanguageConfigByIds(ids));
    }

    @ApiOperation("根据movieId获取可分发翻译语言配置")
    @GetMapping("/getAccessLanguageConfig/{movieId}")
    public R<List<DramaRoomLanguageConfig>> getAccessLanguageConfig(@PathVariable("movieId") Long movieId){
        return R.ok(dramaRoomLanguageConfigService.getAccessLanguageConfig(movieId));
    }
}
