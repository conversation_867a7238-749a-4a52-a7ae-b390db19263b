package com.ruoyi.web.controller.mobile;

import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.drama.dto.UserBehaviorLogDTO;
import com.ruoyi.drama.service.IAppUserBehaviorLogService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@Api(tags = "用户行为相关接口")
@RestController
@RequestMapping("/mobile/behavior")
public class UserBehaviorLogController extends BaseController<Object> {

    @Autowired
    private IAppUserBehaviorLogService appUserBehaviorLogService;

    @ApiOperation("上报用户行为")
    @PostMapping("/report")
    public R<Integer> report(@RequestBody UserBehaviorLogDTO dto) {
        return R.toR(appUserBehaviorLogService.reportBehavior(dto));
    }
}
