package com.ruoyi.web.controller.drama;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.drama.domain.DramaRoomPayTemplate;
import com.ruoyi.drama.service.IDramaRoomPayTemplateService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 付费模板Controller
 * 
 * <AUTHOR>
 * @date 2025-08-13
 */
@Api(value = "付费模板管理", tags = "付费模板")
@RestController
@RequestMapping("/drama/payTemplate")
public class DramaRoomPayTemplateController extends BaseController<DramaRoomPayTemplate>
{
    @Autowired
    private IDramaRoomPayTemplateService dramaRoomPayTemplateService;

    /**
     * 查询付费模板列表
     */
    @ApiOperation(value = "分页获取付费模板列表")
    @PreAuthorize("@ss.hasPermi('drama:payTemplate:list')")
    @GetMapping("/list")
    public TableDataInfo<DramaRoomPayTemplate> list(DramaRoomPayTemplate dramaRoomPayTemplate)
    {
        startPage();
        List<DramaRoomPayTemplate> list = dramaRoomPayTemplateService.selectDramaRoomPayTemplateList(dramaRoomPayTemplate);
        return getDataTable(list);
    }

    @ApiOperation(value = "获取所有付费模板列表")
    @PreAuthorize("@ss.hasPermi('drama:payTemplate:listAll')")
    @GetMapping("/listAll")
    public AjaxResult listAll()
    {
        List<DramaRoomPayTemplate> list = dramaRoomPayTemplateService.selectDramaRoomPayTemplateList(new DramaRoomPayTemplate());
        return AjaxResult.success(list);
    }

    /**
     * 导出付费模板列表
     */
    @ApiOperation(value = "导出付费模板列表")
    @PreAuthorize("@ss.hasPermi('drama:payTemplate:export')")
    @Log(title = "付费模板", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DramaRoomPayTemplate dramaRoomPayTemplate)
    {
        List<DramaRoomPayTemplate> list = dramaRoomPayTemplateService.selectDramaRoomPayTemplateList(dramaRoomPayTemplate);
        ExcelUtil<DramaRoomPayTemplate> util = new ExcelUtil<DramaRoomPayTemplate>(DramaRoomPayTemplate.class);
        util.exportExcel(response, list, "付费模板数据");
    }

    /**
     * 获取付费模板详细信息
     */
    @ApiOperation(value = "获取付费模板详细信息")
    @PreAuthorize("@ss.hasPermi('drama:payTemplate:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(dramaRoomPayTemplateService.selectDramaRoomPayTemplateById(id));
    }

    /**
     * 新增付费模板
     */
    @ApiOperation(value = "新增付费模板")
    @PreAuthorize("@ss.hasPermi('drama:payTemplate:add')")
    @Log(title = "付费模板", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DramaRoomPayTemplate dramaRoomPayTemplate)
    {
        return toAjax(dramaRoomPayTemplateService.insertDramaRoomPayTemplate(dramaRoomPayTemplate));
    }

    /**
     * 修改付费模板
     */
    @ApiOperation(value = "修改付费模板")
    @PreAuthorize("@ss.hasPermi('drama:payTemplate:edit')")
    @Log(title = "付费模板", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DramaRoomPayTemplate dramaRoomPayTemplate)
    {
        return toAjax(dramaRoomPayTemplateService.updateDramaRoomPayTemplate(dramaRoomPayTemplate));
    }

    /**
     * 删除付费模板
     */
    @ApiOperation(value = "删除付费模板")
    @PreAuthorize("@ss.hasPermi('drama:payTemplate:remove')")
    @Log(title = "付费模板", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(dramaRoomPayTemplateService.deleteDramaRoomPayTemplateByIds(ids));
    }
}