package com.ruoyi.web.controller.drama;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.drama.dto.DramaRoomGeneralI18nDTO;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.drama.domain.DramaRoomAreaI18n;
import com.ruoyi.drama.service.IDramaRoomAreaI18nService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 短剧地区多语言Controller
 *
 * <AUTHOR>
 * @date 2025-08-05
 */
@RestController
@RequestMapping("/drama/areaI18n")
public class DramaRoomAreaI18nController extends BaseController<DramaRoomAreaI18n> {
    @Autowired
    private IDramaRoomAreaI18nService dramaRoomAreaI18nService;

    /**
     * 查询短剧地区多语言列表
     */
    @PreAuthorize("@ss.hasPermi('drama:areaI18n:list')")
    @GetMapping("/list")
    public TableDataInfo<DramaRoomAreaI18n> list(DramaRoomAreaI18n dramaRoomAreaI18n) {
        startPage();
        List<DramaRoomAreaI18n> list = dramaRoomAreaI18nService.selectDramaRoomAreaI18nList(dramaRoomAreaI18n);
        return getDataTable(list);
    }

    /**
     * 导出短剧地区多语言列表
     */
    @PreAuthorize("@ss.hasPermi('drama:areaI18n:export')")
    @Log(title = "短剧地区多语言", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DramaRoomAreaI18n dramaRoomAreaI18n) {
        List<DramaRoomAreaI18n> list = dramaRoomAreaI18nService.selectDramaRoomAreaI18nList(dramaRoomAreaI18n);
        ExcelUtil<DramaRoomAreaI18n> util = new ExcelUtil<DramaRoomAreaI18n>(DramaRoomAreaI18n.class);
        util.exportExcel(response, list, "短剧地区多语言数据");
    }

    /**
     * 获取短剧地区多语言详细信息
     */
    @PreAuthorize("@ss.hasPermi('drama:areaI18n:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(dramaRoomAreaI18nService.selectDramaRoomAreaI18nById(id));
    }

    /**
     * 新增短剧地区多语言
     */
    @PreAuthorize("@ss.hasPermi('drama:areaI18n:add')")
    @Log(title = "短剧地区多语言", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DramaRoomAreaI18n dramaRoomAreaI18n) {
        return toAjax(dramaRoomAreaI18nService.insertDramaRoomAreaI18n(dramaRoomAreaI18n));
    }

    /**
     * 修改短剧地区多语言
     */
    @PreAuthorize("@ss.hasPermi('drama:areaI18n:edit')")
    @Log(title = "短剧地区多语言", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DramaRoomAreaI18n dramaRoomAreaI18n) {
        return toAjax(dramaRoomAreaI18nService.updateDramaRoomAreaI18n(dramaRoomAreaI18n));
    }

    /**
     * 删除短剧地区多语言
     */
    @PreAuthorize("@ss.hasPermi('drama:areaI18n:remove')")
    @Log(title = "短剧地区多语言", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(dramaRoomAreaI18nService.deleteDramaRoomAreaI18nByIds(ids));
    }

    /**
     * 单个语种翻译
     */
    @PreAuthorize("@ss.hasPermi('drama:areaI18n:translate')")
    @Log(title = "短剧地区多语言", businessType = BusinessType.INSERT)
    @PostMapping("/translate")
    public AjaxResult translate(@RequestBody DramaRoomGeneralI18nDTO dto) {
        dto.setUserId(getUserId());
        return dramaRoomAreaI18nService.translate(dto);
    }

    /**
     * 获取需要翻译的语种
     */
    @PreAuthorize("@ss.hasPermi('drama:areaI18n:translateList')")
    @GetMapping("/list/translate/{id}")
    public AjaxResult listTranslate(@PathVariable("id") Long id) {
        return dramaRoomAreaI18nService.listTranslate(id);
    }
}
