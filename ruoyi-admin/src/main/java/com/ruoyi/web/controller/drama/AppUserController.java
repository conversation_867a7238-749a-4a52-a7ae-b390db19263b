package com.ruoyi.web.controller.drama;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.drama.domain.vo.SemAppUserVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.domain.entity.AppUser;
import com.ruoyi.drama.service.IAppUserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiImplicitParam;

/**
 * APP用户管理Controller
 * 
 * <AUTHOR>
 */
@Api(tags = "APP用户管理", description = "管理后台APP用户数据的CRUD操作")
@RestController
@RequestMapping("/system/appUser")
public class AppUserController extends BaseController
{
    @Autowired
    private IAppUserService appUserService;

    /**
     * 查询APP用户列表
     */
    @ApiOperation(value = "查询APP用户列表", notes = "分页查询系统中的APP用户记录")
    @PreAuthorize("@ss.hasPermi('system:appUser:list')")
    @GetMapping("/list")
    public TableDataInfo<AppUser> list(AppUser appUser)
    {
        startPage();
        List<AppUser> list = appUserService.selectAppUserList(appUser);
        return getDataTable(list);
    }

    /**
     * 查询APP用户列表
     */
    @ApiOperation(value = "查询APP用户列表", notes = "分页查询系统中的APP用户记录")
    @PreAuthorize("@ss.hasPermi('system:appUser:list')")
    @GetMapping("/sem-list")
    public TableDataInfo<SemAppUserVo> semList(AppUser appUser)
    {
        startPage();
        List<SemAppUserVo> list = appUserService.selectSemAppUserList(appUser);

        return getDataTable(list);
    }

    /**
     * 导出APP用户列表
     */
    @ApiOperation(value = "导出APP用户列表", notes = "导出APP用户记录数据到Excel文件")
    @PreAuthorize("@ss.hasPermi('system:appUser:export')")
    @Log(title = "APP用户管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, AppUser appUser)
    {
        List<AppUser> list = appUserService.selectAppUserList(appUser);
        ExcelUtil<AppUser> util = new ExcelUtil<AppUser>(AppUser.class);
        util.exportExcel(response, list, "APP用户数据");
    }

    /**
     * 获取APP用户详细信息
     */
    @ApiOperation(value = "获取APP用户详细信息", notes = "根据用户ID查询单个APP用户记录的详细信息")
    @ApiImplicitParam(name = "userId", value = "APP用户ID", required = true, dataType = "Long", paramType = "path")
    @PreAuthorize("@ss.hasPermi('system:appUser:query')")
    @GetMapping(value = "/{userId}")
    public R<AppUser> getInfo(@PathVariable("userId") Long userId)
    {
        return R.ok(appUserService.selectAppUserByUserId(userId));
    }

} 