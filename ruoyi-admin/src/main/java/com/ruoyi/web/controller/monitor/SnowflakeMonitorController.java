package com.ruoyi.web.controller.monitor;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.uuid.IdUtils;
import com.ruoyi.common.utils.uuid.SnowflakeIdWorker;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 雪花算法监控
 *
 * <AUTHOR>
 */
@Api(tags = "雪花算法监控")
@RestController
@RequestMapping("/monitor/snowflake")
public class SnowflakeMonitorController extends BaseController
{
    /**
     * 获取雪花算法监控信息
     */
    @ApiOperation("获取雪花算法监控信息")
    @PreAuthorize("@ss.hasPermi('monitor:snowflake:view')")
    @GetMapping()
    public R<SnowflakeIdWorker.SnowflakeMonitorInfo> getMonitorInfo()
    {
        SnowflakeIdWorker.SnowflakeMonitorInfo info = IdUtils.getSnowflakeMonitorInfo();
        return R.ok(info);
    }

    /**
     * 重置雪花算法监控统计
     */
    @ApiOperation("重置雪花算法监控统计")
    @Log(title = "雪花算法监控", businessType = BusinessType.CLEAN)
    @PreAuthorize("@ss.hasPermi('monitor:snowflake:reset')")
    @PostMapping("/reset")
    public R<Void> resetMonitor()
    {
        IdUtils.resetSnowflakeMonitor();
        return R.ok();
    }

    /**
     * 生成测试ID（用于性能测试）
     */
    @ApiOperation("生成测试ID")
    @PreAuthorize("@ss.hasPermi('monitor:snowflake:test')")
    @PostMapping("/test")
    public R<Long> generateTestId()
    {
        long id = IdUtils.snowflakeId();
        return R.ok(id);
    }
} 