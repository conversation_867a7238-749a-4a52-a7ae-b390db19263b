package com.ruoyi.web.controller.mobile;


import cn.hutool.http.HttpUtil;
import cn.hutool.http.server.HttpServerRequest;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.drama.vo.MovieCategoryVO;
import com.ruoyi.common.drama.vo.MovieFilterVO;
import com.ruoyi.common.drama.vo.PushMovieVideoVO;
import com.ruoyi.common.drama.vo.VideoVO;
import com.ruoyi.common.enums.DramaRoomParamConfigEnum;
import com.ruoyi.common.utils.RequestUtils;
import com.ruoyi.drama.domain.DramaRoomLanguageConfig;
import com.ruoyi.drama.service.IDramaRoomLanguageConfigService;
import com.ruoyi.drama.service.IDramaRoomParamConfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * App配置接口
 *
 * <AUTHOR>
 */
@Api(tags = "App配置接口")
@RestController
@RequestMapping("/mobile/config")
public class AppConfigController {

    @Autowired
    private IDramaRoomParamConfigService dramaRoomParamConfigService;

    @Autowired
    private IDramaRoomLanguageConfigService dramaRoomLanguageConfigService;

    /**
     * 获取首页-配置数据
     */
    @ApiOperation("获取首页-配置数据")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "授权token", required = true, paramType = "header"),
            @ApiImplicitParam(name = "languageCode", value = "语言编码", required = true, paramType = "query")
    })
    @GetMapping("/home")
    public R<List<MovieCategoryVO>> getHome(@RequestHeader("languageCode") String languageCode) {
        List<MovieCategoryVO> appHomeConfig = dramaRoomParamConfigService.selectRecommendConfigByKey(DramaRoomParamConfigEnum.APP_HOME_CONFIG.getCode(), languageCode);
        return R.ok(appHomeConfig);
    }

    @ApiOperation("获取筛选数据")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "授权token", required = true, paramType = "header"),
            @ApiImplicitParam(name = "languageCode", value = "语言编码", required = true, paramType = "query")
    })
    @GetMapping("/filter")
    public R<MovieFilterVO> getFilter(@RequestHeader("languageCode") String languageCode) {
        MovieFilterVO movieFilterVO = dramaRoomParamConfigService.selectFilterData(languageCode);
        return R.ok(movieFilterVO);
    }

    @ApiOperation("获取推荐短剧")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "授权token", required = true, paramType = "header"),
            @ApiImplicitParam(name = "languageCode", value = "语言编码", required = true, paramType = "query")
    })
    @GetMapping("/recommend")
    public R<List<PushMovieVideoVO>> getRecommend(@RequestHeader("languageCode") String languageCode) {
        List<PushMovieVideoVO> recommend = dramaRoomParamConfigService.selectRecommendMovieConfigByKey(DramaRoomParamConfigEnum.APP_RECOMMEND_MOVIE_CONFIG.getCode(), languageCode);
        return R.ok(recommend);
    }

    @ApiOperation("获取语言列表")
    @GetMapping("/language")
    public R<List<DramaRoomLanguageConfig>> getLanguageList() {
        List<DramaRoomLanguageConfig> list = dramaRoomLanguageConfigService.selectDramaRoomLanguageConfigList(new DramaRoomLanguageConfig());
        return R.ok(list);
    }
}
