package com.ruoyi.web.controller.drama;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.core.domain.R;
import com.ruoyi.drama.domain.vo.DramaRoomMovieBindTagVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.drama.domain.DramaRoomMovieTag;
import com.ruoyi.drama.service.IDramaRoomMovieTagService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 短剧标签关联Controller
 *
 * <AUTHOR>
 * @date 2025-07-04
 */
@Api(tags = "短剧标签关联接口")
@RestController
@RequestMapping("/drama/movieTag")
public class DramaRoomMovieTagController extends BaseController<DramaRoomMovieTag> {
    @Autowired
    private IDramaRoomMovieTagService dramaRoomMovieTagService;

    /**
     * 查询短剧标签关联列表
     */
    @ApiOperation("获取短剧标签关联列表")
    @PreAuthorize("@ss.hasPermi('system:tag:list')")
    @GetMapping("/list")
    public TableDataInfo<DramaRoomMovieTag> list(DramaRoomMovieTag DramaRoomMovieTag) {
        startPage();
        List<DramaRoomMovieTag> list = dramaRoomMovieTagService.selectDramaRoomMovieTagList(DramaRoomMovieTag);
        return getDataTable(list);
    }

    /**
     * 导出短剧标签关联列表
     */
    @ApiOperation("导出短剧标签关联列表")
    @PreAuthorize("@ss.hasPermi('system:tag:export')")
    @Log(title = "短剧标签关联", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DramaRoomMovieTag DramaRoomMovieTag) {
        List<DramaRoomMovieTag> list = dramaRoomMovieTagService.selectDramaRoomMovieTagList(DramaRoomMovieTag);
        ExcelUtil<DramaRoomMovieTag> util = new ExcelUtil<DramaRoomMovieTag>(DramaRoomMovieTag.class);
        util.exportExcel(response, list, "短剧标签关联数据");
    }

    /**
     * 获取短剧标签关联详细信息
     */
    @ApiOperation("获取短剧标签关联详细信息")
    @PreAuthorize("@ss.hasPermi('system:tag:query')")
    @GetMapping(value = "/{id}")
    public R<DramaRoomMovieTag> getInfo(@PathVariable("id") Long id) {
        return R.ok(dramaRoomMovieTagService.selectDramaRoomMovieTagById(id));
    }

    /**
     * 新增短剧标签关联
     */
    @ApiOperation("新增短剧标签关联")
    @PreAuthorize("@ss.hasPermi('system:tag:add')")
    @Log(title = "短剧标签关联", businessType = BusinessType.INSERT)
    @PostMapping
    public R<Integer> add(@RequestBody DramaRoomMovieTag DramaRoomMovieTag) {
        return R.toR(dramaRoomMovieTagService.insertDramaRoomMovieTag(DramaRoomMovieTag));
    }

    /**
     * 修改短剧标签关联
     */
    @ApiOperation("修改短剧标签关联")
    @PreAuthorize("@ss.hasPermi('system:tag:edit')")
    @Log(title = "短剧标签关联", businessType = BusinessType.UPDATE)
    @PutMapping
    public R<Integer> edit(@RequestBody DramaRoomMovieTag DramaRoomMovieTag) {
        return R.toR(dramaRoomMovieTagService.updateDramaRoomMovieTag(DramaRoomMovieTag));
    }

    /**
     * 删除短剧标签关联
     */
    @ApiOperation("删除短剧标签关联")
    @PreAuthorize("@ss.hasPermi('system:tag:remove')")
    @Log(title = "短剧标签关联", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Integer> remove(@PathVariable Long[] ids) {
        return R.toR(dramaRoomMovieTagService.deleteDramaRoomMovieTagByIds(ids));
    }

    /**
     * 根据标签id查询所有绑定短剧
     */
    @ApiOperation("根据标签id查询所有绑定短剧")
    @PreAuthorize("@ss.hasPermi('system:tag:movieList')")
    @GetMapping("/list/movies/{tagId}")
    public R<List<DramaRoomMovieBindTagVO>> list(@PathVariable Long tagId) {
        return R.ok(dramaRoomMovieTagService.selectDramaRoomMovieTagListByCategoryId(tagId));
    }

    /**
     * 修改标签绑定短剧数据
     */
    @ApiOperation("修改标签绑定短剧数据")
    @PreAuthorize("@ss.hasPermi('system:tag:movieUpdate')")
    @PutMapping("/bind/movies/{tagId}")
    public R<String> updateBindMovies(@PathVariable Long tagId, @RequestBody List<Long> movieIds) {
        dramaRoomMovieTagService.updateBindMovies(tagId, movieIds);
        return R.ok("绑定成功");
    }

}
