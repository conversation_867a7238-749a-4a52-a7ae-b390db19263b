package com.ruoyi.web.controller.drama;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.drama.domain.DramaRoomTransactionRecord;
import com.ruoyi.drama.service.IDramaRoomTransactionRecordService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 交易记录（包含支付和退款）Controller
 * 
 * <AUTHOR>
 * @date 2025-08-08
 */
@RestController
@RequestMapping("/drama/record")
public class DramaRoomTransactionRecordController extends BaseController
{
    @Autowired
    private IDramaRoomTransactionRecordService dramaRoomTransactionRecordService;

    /**
     * 查询交易记录（包含支付和退款）列表
     */
    @PreAuthorize("@ss.hasPermi('system:record:list')")
    @GetMapping("/list")
    public TableDataInfo list(DramaRoomTransactionRecord dramaRoomTransactionRecord)
    {
        startPage();
        List<DramaRoomTransactionRecord> list = dramaRoomTransactionRecordService.selectDramaRoomTransactionRecordList(dramaRoomTransactionRecord);
        return getDataTable(list);
    }

    /**
     * 导出交易记录（包含支付和退款）列表
     */
    @PreAuthorize("@ss.hasPermi('system:record:export')")
    @Log(title = "交易记录（包含支付和退款）", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DramaRoomTransactionRecord dramaRoomTransactionRecord)
    {
        List<DramaRoomTransactionRecord> list = dramaRoomTransactionRecordService.selectDramaRoomTransactionRecordList(dramaRoomTransactionRecord);
        ExcelUtil<DramaRoomTransactionRecord> util = new ExcelUtil<DramaRoomTransactionRecord>(DramaRoomTransactionRecord.class);
        util.exportExcel(response, list, "交易记录（包含支付和退款）数据");
    }

    /**
     * 获取交易记录（包含支付和退款）详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:record:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(dramaRoomTransactionRecordService.selectDramaRoomTransactionRecordById(id));
    }

    /**
     * 新增交易记录（包含支付和退款）
     */
    @PreAuthorize("@ss.hasPermi('system:record:add')")
    @Log(title = "交易记录（包含支付和退款）", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DramaRoomTransactionRecord dramaRoomTransactionRecord)
    {
        return toAjax(dramaRoomTransactionRecordService.insertDramaRoomTransactionRecord(dramaRoomTransactionRecord));
    }

    /**
     * 修改交易记录（包含支付和退款）
     */
    @PreAuthorize("@ss.hasPermi('system:record:edit')")
    @Log(title = "交易记录（包含支付和退款）", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DramaRoomTransactionRecord dramaRoomTransactionRecord)
    {
        return toAjax(dramaRoomTransactionRecordService.updateDramaRoomTransactionRecord(dramaRoomTransactionRecord));
    }

    /**
     * 删除交易记录（包含支付和退款）
     */
    @PreAuthorize("@ss.hasPermi('system:record:remove')")
    @Log(title = "交易记录（包含支付和退款）", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(dramaRoomTransactionRecordService.deleteDramaRoomTransactionRecordByIds(ids));
    }
}