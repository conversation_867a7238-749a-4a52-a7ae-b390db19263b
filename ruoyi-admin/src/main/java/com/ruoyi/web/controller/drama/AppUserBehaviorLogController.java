package com.ruoyi.web.controller.drama;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.drama.domain.AppUserBehaviorLog;
import com.ruoyi.drama.service.IAppUserBehaviorLogService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 用户行为记录Controller
 *
 * <AUTHOR>
 * @date 2025-07-29
 */
@Api(tags = "用户行为记录")
@RestController
@RequestMapping("/drama/userBehaviorLog")
public class AppUserBehaviorLogController extends BaseController {

    @Autowired
    private IAppUserBehaviorLogService appUserBehaviorLogService;

    /**
     * 查询用户行为记录列表
     */
    @ApiOperation("查询用户行为记录列表")
    @PreAuthorize("@ss.hasPermi('system:log:list')")
    @GetMapping("/list")
    public TableDataInfo list(AppUserBehaviorLog appUserBehaviorLog) {
        startPage();
        List<AppUserBehaviorLog> list = appUserBehaviorLogService.selectAppUserBehaviorLogList(appUserBehaviorLog);
        return getDataTable(list);
    }

    /**
     * 导出用户行为记录列表
     */
    @ApiOperation("导出用户行为记录列表")
    @PreAuthorize("@ss.hasPermi('system:log:export')")
    @Log(title = "用户行为记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, AppUserBehaviorLog appUserBehaviorLog) {
        List<AppUserBehaviorLog> list = appUserBehaviorLogService.selectAppUserBehaviorLogList(appUserBehaviorLog);
        ExcelUtil<AppUserBehaviorLog> util = new ExcelUtil<>(AppUserBehaviorLog.class);
        util.exportExcel(response, list, "用户行为记录数据");
    }

    /**
     * 获取用户行为记录详细信息
     */
    @ApiOperation("获取用户行为记录详细信息")
    @PreAuthorize("@ss.hasPermi('system:log:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(appUserBehaviorLogService.selectAppUserBehaviorLogById(id));
    }
}


