package com.ruoyi.web.controller.mobile;


import cn.hutool.http.server.HttpServerRequest;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.drama.dto.DramaRoomMovieQueryDTO;
import com.ruoyi.common.drama.dto.DramaRoomMovieUnlockVideoDTO;
import com.ruoyi.common.drama.vo.MovieDetailVO;
import com.ruoyi.common.drama.vo.MovieVO;
import com.ruoyi.common.drama.vo.VideoVO;
import com.ruoyi.drama.domain.DramaRoomMovie;
import com.ruoyi.drama.domain.DramaRoomVideo;
import com.ruoyi.drama.service.IDramaRoomMovieService;
import com.ruoyi.drama.service.IDramaRoomVideoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 短剧接口
 *
 * <AUTHOR>
 */
@Api(tags = "短剧接口")
@RestController
@RequestMapping("/mobile/movie")
public class MovieController extends BaseController<MovieVO> {

    @Autowired
    private IDramaRoomMovieService dramaRoomMovieService;

    @Autowired
    private IDramaRoomVideoService dramaRoomVideoService;

    @ApiOperation("分页获取所有短剧列表")
    @PostMapping("/allMovie")
    public TableDataInfo<MovieVO> getMovieList() {
        startPage();
        // 这里实现分页逻辑
        // 通常需要接收分页参数如pageNum, pageSize
        // 并返回分页查询结果
        List<DramaRoomMovie> list = dramaRoomMovieService.selectDramaRoomMovieList(new DramaRoomMovie());
        return getDataTable(DramaRoomMovie.toVOList(list));
    }

    @ApiOperation("获取短剧详情")
    @GetMapping("/detail")
    public R<MovieDetailVO> getMovieDetail(@RequestHeader("languageCode") String languageCode, Long id) {
        MovieDetailVO movieDetailVO = dramaRoomMovieService.getMovieDetail(id,languageCode);
        return R.ok(movieDetailVO);
    }

    @ApiOperation("根据短剧ID获取短剧视频列表")
    @GetMapping("/getVideoList/{movieId}")
    public R<List<VideoVO>> getMovieVideos(@RequestHeader("languageCode") String languageCode, @PathVariable("movieId") Long movieId) {
        List<DramaRoomVideo> list = dramaRoomVideoService.selectByMovieId(movieId, languageCode);
        return R.ok(DramaRoomVideo.toVideoVOList(list));
    }

    //推荐页短剧分类查询
    @ApiOperation("推荐页短剧分类查询")
    @PostMapping("/queryMovie")
    public R<List<MovieVO>> queryMovie(@RequestHeader("languageCode") String languageCode, @RequestBody DramaRoomMovieQueryDTO queryDTO) {
        List<DramaRoomMovie> list = dramaRoomMovieService.queryMovie(queryDTO,languageCode);
        return R.ok(DramaRoomMovie.toVOList(list));
    }

    @ApiOperation("根据短剧名称模糊搜索")
    @GetMapping("/queryByMovieName")
    public R<List<MovieVO>> queryByMovieName(String name) {
        List<DramaRoomMovie> list = dramaRoomMovieService.queryByMovieName(name);
        return R.ok(DramaRoomMovie.toVOList(list));
    }

}
