package com.ruoyi.web.controller.drama;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.drama.domain.AppUserSubscription;
import com.ruoyi.drama.service.IAppUserSubscriptionService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 用户订阅Controller
 * 
 * <AUTHOR>
 * @date 2025-09-01
 */
@Api(value = "用户订阅管理", tags = "用户订阅")
@RestController
@RequestMapping("/drama/user/subscription")
public class AppUserSubscriptionController extends BaseController<AppUserSubscription>
{
    @Autowired
    private IAppUserSubscriptionService appUserSubscriptionService;

    /**
     * 查询用户订阅列表
     */
    @ApiOperation(value = "获取用户订阅列表")
    @PreAuthorize("@ss.hasPermi('drama:subscription:list')")
    @GetMapping("/list")
    public TableDataInfo<AppUserSubscription> list(AppUserSubscription appUserSubscription)
    {
        startPage();
        List<AppUserSubscription> list = appUserSubscriptionService.selectAppUserSubscriptionList(appUserSubscription);
        return getDataTable(list);
    }

    /**
     * 导出用户订阅列表
     */
    @ApiOperation(value = "导出用户订阅列表")
    @PreAuthorize("@ss.hasPermi('drama:subscription:export')")
    @Log(title = "用户订阅", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, AppUserSubscription appUserSubscription)
    {
        List<AppUserSubscription> list = appUserSubscriptionService.selectAppUserSubscriptionList(appUserSubscription);
        ExcelUtil<AppUserSubscription> util = new ExcelUtil<AppUserSubscription>(AppUserSubscription.class);
        util.exportExcel(response, list, "用户订阅数据");
    }

    /**
     * 获取用户订阅详细信息
     */
    @ApiOperation(value = "获取用户订阅详细信息")
    @PreAuthorize("@ss.hasPermi('drama:subscription:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(appUserSubscriptionService.selectAppUserSubscriptionById(id));
    }

    /**
     * 新增用户订阅
     */
    @ApiOperation(value = "新增用户订阅")
    @PreAuthorize("@ss.hasPermi('drama:subscription:add')")
    @Log(title = "用户订阅", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody AppUserSubscription appUserSubscription)
    {
        return toAjax(appUserSubscriptionService.insertAppUserSubscription(appUserSubscription));
    }

    /**
     * 修改用户订阅
     */
    @ApiOperation(value = "修改用户订阅")
    @PreAuthorize("@ss.hasPermi('drama:subscription:edit')")
    @Log(title = "用户订阅", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody AppUserSubscription appUserSubscription)
    {
        return toAjax(appUserSubscriptionService.updateAppUserSubscription(appUserSubscription));
    }

    /**
     * 删除用户订阅
     */
    @ApiOperation(value = "删除用户订阅")
    @PreAuthorize("@ss.hasPermi('drama:subscription:remove')")
    @Log(title = "用户订阅", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(appUserSubscriptionService.deleteAppUserSubscriptionByIds(ids));
    }
}