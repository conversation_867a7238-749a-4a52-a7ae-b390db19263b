package com.ruoyi.web.controller.drama;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.drama.domain.DramaRoomParamConfig;
import com.ruoyi.drama.service.IDramaRoomParamConfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 【请填写功能名称】Controller
 *
 * <AUTHOR>
 * @date 2025-07-04
 */
@Api(tags = "短剧参数配置接口")
@RestController
@RequestMapping("/drama/config")
public class DramaRoomParamConfigController extends BaseController<DramaRoomParamConfig> {
    @Autowired
    private IDramaRoomParamConfigService dramaRoomParamConfigService;

    /**
     * 查询【请填写功能名称】列表
     */
    @ApiOperation("获取参数配置列表")
    @PreAuthorize("@ss.hasPermi('system:config:list')")
    @GetMapping("/list")
    public TableDataInfo<DramaRoomParamConfig> list(DramaRoomParamConfig dramaRoomParamConfig) {
        startPage();
        List<DramaRoomParamConfig> list = dramaRoomParamConfigService.selectDramaRoomParamConfigList(dramaRoomParamConfig);
        return getDataTable(list);
    }

    /**
     * 导出【请填写功能名称】列表
     */
    @ApiOperation("导出参数配置列表")
    @PreAuthorize("@ss.hasPermi('system:config:export')")
    @Log(title = "【请填写功能名称】", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DramaRoomParamConfig dramaRoomParamConfig) {
        List<DramaRoomParamConfig> list = dramaRoomParamConfigService.selectDramaRoomParamConfigList(dramaRoomParamConfig);
        ExcelUtil<DramaRoomParamConfig> util = new ExcelUtil<DramaRoomParamConfig>(DramaRoomParamConfig.class);
        util.exportExcel(response, list, "【请填写功能名称】数据");
    }

    /**
     * 获取参数配置详细信息
     */
    @ApiOperation("获取参数配置详细信息")
    @PreAuthorize("@ss.hasPermi('system:config:query')")
    @GetMapping(value = "/{id}")
    public R<DramaRoomParamConfig> getInfo(@PathVariable("id") Long id) {
        return R.ok(dramaRoomParamConfigService.selectDramaRoomParamConfigById(id));
    }

    /**
     * 新增参数配置
     */
    @ApiOperation("新增参数配置")
    @PreAuthorize("@ss.hasPermi('system:config:add')")
    @Log(title = "【请填写功能名称】", businessType = BusinessType.INSERT)
    @PostMapping
    public R<Integer> add(@RequestBody DramaRoomParamConfig dramaRoomParamConfig) {
        return R.toR(dramaRoomParamConfigService.insertDramaRoomParamConfig(dramaRoomParamConfig));
    }

    /**
     * 修改参数配置
     */
    @ApiOperation("修改参数配置")
    @PreAuthorize("@ss.hasPermi('system:config:edit')")
    @Log(title = "【请填写功能名称】", businessType = BusinessType.UPDATE)
    @PutMapping
    public R<Integer> edit(@RequestBody DramaRoomParamConfig dramaRoomParamConfig) {
        return R.toR(dramaRoomParamConfigService.updateDramaRoomParamConfig(dramaRoomParamConfig));
    }

    /**
     * 删除参数配置
     */
    @ApiOperation("删除参数配置")
    @PreAuthorize("@ss.hasPermi('system:config:remove')")
    @Log(title = "【请填写功能名称】", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Integer> remove(@PathVariable Long[] ids) {
        return R.toR(dramaRoomParamConfigService.deleteDramaRoomParamConfigByIds(ids));
    }
}
