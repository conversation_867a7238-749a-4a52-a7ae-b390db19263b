package com.ruoyi.web.controller.drama;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.drama.domain.DramaRoomUserLike;
import com.ruoyi.drama.service.IDramaRoomUserLikeService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 用户点赞记录Controller
 *
 * <AUTHOR>
 * @date 2025-07-08
 */
@RestController
@RequestMapping("/system/like")
public class DramaRoomUserLikeController extends BaseController
{
    @Autowired
    private IDramaRoomUserLikeService dramaRoomUserLikeService;

    /**
     * 查询用户点赞记录列表
     */
    @PreAuthorize("@ss.hasPermi('system:like:list')")
    @GetMapping("/list")
    public TableDataInfo list(DramaRoomUserLike dramaRoomUserLike)
    {
        startPage();
        List<DramaRoomUserLike> list = dramaRoomUserLikeService.selectDramaRoomUserLikeList(dramaRoomUserLike);
        return getDataTable(list);
    }

    /**
     * 导出用户点赞记录列表
     */
    @PreAuthorize("@ss.hasPermi('system:like:export')")
    @Log(title = "用户点赞记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DramaRoomUserLike dramaRoomUserLike)
    {
        List<DramaRoomUserLike> list = dramaRoomUserLikeService.selectDramaRoomUserLikeList(dramaRoomUserLike);
        ExcelUtil<DramaRoomUserLike> util = new ExcelUtil<DramaRoomUserLike>(DramaRoomUserLike.class);
        util.exportExcel(response, list, "用户点赞记录数据");
    }

    /**
     * 获取用户点赞记录详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:like:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(dramaRoomUserLikeService.selectDramaRoomUserLikeById(id));
    }

    /**
     * 新增用户点赞记录
     */
    @PreAuthorize("@ss.hasPermi('system:like:add')")
    @Log(title = "用户点赞记录", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DramaRoomUserLike dramaRoomUserLike)
    {
        return toAjax(dramaRoomUserLikeService.insertDramaRoomUserLike(dramaRoomUserLike));
    }

    /**
     * 修改用户点赞记录
     */
    @PreAuthorize("@ss.hasPermi('system:like:edit')")
    @Log(title = "用户点赞记录", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DramaRoomUserLike dramaRoomUserLike)
    {
        return toAjax(dramaRoomUserLikeService.updateDramaRoomUserLike(dramaRoomUserLike));
    }

    /**
     * 删除用户点赞记录
     */
    @PreAuthorize("@ss.hasPermi('system:like:remove')")
    @Log(title = "用户点赞记录", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(dramaRoomUserLikeService.deleteDramaRoomUserLikeByIds(ids));
    }
}
