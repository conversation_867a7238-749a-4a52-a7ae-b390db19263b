
package com.ruoyi.web.controller.mobile;

import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.drama.dto.GuestLoginDto;
import com.ruoyi.common.drama.dto.RefreshTokenDto;
import com.ruoyi.common.drama.dto.ThirdPartyLoginDTO;
import com.ruoyi.common.drama.vo.TokenVO;
import com.ruoyi.common.utils.mobile.AppTokenService;
import com.ruoyi.drama.service.IAppUserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * APP用户认证Controller
 * 
 * <AUTHOR>
 */
@Api(tags = "移动端用户认证")
@RestController("mobileAppUserAuthController")
@RequestMapping("/mobile/auth")
@Validated
public class AppUserAuthController extends BaseController
{
    @Autowired
    private IAppUserService appUserService;

    /**
     * 游客登录
     */
    @ApiOperation("游客登录")
    @PostMapping("/guest-login")
    public R<TokenVO> guestLogin(
            @Valid @RequestBody GuestLoginDto guestLoginDto)
    {
        AppTokenService.TokenVo tokenVo = appUserService.guestLogin(guestLoginDto);
        return R.ok(new TokenVO(tokenVo.getAccessToken(), tokenVo.getRefreshToken()));
    }

    /**
     * 刷新token
     */
    @ApiOperation("刷新token")
    @PostMapping("/refresh-token")
    public R<TokenVO> refreshToken(
            @Valid @RequestBody RefreshTokenDto refreshTokenDto)
    {
        AppTokenService.TokenVo tokenVo = appUserService.refreshToken(refreshTokenDto);
        return R.ok(new TokenVO(tokenVo.getAccessToken(), tokenVo.getRefreshToken(),tokenVo.isTop()));
    }

    /**
     * 第三方登录
     */
    @ApiOperation("第三方登录")
    @PostMapping("/third-party-login")
    public R<TokenVO> thirdPartyLogin(@Valid @RequestBody ThirdPartyLoginDTO request)
    {
        AppTokenService.TokenVo tokenVo = appUserService.thirdPartyLogin(request);

        return R.ok(new TokenVO(tokenVo.getAccessToken(), tokenVo.getRefreshToken()));
    }

    /**
     * 退出登录
     */
    @ApiOperation("退出登录")
    @PostMapping("/logout")
    public R<?> logout()
    {
        appUserService.logout();
        return R.ok();
    }
}
