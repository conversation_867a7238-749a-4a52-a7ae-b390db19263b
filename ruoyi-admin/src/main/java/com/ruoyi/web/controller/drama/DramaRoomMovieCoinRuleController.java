package com.ruoyi.web.controller.drama;

import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.drama.dto.DramaRoomMovieCoinRuleDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.drama.domain.DramaRoomMovieCoinRule;
import com.ruoyi.drama.service.IDramaRoomMovieCoinRuleService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 短剧扣费规则Controller
 *
 * <AUTHOR>
 * @date 2025-08-05
 */
@Api(tags = "短剧扣费规则接口")
@RestController
@RequestMapping("/drama/movieCoinRule")
public class DramaRoomMovieCoinRuleController extends BaseController<DramaRoomMovieCoinRule>
{
    @Autowired
    private IDramaRoomMovieCoinRuleService dramaRoomMovieCoinRuleService;

    /**
     * 查询短剧扣费规则列表
     */
    @PreAuthorize("@ss.hasPermi('drama:movieCoinRule:list')")
    @GetMapping("/list")
    public TableDataInfo<DramaRoomMovieCoinRule> list(DramaRoomMovieCoinRule dramaRoomMovieCoinRule)
    {
        startPage();
        List<DramaRoomMovieCoinRule> list = dramaRoomMovieCoinRuleService.selectDramaRoomMovieCoinRuleList(dramaRoomMovieCoinRule);
        return getDataTable(list);
    }

    /**
     * 导出短剧扣费规则列表
     */
    @PreAuthorize("@ss.hasPermi('drama:movieCoinRule:export')")
    @Log(title = "短剧扣费规则", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DramaRoomMovieCoinRule dramaRoomMovieCoinRule)
    {
        List<DramaRoomMovieCoinRule> list = dramaRoomMovieCoinRuleService.selectDramaRoomMovieCoinRuleList(dramaRoomMovieCoinRule);
        ExcelUtil<DramaRoomMovieCoinRule> util = new ExcelUtil<DramaRoomMovieCoinRule>(DramaRoomMovieCoinRule.class);
        util.exportExcel(response, list, "短剧扣费规则数据");
    }

    /**
     * 获取短剧扣费规则详细信息
     */
    @PreAuthorize("@ss.hasPermi('drama:movieCoinRule:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(dramaRoomMovieCoinRuleService.selectDramaRoomMovieCoinRuleById(id));
    }

    /**
     * 新增短剧扣费规则
     */
    @PreAuthorize("@ss.hasPermi('drama:movieCoinRule:add')")
    @Log(title = "短剧扣费规则", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DramaRoomMovieCoinRule dramaRoomMovieCoinRule)
    {
        return toAjax(dramaRoomMovieCoinRuleService.insertDramaRoomMovieCoinRule(dramaRoomMovieCoinRule));
    }

    /**
     * 修改短剧扣费规则
     */
    @PreAuthorize("@ss.hasPermi('drama:movieCoinRule:edit')")
    @Log(title = "短剧扣费规则", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DramaRoomMovieCoinRule dramaRoomMovieCoinRule)
    {
        return toAjax(dramaRoomMovieCoinRuleService.updateDramaRoomMovieCoinRule(dramaRoomMovieCoinRule));
    }

    /**
     * 删除短剧扣费规则
     */
    @PreAuthorize("@ss.hasPermi('drama:movieCoinRule:remove')")
    @Log(title = "短剧扣费规则", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(dramaRoomMovieCoinRuleService.deleteDramaRoomMovieCoinRuleByIds(ids));
    }

    /**
     * 新增或保存短剧扣费规则 涉及 movieCoin videoCoinRule等
     */

    @ApiOperation("新增或保存短剧扣费规则")
    @PostMapping("/saveMovieCoinRule")
    public AjaxResult saveMovieCoinRule(@RequestBody DramaRoomMovieCoinRuleDTO dramaRoomMovieCoinRuleDTO){

        return toAjax(dramaRoomMovieCoinRuleService.saveMovieCoinRule(dramaRoomMovieCoinRuleDTO));
    }
    @ApiOperation("获取短剧下的扣费规则")
    @GetMapping("/getDramaRoomMovieCoinRule")
    public AjaxResult getDramaRoomMovieCoinRule(Long movieId){
      Map<String,Object> data =  dramaRoomMovieCoinRuleService.getDramaRoomMovieCoinRule(movieId);
      return AjaxResult.success(data);
    }
}
