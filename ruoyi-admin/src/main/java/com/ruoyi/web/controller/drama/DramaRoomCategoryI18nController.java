package com.ruoyi.web.controller.drama;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.drama.dto.DramaRoomGeneralI18nDTO;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.drama.domain.DramaRoomCategoryI18n;
import com.ruoyi.drama.service.IDramaRoomCategoryI18nService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 分类多语言Controller
 *
 * <AUTHOR>
 * @date 2025-08-05
 */
@RestController
@RequestMapping("/drama/categoryI18n")
public class DramaRoomCategoryI18nController extends BaseController<DramaRoomCategoryI18n> {
    @Autowired
    private IDramaRoomCategoryI18nService dramaRoomCategoryI18nService;

    /**
     * 查询分类多语言列表
     */
    @PreAuthorize("@ss.hasPermi('drama:categoryI18n:list')")
    @GetMapping("/list")
    public TableDataInfo<DramaRoomCategoryI18n> list(DramaRoomCategoryI18n dramaRoomCategoryI18n) {
        startPage();
        List<DramaRoomCategoryI18n> list = dramaRoomCategoryI18nService.selectDramaRoomCategoryI18nList(dramaRoomCategoryI18n);
        return getDataTable(list);
    }

    /**
     * 导出分类多语言列表
     */
    @PreAuthorize("@ss.hasPermi('drama:categoryI18n:export')")
    @Log(title = "分类多语言", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DramaRoomCategoryI18n dramaRoomCategoryI18n) {
        List<DramaRoomCategoryI18n> list = dramaRoomCategoryI18nService.selectDramaRoomCategoryI18nList(dramaRoomCategoryI18n);
        ExcelUtil<DramaRoomCategoryI18n> util = new ExcelUtil<DramaRoomCategoryI18n>(DramaRoomCategoryI18n.class);
        util.exportExcel(response, list, "分类多语言数据");
    }

    /**
     * 获取分类多语言详细信息
     */
    @PreAuthorize("@ss.hasPermi('drama:categoryI18n:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(dramaRoomCategoryI18nService.selectDramaRoomCategoryI18nById(id));
    }

    /**
     * 新增分类多语言
     */
    @PreAuthorize("@ss.hasPermi('drama:categoryI18n:add')")
    @Log(title = "分类多语言", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DramaRoomCategoryI18n dramaRoomCategoryI18n) {
        return toAjax(dramaRoomCategoryI18nService.insertDramaRoomCategoryI18n(dramaRoomCategoryI18n));
    }

    /**
     * 修改分类多语言
     */
    @PreAuthorize("@ss.hasPermi('drama:categoryI18n:edit')")
    @Log(title = "分类多语言", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DramaRoomCategoryI18n dramaRoomCategoryI18n) {
        return toAjax(dramaRoomCategoryI18nService.updateDramaRoomCategoryI18n(dramaRoomCategoryI18n));
    }

    /**
     * 删除分类多语言
     */
    @PreAuthorize("@ss.hasPermi('drama:categoryI18n:remove')")
    @Log(title = "分类多语言", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(dramaRoomCategoryI18nService.deleteDramaRoomCategoryI18nByIds(ids));
    }

    /**
     * 单语种翻译
     */
    @PreAuthorize("@ss.hasPermi('drama:categoryI18n:translate')")
    @Log(title = "分类多语言", businessType = BusinessType.INSERT)
    @PostMapping("/translate")
    public AjaxResult translate(@RequestBody DramaRoomGeneralI18nDTO dto) {
        dto.setUserId(getUserId());
        return dramaRoomCategoryI18nService.translate(dto);
    }

    /**
     * 获取需要翻译的语种
     */
    @PreAuthorize("@ss.hasPermi('drama:areaI18n:translateList')")
    @GetMapping("/list/translate/{id}")
    public AjaxResult listTranslate(@PathVariable("id") Long id) {
        return dramaRoomCategoryI18nService.listTranslate(id);
    }
}
