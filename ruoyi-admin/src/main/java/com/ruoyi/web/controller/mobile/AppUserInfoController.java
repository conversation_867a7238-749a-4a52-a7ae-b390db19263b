package com.ruoyi.web.controller.mobile;


import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.drama.vo.UserInfoVO;
import com.ruoyi.drama.service.IAppUserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Api(tags = "app用户信息相关接口")
@RestController
@RequestMapping("/mobile/user")
public class AppUserInfoController {

    @Autowired
    private IAppUserService appUserService;

    /**
     * 获取用户信息
     */
    @ApiOperation("获取用户信息")
    @PostMapping("/get-info")
    public R<UserInfoVO> getUserInfo()
    {
        UserInfoVO userInfoVO = appUserService.getLoginUserInfo();

        return R<PERSON>ok(userInfoVO);
    }
}
