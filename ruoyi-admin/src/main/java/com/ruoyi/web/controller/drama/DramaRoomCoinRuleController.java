package com.ruoyi.web.controller.drama;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.drama.domain.DramaRoomCoinRule;
import com.ruoyi.drama.service.IDramaRoomCoinRuleService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 充值金币规则Controller
 *
 * <AUTHOR>
 * @date 2025-07-29
 */
@Api(tags = "用户反馈")
@RestController
@RequestMapping("/drama/coinRule")
public class DramaRoomCoinRuleController extends BaseController
{
    @Autowired
    private IDramaRoomCoinRuleService dramaRoomCoinRuleService;

    /**
     * 查询充值金币规则列表
     */
    @ApiOperation("查询充值金币规则列表")
    @PreAuthorize("@ss.hasPermi('system:rule:list')")
    @GetMapping("/list")
    public TableDataInfo list(DramaRoomCoinRule dramaRoomCoinRule)
    {
        startPage();
        List<DramaRoomCoinRule> list = dramaRoomCoinRuleService.selectDramaRoomCoinRuleList(dramaRoomCoinRule);
        return getDataTable(list);
    }

    /**
     * 导出充值金币规则列表
     */
    @ApiOperation("导出充值金币规则列表")
    @PreAuthorize("@ss.hasPermi('system:rule:export')")
    @Log(title = "充值金币规则", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DramaRoomCoinRule dramaRoomCoinRule)
    {
        List<DramaRoomCoinRule> list = dramaRoomCoinRuleService.selectDramaRoomCoinRuleList(dramaRoomCoinRule);
        ExcelUtil<DramaRoomCoinRule> util = new ExcelUtil<DramaRoomCoinRule>(DramaRoomCoinRule.class);
        util.exportExcel(response, list, "充值金币规则数据");
    }

    /**
     * 获取充值金币规则详细信息
     */
    @ApiOperation("获取充值金币规则详细信息")
    @PreAuthorize("@ss.hasPermi('system:rule:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(dramaRoomCoinRuleService.selectDramaRoomCoinRuleById(id));
    }

    /**
     * 新增充值金币规则
     */
    @ApiOperation("新增充值金币规则")
    @PreAuthorize("@ss.hasPermi('system:rule:add')")
    @Log(title = "充值金币规则", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DramaRoomCoinRule dramaRoomCoinRule)
    {
        return toAjax(dramaRoomCoinRuleService.insertDramaRoomCoinRule(dramaRoomCoinRule));
    }

    /**
     * 修改充值金币规则
     */
    @ApiOperation("修改充值金币规则")
    @PreAuthorize("@ss.hasPermi('system:rule:edit')")
    @Log(title = "充值金币规则", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DramaRoomCoinRule dramaRoomCoinRule)
    {
        return toAjax(dramaRoomCoinRuleService.updateDramaRoomCoinRule(dramaRoomCoinRule));
    }

    /**
     * 删除充值金币规则
     */
    @ApiOperation("删除充值金币规则")
    @PreAuthorize("@ss.hasPermi('system:rule:remove')")
    @Log(title = "充值金币规则", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(dramaRoomCoinRuleService.deleteDramaRoomCoinRuleByIds(ids));
    }
}
