package com.ruoyi.web.controller.drama;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.drama.domain.DramaRoomFeedback;
import com.ruoyi.drama.service.IDramaRoomFeedbackService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 用户反馈Controller
 *
 * <AUTHOR>
 * @date 2025-07-28
 */
@Api(tags = "用户反馈")
@RestController
@RequestMapping("/drama/feedback")
public class DramaRoomFeedbackController extends BaseController
{
    @Autowired
    private IDramaRoomFeedbackService dramaRoomFeedbackService;

    /**
     * 查询用户反馈列表
     */
    @ApiOperation("查询用户反馈列表")
    @PreAuthorize("@ss.hasPermi('system:feedback:list')")
    @GetMapping("/list")
    public TableDataInfo list(DramaRoomFeedback dramaRoomFeedback)
    {
        startPage();
        List<DramaRoomFeedback> list = dramaRoomFeedbackService.selectDramaRoomFeedbackList(dramaRoomFeedback);
        return getDataTable(list);
    }

    /**
     * 导出用户反馈列表
     */
    @ApiOperation("导出用户反馈列表")
    @PreAuthorize("@ss.hasPermi('system:feedback:export')")
    @Log(title = "用户反馈", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DramaRoomFeedback dramaRoomFeedback)
    {
        List<DramaRoomFeedback> list = dramaRoomFeedbackService.selectDramaRoomFeedbackList(dramaRoomFeedback);
        ExcelUtil<DramaRoomFeedback> util = new ExcelUtil<DramaRoomFeedback>(DramaRoomFeedback.class);
        util.exportExcel(response, list, "用户反馈数据");
    }

    /**
     * 获取用户反馈详细信息
     */
    @ApiOperation("获取用户反馈详细信息")
    @PreAuthorize("@ss.hasPermi('system:feedback:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(dramaRoomFeedbackService.selectDramaRoomFeedbackById(id));
    }

    /**
     * 新增用户反馈
     */
    @ApiOperation("新增用户反馈")
    @PreAuthorize("@ss.hasPermi('system:feedback:add')")
    @Log(title = "用户反馈", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DramaRoomFeedback dramaRoomFeedback)
    {
        return toAjax(dramaRoomFeedbackService.insertDramaRoomFeedback(dramaRoomFeedback));
    }

    /**
     * 修改用户反馈
     */
    @ApiOperation("修改用户反馈")
    @PreAuthorize("@ss.hasPermi('system:feedback:edit')")
    @Log(title = "用户反馈", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DramaRoomFeedback dramaRoomFeedback)
    {
        return toAjax(dramaRoomFeedbackService.updateDramaRoomFeedback(dramaRoomFeedback));
    }

    /**
     * 删除用户反馈
     */
    @ApiOperation("删除用户反馈")
    @PreAuthorize("@ss.hasPermi('system:feedback:remove')")
    @Log(title = "用户反馈", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(dramaRoomFeedbackService.deleteDramaRoomFeedbackByIds(ids));
    }
}
