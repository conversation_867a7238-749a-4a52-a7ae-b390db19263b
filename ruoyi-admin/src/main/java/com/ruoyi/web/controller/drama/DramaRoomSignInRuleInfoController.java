package com.ruoyi.web.controller.drama;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.drama.domain.DramaRoomSignInRuleInfo;
import com.ruoyi.drama.service.IDramaRoomSignInRuleInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 签到规则明细Controller
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
@Api(tags = "签到规则明细")
@RestController
@RequestMapping("/drama/signInRuleInfo")
public class DramaRoomSignInRuleInfoController extends BaseController
{
    @Autowired
    private IDramaRoomSignInRuleInfoService dramaRoomSignInRuleInfoService;

    /**
     * 查询签到规则明细列表
     */
    @ApiOperation("查询签到规则明细列表")
    @PreAuthorize("@ss.hasPermi('system:info:list')")
    @GetMapping("/list")
    public TableDataInfo list(DramaRoomSignInRuleInfo dramaRoomSignInRuleInfo)
    {
        startPage();
        List<DramaRoomSignInRuleInfo> list = dramaRoomSignInRuleInfoService.selectDramaRoomSignInRuleInfoList(dramaRoomSignInRuleInfo);
        return getDataTable(list);
    }

    /**
     * 导出签到规则明细列表
     */
    @ApiOperation("导出签到规则明细列表")
    @PreAuthorize("@ss.hasPermi('system:info:export')")
    @Log(title = "签到规则明细", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DramaRoomSignInRuleInfo dramaRoomSignInRuleInfo)
    {
        List<DramaRoomSignInRuleInfo> list = dramaRoomSignInRuleInfoService.selectDramaRoomSignInRuleInfoList(dramaRoomSignInRuleInfo);
        ExcelUtil<DramaRoomSignInRuleInfo> util = new ExcelUtil<DramaRoomSignInRuleInfo>(DramaRoomSignInRuleInfo.class);
        util.exportExcel(response, list, "签到规则明细数据");
    }

    /**
     * 获取签到规则明细详细信息
     */
    @ApiOperation("获取签到规则明细详细信息")
    @PreAuthorize("@ss.hasPermi('system:info:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(dramaRoomSignInRuleInfoService.selectDramaRoomSignInRuleInfoById(id));
    }

    /**
     * 新增签到规则明细
     */
    @ApiOperation("新增签到规则明细")
    @PreAuthorize("@ss.hasPermi('system:info:add')")
    @Log(title = "签到规则明细", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DramaRoomSignInRuleInfo dramaRoomSignInRuleInfo)
    {
        return toAjax(dramaRoomSignInRuleInfoService.insertDramaRoomSignInRuleInfo(dramaRoomSignInRuleInfo));
    }

    /**
     * 修改签到规则明细
     */
    @ApiOperation("修改签到规则明细")
    @PreAuthorize("@ss.hasPermi('system:info:edit')")
    @Log(title = "签到规则明细", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DramaRoomSignInRuleInfo dramaRoomSignInRuleInfo)
    {
        return toAjax(dramaRoomSignInRuleInfoService.updateDramaRoomSignInRuleInfo(dramaRoomSignInRuleInfo));
    }

    /**
     * 删除签到规则明细
     */
    @ApiOperation("删除签到规则明细")
    @PreAuthorize("@ss.hasPermi('system:info:remove')")
    @Log(title = "签到规则明细", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(dramaRoomSignInRuleInfoService.deleteDramaRoomSignInRuleInfoByIds(ids));
    }
}
