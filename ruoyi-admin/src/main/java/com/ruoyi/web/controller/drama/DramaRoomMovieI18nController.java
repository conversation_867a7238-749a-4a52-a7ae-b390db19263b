package com.ruoyi.web.controller.drama;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.drama.dto.DramaRoomMovieI18nDTO;
import com.ruoyi.common.utils.mobile.AppSecurityUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.drama.domain.DramaRoomMovieI18n;
import com.ruoyi.drama.service.IDramaRoomMovieI18nService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;
import software.amazon.awssdk.services.s3.endpoints.internal.Value;

/**
 * 短剧翻译Controller
 * 
 * <AUTHOR>
 * @date 2025-08-05
 */
@Api(tags = "短剧翻译相关接口")
@RestController
@RequestMapping("/drama/movieI18n")
public class DramaRoomMovieI18nController extends BaseController<DramaRoomMovieI18n>
{
    @Autowired
    private IDramaRoomMovieI18nService dramaRoomMovieI18nService;

    /**
     * 查询短剧翻译列表
     */
    @PreAuthorize("@ss.hasPermi('drama:movieI18n:list')")
    @GetMapping("/list")
    public TableDataInfo<DramaRoomMovieI18n> list(DramaRoomMovieI18n dramaRoomMovieI18n)
    {
        startPage();
        List<DramaRoomMovieI18n> list = dramaRoomMovieI18nService.selectDramaRoomMovieI18nList(dramaRoomMovieI18n);
        return getDataTable(list);
    }

    /**
     * 导出短剧翻译列表
     */
    @PreAuthorize("@ss.hasPermi('drama:movieI18n:export')")
    @Log(title = "短剧翻译", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DramaRoomMovieI18n dramaRoomMovieI18n)
    {
        List<DramaRoomMovieI18n> list = dramaRoomMovieI18nService.selectDramaRoomMovieI18nList(dramaRoomMovieI18n);
        ExcelUtil<DramaRoomMovieI18n> util = new ExcelUtil<DramaRoomMovieI18n>(DramaRoomMovieI18n.class);
        util.exportExcel(response, list, "短剧翻译数据");
    }

    /**
     * 获取短剧翻译详细信息
     */
    @PreAuthorize("@ss.hasPermi('drama:movieI18n:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(dramaRoomMovieI18nService.selectDramaRoomMovieI18nById(id));
    }

    /**
     * 新增短剧翻译
     */
    @PreAuthorize("@ss.hasPermi('drama:movieI18n:add')")
    @Log(title = "短剧翻译", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DramaRoomMovieI18n dramaRoomMovieI18n)
    {
        return toAjax(dramaRoomMovieI18nService.insertDramaRoomMovieI18n(dramaRoomMovieI18n));
    }

    /**
     * 修改短剧翻译
     */
    @PreAuthorize("@ss.hasPermi('drama:movieI18n:edit')")
    @Log(title = "短剧翻译", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DramaRoomMovieI18n dramaRoomMovieI18n)
    {
        return toAjax(dramaRoomMovieI18nService.updateDramaRoomMovieI18n(dramaRoomMovieI18n));
    }

    /**
     * 删除短剧翻译
     */
    @PreAuthorize("@ss.hasPermi('drama:movieI18n:remove')")
    @Log(title = "短剧翻译", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(dramaRoomMovieI18nService.deleteDramaRoomMovieI18nByIds(ids));
    }

    /**
     * 根据movieId获取短剧翻译列表
     * @param movieId
     * @param languageCode
     * @return
     */
    @ApiOperation(value = "根据movieId获取短剧翻译列表")
    @GetMapping("/getMovieI18nList")
    public AjaxResult getMovieI18nList(Long movieId, String languageCode)
    {
        return success(dramaRoomMovieI18nService.getMovieI18nList(movieId, languageCode));
    }

    /**
     * 审核短句翻译结果
     * @param dramaRoomMovieI18n
     * @return
     */
    @ApiOperation(value = "审核短句翻译结果")
    @PostMapping("/updateMovieI18nState")
    public AjaxResult updateMovieI18nState(@RequestBody DramaRoomMovieI18n dramaRoomMovieI18n){

        return toAjax(dramaRoomMovieI18nService.updateDramaRoomMovieI18nState(dramaRoomMovieI18n));
    }

    /**
     * 翻译短剧多语言
     */
    @ApiOperation(value = "翻译短剧多语言")
    @PostMapping("/translateMovieI18n")
    public AjaxResult translateMovieI18n(@RequestBody DramaRoomMovieI18nDTO dto){
        dto.setUserId(getUserId());
        return dramaRoomMovieI18nService.translateMovieI18n(dto);
    }

    @DeleteMapping("/delete")
    public AjaxResult deleteMovieI18n(Long movieId, String languageCode){
        return toAjax(dramaRoomMovieI18nService.deleteMovieI18n(movieId, languageCode));
    }
}
