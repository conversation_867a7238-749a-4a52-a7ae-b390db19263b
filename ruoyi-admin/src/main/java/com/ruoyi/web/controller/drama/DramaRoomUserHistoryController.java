package com.ruoyi.web.controller.drama;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.drama.domain.DramaRoomUserHistory;
import com.ruoyi.drama.domain.vo.DramaRoomUserHistoryVO;
import com.ruoyi.drama.service.IDramaRoomUserHistoryService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 用户观看历史记录Controller
 *
 * <AUTHOR>
 * @date 2025-07-04
 */
@Api(tags = "用户观看历史记录接口")
@RestController
@RequestMapping("/drama/history")
public class DramaRoomUserHistoryController extends BaseController<DramaRoomUserHistory> {
    @Autowired
    private IDramaRoomUserHistoryService dramaRoomUserHistoryService;

    /**
     * 查询用户观看历史记录列表
     */
    @ApiOperation("获取用户观看历史记录列表")
    @PreAuthorize("@ss.hasPermi('system:history:list')")
    @GetMapping("/list")
    public TableDataInfo<DramaRoomUserHistory> list(DramaRoomUserHistory dramaRoomUserHistory) {
        startPage();
        List<DramaRoomUserHistory> list = dramaRoomUserHistoryService.selectDramaRoomUserHistoryList(dramaRoomUserHistory);
        return getDataTable(list);
    }

    /**
     * 导出用户观看历史记录列表
     */
    @ApiOperation("导出用户观看历史记录列表")
    @PreAuthorize("@ss.hasPermi('system:history:export')")
    @Log(title = "用户观看历史记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DramaRoomUserHistory dramaRoomUserHistory) {
        List<DramaRoomUserHistory> list = dramaRoomUserHistoryService.selectDramaRoomUserHistoryList(dramaRoomUserHistory);
        ExcelUtil<DramaRoomUserHistory> util = new ExcelUtil<DramaRoomUserHistory>(DramaRoomUserHistory.class);
        util.exportExcel(response, list, "用户观看历史记录数据");
    }

    /**
     * 获取用户观看历史记录详细信息
     */
    @ApiOperation("获取用户观看历史记录详细信息")
    @PreAuthorize("@ss.hasPermi('system:history:query')")
    @GetMapping(value = "/{id}")
    public R<DramaRoomUserHistory> getInfo(@PathVariable("id") Long id) {
        return R.ok(dramaRoomUserHistoryService.selectDramaRoomUserHistoryById(id));
    }

    /**
     * 新增用户观看历史记录
     */
    @ApiOperation("新增用户观看历史记录")
    @PreAuthorize("@ss.hasPermi('system:history:add')")
    @Log(title = "用户观看历史记录", businessType = BusinessType.INSERT)
    @PostMapping
    public R<Integer> add(@RequestBody DramaRoomUserHistory dramaRoomUserHistory) {
        return R.toR(dramaRoomUserHistoryService.insertDramaRoomUserHistory(dramaRoomUserHistory));
    }

    /**
     * 修改用户观看历史记录
     */
    @ApiOperation("修改用户观看历史记录")
    @PreAuthorize("@ss.hasPermi('system:history:edit')")
    @Log(title = "用户观看历史记录", businessType = BusinessType.UPDATE)
    @PutMapping
    public R<Integer> edit(@RequestBody DramaRoomUserHistory dramaRoomUserHistory) {
        return R.toR(dramaRoomUserHistoryService.updateDramaRoomUserHistory(dramaRoomUserHistory));
    }

    /**
     * 删除用户观看历史记录
     */
    @ApiOperation("删除用户观看历史记录")
    @PreAuthorize("@ss.hasPermi('system:history:remove')")
    @Log(title = "用户观看历史记录", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Integer> remove(@PathVariable Long[] ids) {
        return R.toR(dramaRoomUserHistoryService.deleteDramaRoomUserHistoryByIds(ids));
    }

    @GetMapping("/getUserHistory/{userId}")
    @ApiOperation("获取用户观看历史记录")
    public R<List<DramaRoomUserHistoryVO>> queryUserHistory(@PathVariable("userId") Long userId) {
        return R.ok(dramaRoomUserHistoryService.queryUserHistory(userId));
    }
}
