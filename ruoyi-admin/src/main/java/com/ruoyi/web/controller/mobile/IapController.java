package com.ruoyi.web.controller.mobile;

import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.drama.dto.IapNotifyDTO;
import com.ruoyi.common.drama.dto.IapVerifyDTO;
import com.ruoyi.common.enums.PayChannelEnum;
import com.ruoyi.drama.domain.DramaRoomPayTemplateAmt;
import com.ruoyi.drama.manager.factory.PaymentStrategyFactory;
import com.ruoyi.drama.mapper.DramaRoomPayTemplateAmtMapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * 内购验证控制器（处理苹果/谷歌内购的后端验证）
 */
@Slf4j
@Api(tags = "内购相关接口")
@RestController
@RequestMapping("/mobile/iap")
@RequiredArgsConstructor
public class IapController {

    @Autowired
    private PaymentStrategyFactory strategyFactory;

    @Autowired
    private DramaRoomPayTemplateAmtMapper amtMapper;

    /**
     * 内购凭证验证
     */
    @ApiOperation("内购凭证验证")
    @PostMapping("/verify")
    public R<?> verifyPayment(@Validated @RequestBody IapVerifyDTO paymentVO) {
        return strategyFactory.getStrategy(paymentVO.getChannelCode())
                .verifyPayment(paymentVO);
    }

    /**
     * 苹果自动续订订阅回调通知
     * 用于接收服务器发送的订阅状态变更通知
     */
    @ApiOperation("苹果续订回调通知")
    @PostMapping("/apple/notify")
    public R<?> appleSubscriptionNotify(Map<String, Object> notificationData) {
        IapNotifyDTO dto = new IapNotifyDTO();
        dto.setNotificationUUID((String) notificationData.get("notificationUUID"));
        dto.setNotificationType((String) notificationData.get("notificationType"));
        dto.setData((Map<String, Object>) notificationData.get("data"));
        return strategyFactory.getStrategy(PayChannelEnum.APPLE_IAP.getCode())
                .handleSubscriptionNotify(dto);
    }

    /**
     * 苹果自动续订订阅回调通知
     * 用于接收服务器发送的订阅状态变更通知
     */
    @ApiOperation("谷歌续订回调通知")
    @PostMapping("/google/notify")
    public R<?> googleSubscriptionNotify(Map<String, Object> notificationData) {

        /*IapNotifyDTO dto = new IapNotifyDTO();
        dto.setNotificationUUID((String) notificationData.get("notificationUUID"));
        dto.setNotificationType((String) notificationData.get("notificationType"));
        dto.setData((String) notificationData.get("data"));*/
        return strategyFactory.getStrategy(PayChannelEnum.GOOGLE_IAP.getCode())
                .handleSubscriptionNotify(null);
    }

    /**
     * 内购商品列表
     */
    @ApiOperation("内购商品列表")
    @PostMapping("/goods")
    public R<?> list() {
        DramaRoomPayTemplateAmt dramaRoomPayTemplateAmt = amtMapper.selectDramaRoomPayTemplateAmtById(1L);
        return R.ok(dramaRoomPayTemplateAmt);
    }
}
