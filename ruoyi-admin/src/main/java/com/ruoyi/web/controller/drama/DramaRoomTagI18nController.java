package com.ruoyi.web.controller.drama;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.drama.dto.DramaRoomGeneralI18nDTO;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.drama.domain.DramaRoomTagI18n;
import com.ruoyi.drama.service.IDramaRoomTagI18nService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 标签多语言Controller
 *
 * <AUTHOR>
 * @date 2025-08-05
 */
@RestController
@RequestMapping("/drama/tagI18n")
public class DramaRoomTagI18nController extends BaseController<DramaRoomTagI18n> {
    @Autowired
    private IDramaRoomTagI18nService dramaRoomTagI18nService;

    /**
     * 查询标签多语言列表
     */
    @PreAuthorize("@ss.hasPermi('drama:tagI18n:list')")
    @GetMapping("/list")
    public TableDataInfo<DramaRoomTagI18n> list(DramaRoomTagI18n dramaRoomTagI18n) {
        startPage();
        List<DramaRoomTagI18n> list = dramaRoomTagI18nService.selectDramaRoomTagI18nList(dramaRoomTagI18n);
        return getDataTable(list);
    }

    /**
     * 导出标签多语言列表
     */
    @PreAuthorize("@ss.hasPermi('drama:tagI18n:export')")
    @Log(title = "标签多语言", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DramaRoomTagI18n dramaRoomTagI18n) {
        List<DramaRoomTagI18n> list = dramaRoomTagI18nService.selectDramaRoomTagI18nList(dramaRoomTagI18n);
        ExcelUtil<DramaRoomTagI18n> util = new ExcelUtil<DramaRoomTagI18n>(DramaRoomTagI18n.class);
        util.exportExcel(response, list, "标签多语言数据");
    }

    /**
     * 获取标签多语言详细信息
     */
    @PreAuthorize("@ss.hasPermi('drama:tagI18n:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(dramaRoomTagI18nService.selectDramaRoomTagI18nById(id));
    }

    /**
     * 新增标签多语言
     */
    @PreAuthorize("@ss.hasPermi('drama:tagI18n:add')")
    @Log(title = "标签多语言", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DramaRoomTagI18n dramaRoomTagI18n) {
        return toAjax(dramaRoomTagI18nService.insertDramaRoomTagI18n(dramaRoomTagI18n));
    }

    /**
     * 修改标签多语言
     */
    @PreAuthorize("@ss.hasPermi('drama:tagI18n:edit')")
    @Log(title = "标签多语言", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DramaRoomTagI18n dramaRoomTagI18n) {
        return toAjax(dramaRoomTagI18nService.updateDramaRoomTagI18n(dramaRoomTagI18n));
    }

    /**
     * 删除标签多语言
     */
    @PreAuthorize("@ss.hasPermi('drama:tagI18n:remove')")
    @Log(title = "标签多语言", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(dramaRoomTagI18nService.deleteDramaRoomTagI18nByIds(ids));
    }

    /**
     * 单语种翻译
     */
    @PreAuthorize("@ss.hasPermi('drama:tagI18n:translate')")
    @Log(title = "标签多语言", businessType = BusinessType.UPDATE)
    @PostMapping("/translate")
    public AjaxResult translate(@RequestBody DramaRoomGeneralI18nDTO dto) {
        dto.setUserId(getUserId());
        return dramaRoomTagI18nService.translate(dto);
    }

    /**
     * 多语种翻译列表
     */
    @PreAuthorize("@ss.hasPermi('drama:tagI18n:translateList')")
    @GetMapping("/list/translate/{id}")
    public AjaxResult listTranslate(@PathVariable("id") Long id) {
        return dramaRoomTagI18nService.listTranslate(id);
    }
}
