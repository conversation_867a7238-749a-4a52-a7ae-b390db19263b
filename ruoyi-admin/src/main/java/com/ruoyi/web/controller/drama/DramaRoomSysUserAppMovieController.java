package com.ruoyi.web.controller.drama;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.drama.domain.DramaRoomSysUserAppMovie;
import com.ruoyi.drama.service.IDramaRoomSysUserAppMovieService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 运营用户关联app，短剧,组长组员Controller
 *
 * <AUTHOR>
 * @date 2025-08-11
 */
@Api(tags = "运营用户关联app，短剧,组长组员")
@RestController
@RequestMapping("/drama/sysUserAppMovie")
public class DramaRoomSysUserAppMovieController extends BaseController
{
    @Autowired
    private IDramaRoomSysUserAppMovieService dramaRoomSysUserAppMovieService;

    /**
     * 运营用户关联app，短剧,组长组员列表
     */
    @ApiOperation("运营用户关联app，短剧,组长组员列表")
    @PreAuthorize("@ss.hasPermi('system:sysUserAppMovie:list')")
    @GetMapping("/list")
    public TableDataInfo list(DramaRoomSysUserAppMovie dramaRoomSysUserAppMovie)
    {
        startPage();
        List<DramaRoomSysUserAppMovie> list = dramaRoomSysUserAppMovieService.selectDramaRoomSysUserAppMovieList(dramaRoomSysUserAppMovie);
        return getDataTable(list);
    }

    /**
     * 导出运营用户关联app，短剧,组长组员列表
     */
    @ApiOperation("导出运营用户关联app，短剧,组长组员列表")
    @PreAuthorize("@ss.hasPermi('system:sysUserAppMovie:export')")
    @Log(title = "运营用户关联app，短剧,组长组员", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DramaRoomSysUserAppMovie dramaRoomSysUserAppMovie)
    {
        List<DramaRoomSysUserAppMovie> list = dramaRoomSysUserAppMovieService.selectDramaRoomSysUserAppMovieList(dramaRoomSysUserAppMovie);
        ExcelUtil<DramaRoomSysUserAppMovie> util = new ExcelUtil<DramaRoomSysUserAppMovie>(DramaRoomSysUserAppMovie.class);
        util.exportExcel(response, list, "运营用户关联app，短剧,组长组员数据");
    }

    /**
     * 获取运营用户关联app，短剧,组长组员详细信息
     */
    @ApiOperation("获取运营用户关联app，短剧,组长组员详细信息")
    @PreAuthorize("@ss.hasPermi('system:sysUserAppMovie:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(dramaRoomSysUserAppMovieService.selectDramaRoomSysUserAppMovieById(id));
    }

    /**
     * 新增运营用户关联app，短剧,组长组员
     */
    @ApiOperation("新增运营用户关联app，短剧,组长组员")
    @PreAuthorize("@ss.hasPermi('system:sysUserAppMovie:add')")
    @Log(title = "运营用户关联app，短剧,组长组员", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DramaRoomSysUserAppMovie dramaRoomSysUserAppMovie)
    {
        return toAjax(dramaRoomSysUserAppMovieService.insertDramaRoomSysUserAppMovie(dramaRoomSysUserAppMovie));
    }

    @ApiOperation("批量插入")
    @PreAuthorize("@ss.hasPermi('system:sysUserAppMovie:add')")
    @Log(title = "批量插入", businessType = BusinessType.INSERT)
    @PostMapping("addList")
    public AjaxResult addList(@RequestBody DramaRoomSysUserAppMovie shortSysUserAppMovie)
    {
        return toAjax(dramaRoomSysUserAppMovieService.addList(shortSysUserAppMovie.getShortSysUserAppMovieList()));
    }

    /**
     * 修改运营用户关联app，短剧,组长组员
     */
    @ApiOperation("修改运营用户关联app，短剧,组长组员")
    @PreAuthorize("@ss.hasPermi('system:sysUserAppMovie:edit')")
    @Log(title = "运营用户关联app，短剧,组长组员", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DramaRoomSysUserAppMovie dramaRoomSysUserAppMovie)
    {
        return toAjax(dramaRoomSysUserAppMovieService.updateDramaRoomSysUserAppMovie(dramaRoomSysUserAppMovie));
    }

    /**
     * 删除运营用户关联app，短剧,组长组员
     */
    @ApiOperation("删除运营用户关联app，短剧,组长组员")
    @PreAuthorize("@ss.hasPermi('system:sysUserAppMovie:remove')")
    @Log(title = "运营用户关联app，短剧,组长组员", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(dramaRoomSysUserAppMovieService.deleteDramaRoomSysUserAppMovieByIds(ids));
    }

    @ApiOperation("根据岗位id查询下级岗位对应的人员")
    @PreAuthorize("@ss.hasPermi('system:sysUserAppMovie:list')")
    @GetMapping("/getChildUserList")
    public AjaxResult getChildUserList(@RequestParam String postId)
    {
        return success(dramaRoomSysUserAppMovieService.getChildUserList(postId));
    }

    @ApiOperation("继承-深链创建人未更改-------查询相关接口未添加")
    @PreAuthorize("@ss.hasPermi('system:sysUserAppMovie:inherit')")
    @GetMapping("/inherit")
    public AjaxResult inherit(@RequestParam Long pId,@RequestParam Long cId)
    {
        return success(dramaRoomSysUserAppMovieService.inherit(pId,cId));
    }



}