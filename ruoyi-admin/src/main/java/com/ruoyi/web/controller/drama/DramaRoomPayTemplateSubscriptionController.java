package com.ruoyi.web.controller.drama;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.drama.domain.DramaRoomPayTemplateSubscription;
import com.ruoyi.drama.service.IDramaRoomPayTemplateSubscriptionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 订阅模板：定义不同周期和等级下的金币订阅计划Controller
 *
 * <AUTHOR>
 * @date 2025-09-01
 */
@Api(value = "订阅模板：定义不同周期和等级下的金币订阅计划管理", tags = "订阅模板：定义不同周期和等级下的金币订阅计划")
@RestController
@RequestMapping("/drama/subscription")
public class DramaRoomPayTemplateSubscriptionController extends BaseController<DramaRoomPayTemplateSubscription> {
    @Autowired
    private IDramaRoomPayTemplateSubscriptionService dramaRoomPayTemplateSubscriptionService;

    /**
     * 查询订阅模板：定义不同周期和等级下的金币订阅计划列表
     */
    @ApiOperation(value = "获取订阅模板：定义不同周期和等级下的金币订阅计划列表")
    @PreAuthorize("@ss.hasPermi('drama:subscription:list')")
    @GetMapping("/list")
    public TableDataInfo<DramaRoomPayTemplateSubscription> list(DramaRoomPayTemplateSubscription dramaRoomPayTemplateSubscription) {
        startPage();
        List<DramaRoomPayTemplateSubscription> list = dramaRoomPayTemplateSubscriptionService.selectDramaRoomPayTemplateSubscriptionList(dramaRoomPayTemplateSubscription);
        return getDataTable(list);
    }

    /**
     * 导出订阅模板：定义不同周期和等级下的金币订阅计划列表
     */
    @ApiOperation(value = "导出订阅模板：定义不同周期和等级下的金币订阅计划列表")
    @PreAuthorize("@ss.hasPermi('drama:subscription:export')")
    @Log(title = "订阅模板：定义不同周期和等级下的金币订阅计划", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DramaRoomPayTemplateSubscription dramaRoomPayTemplateSubscription) {
        List<DramaRoomPayTemplateSubscription> list = dramaRoomPayTemplateSubscriptionService.selectDramaRoomPayTemplateSubscriptionList(dramaRoomPayTemplateSubscription);
        ExcelUtil<DramaRoomPayTemplateSubscription> util = new ExcelUtil<DramaRoomPayTemplateSubscription>(DramaRoomPayTemplateSubscription.class);
        util.exportExcel(response, list, "订阅模板：定义不同周期和等级下的金币订阅计划数据");
    }

    /**
     * 获取订阅模板：定义不同周期和等级下的金币订阅计划详细信息
     */
    @ApiOperation(value = "获取订阅模板：定义不同周期和等级下的金币订阅计划详细信息")
    @PreAuthorize("@ss.hasPermi('drama:subscription:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") String id) {
        return success(dramaRoomPayTemplateSubscriptionService.selectDramaRoomPayTemplateSubscriptionById(id));
    }

    /**
     * 新增订阅模板：定义不同周期和等级下的金币订阅计划
     */
    @ApiOperation(value = "新增订阅模板：定义不同周期和等级下的金币订阅计划")
    @PreAuthorize("@ss.hasPermi('drama:subscription:add')")
    @Log(title = "订阅模板：定义不同周期和等级下的金币订阅计划", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DramaRoomPayTemplateSubscription dramaRoomPayTemplateSubscription) {
        return toAjax(dramaRoomPayTemplateSubscriptionService.insertDramaRoomPayTemplateSubscription(dramaRoomPayTemplateSubscription));
    }

    /**
     * 修改订阅模板：定义不同周期和等级下的金币订阅计划
     */
    @ApiOperation(value = "修改订阅模板：定义不同周期和等级下的金币订阅计划")
    @PreAuthorize("@ss.hasPermi('drama:subscription:edit')")
    @Log(title = "订阅模板：定义不同周期和等级下的金币订阅计划", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DramaRoomPayTemplateSubscription dramaRoomPayTemplateSubscription) {
        return toAjax(dramaRoomPayTemplateSubscriptionService.updateDramaRoomPayTemplateSubscription(dramaRoomPayTemplateSubscription));
    }

    /**
     * 删除订阅模板：定义不同周期和等级下的金币订阅计划
     */
    @ApiOperation(value = "删除订阅模板：定义不同周期和等级下的金币订阅计划")
    @PreAuthorize("@ss.hasPermi('drama:subscription:remove')")
    @Log(title = "订阅模板：定义不同周期和等级下的金币订阅计划", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable String[] ids) {
        return toAjax(dramaRoomPayTemplateSubscriptionService.deleteDramaRoomPayTemplateSubscriptionByIds(ids));
    }
}