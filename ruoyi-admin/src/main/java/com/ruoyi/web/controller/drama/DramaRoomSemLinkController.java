package com.ruoyi.web.controller.drama;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.drama.domain.vo.DramaRoomSemLinkVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.drama.domain.DramaRoomSemLink;
import com.ruoyi.drama.service.IDramaRoomSemLinkService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 推广链接Controller
 * 
 * <AUTHOR>
 * @date 2025-08-11
 */
@Api(value = "推广链接管理", tags = "推广链接管理")
@RestController
@RequestMapping("/drama/link")
public class DramaRoomSemLinkController extends BaseController<DramaRoomSemLinkVO>
{
    @Autowired
    private IDramaRoomSemLinkService dramaRoomSemLinkService;

    /**
     * 查询推广链接列表
     */
    @ApiOperation(value = "推广链接列表")
    @PreAuthorize("@ss.hasPermi('drama:link:list')")
    @GetMapping("/list")
    public TableDataInfo<DramaRoomSemLinkVO> list(DramaRoomSemLink dramaRoomSemLink)
    {
        startPage();
        List<DramaRoomSemLinkVO> list = dramaRoomSemLinkService.queryDramaRoomSemLink(dramaRoomSemLink);
        return getDataTable(list);
    }

    /**
     * 导出推广链接列表
     */
    @ApiOperation(value = "推广链接列表")
    @PreAuthorize("@ss.hasPermi('drama:link:export')")
    @Log(title = "推广链接", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DramaRoomSemLink dramaRoomSemLink)
    {
        List<DramaRoomSemLinkVO> list = dramaRoomSemLinkService.queryDramaRoomSemLink(dramaRoomSemLink);
        ExcelUtil<DramaRoomSemLinkVO> util = new ExcelUtil<>(DramaRoomSemLinkVO.class);
        util.exportExcel(response, list, "推广链接数据");
    }

    /**
     * 获取推广链接详细信息
     */
    @ApiOperation(value = "推广链接详细信息")
    @PreAuthorize("@ss.hasPermi('drama:link:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(dramaRoomSemLinkService.selectDramaRoomSemLinkById(id));
    }

    /**
     * 新增推广链接
     */
    @ApiOperation(value = "新增推广链接")
    @PreAuthorize("@ss.hasPermi('drama:link:add')")
    @Log(title = "推广链接", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DramaRoomSemLink dramaRoomSemLink)
    {
        return toAjax(dramaRoomSemLinkService.insertDramaRoomSemLink(dramaRoomSemLink));
    }

    /**
     * 修改推广链接
     */
    @ApiOperation(value = "修改推广链接")
    @PreAuthorize("@ss.hasPermi('drama:link:edit')")
    @Log(title = "推广链接", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DramaRoomSemLink dramaRoomSemLink)
    {
        return toAjax(dramaRoomSemLinkService.updateDramaRoomSemLink(dramaRoomSemLink));
    }

    /**
     * 删除推广链接
     */
    @ApiOperation(value = "删除推广链接")
    @PreAuthorize("@ss.hasPermi('drama:link:remove')")
    @Log(title = "推广链接", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(dramaRoomSemLinkService.deleteDramaRoomSemLinkByIds(ids));
    }
}