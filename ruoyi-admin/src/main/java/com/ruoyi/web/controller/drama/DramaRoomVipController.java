package com.ruoyi.web.controller.drama;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.drama.domain.DramaRoomVip;
import com.ruoyi.drama.service.IDramaRoomVipService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * vip管理Controller
 * 
 * <AUTHOR>
 * @date 2025-08-11
 */
@Api(value = "付费计划管理", tags = "付费计划管理")
@RestController
@RequestMapping("/drama/vip")
public class DramaRoomVipController extends BaseController<DramaRoomVip>
{
    @Autowired
    private IDramaRoomVipService dramaRoomVipService;

    /**
     * 查询vip管理列表
     */
    @ApiOperation(value = "分页获取付费计划列表")
    @PreAuthorize("@ss.hasPermi('drama:vip:list')")
    @GetMapping("/list")
    public TableDataInfo<DramaRoomVip> list(DramaRoomVip dramaRoomVip)
    {
        startPage();
        List<DramaRoomVip> list = dramaRoomVipService.queryAllDramaRoomVip(dramaRoomVip);
        return getDataTable(list);
    }

    @ApiOperation(value = "查询所有付费计划列表")
    @PreAuthorize("@ss.hasPermi('drama:vip:listAll')")
    @GetMapping("/listAll")
    public AjaxResult listAll(DramaRoomVip dramaRoomVip)
    {
        List<DramaRoomVip> list = dramaRoomVipService.queryAllDramaRoomVip(dramaRoomVip);
        return AjaxResult.success(list);
    }
    /**
     * 导出vip管理列表
     */
    @ApiOperation(value = "付费计划列表")
    @PreAuthorize("@ss.hasPermi('drama:vip:export')")
    @Log(title = "vip管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DramaRoomVip dramaRoomVip)
    {
        List<DramaRoomVip> list = dramaRoomVipService.selectDramaRoomVipList(dramaRoomVip);
        ExcelUtil<DramaRoomVip> util = new ExcelUtil<DramaRoomVip>(DramaRoomVip.class);
        util.exportExcel(response, list, "vip管理数据");
    }

    /**
     * 获取vip管理详细信息
     */
    @ApiOperation(value = "付费计划详细信息")
    @PreAuthorize("@ss.hasPermi('drama:vip:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(dramaRoomVipService.selectDramaRoomVipById(id));
    }

    /**
     * 新增vip管理
     */
    @ApiOperation(value = "新增付费计划")
    @PreAuthorize("@ss.hasPermi('drama:vip:add')")
    @Log(title = "vip管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DramaRoomVip dramaRoomVip)
    {
        return toAjax(dramaRoomVipService.insertDramaRoomVip(dramaRoomVip));
    }

    /**
     * 修改vip管理
     */
    @ApiOperation(value = "修改付费计划")
    @PreAuthorize("@ss.hasPermi('drama:vip:edit')")
    @Log(title = "vip管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DramaRoomVip dramaRoomVip)
    {
        return toAjax(dramaRoomVipService.updateDramaRoomVip(dramaRoomVip));
    }

    /**
     * 删除vip管理
     */
    @ApiOperation(value = "删除付费计划")
    @PreAuthorize("@ss.hasPermi('drama:vip:remove')")
    @Log(title = "vip管理", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(dramaRoomVipService.deleteDramaRoomVipByIds(ids));
    }
}