package com.ruoyi.web.controller.drama;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.drama.domain.SystemErrorLog;
import com.ruoyi.drama.service.ISystemErrorLogService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 系统错误日志Controller
 *
 * <AUTHOR>
 * @date 2025-08-06
 */
@Api(tags = "系统错误日志记录")
@RestController
@RequestMapping("/drama/err-log")
public class SystemErrorLogController extends BaseController
{
    @Autowired
    private ISystemErrorLogService systemErrorLogService;

    /**
     * 查询系统错误日志列表
     */
    @PreAuthorize("@ss.hasPermi('system:log:list')")
    @GetMapping("/list")
    public TableDataInfo list(SystemErrorLog systemErrorLog)
    {
        startPage();
        List<SystemErrorLog> list = systemErrorLogService.selectSystemErrorLogList(systemErrorLog);
        return getDataTable(list);
    }

    /**
     * 导出系统错误日志列表
     */
    @PreAuthorize("@ss.hasPermi('system:log:export')")
    @Log(title = "系统错误日志", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SystemErrorLog systemErrorLog)
    {
        List<SystemErrorLog> list = systemErrorLogService.selectSystemErrorLogList(systemErrorLog);
        ExcelUtil<SystemErrorLog> util = new ExcelUtil<SystemErrorLog>(SystemErrorLog.class);
        util.exportExcel(response, list, "系统错误日志数据");
    }

    /**
     * 获取系统错误日志详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:log:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(systemErrorLogService.selectSystemErrorLogById(id));
    }

    /**
     * 新增系统错误日志
     */
    @PreAuthorize("@ss.hasPermi('system:log:add')")
    @Log(title = "系统错误日志", businessType = BusinessType.INSERT)
    @PostMapping
    @ApiOperation("上报系统错误日志")
    public AjaxResult add(@RequestBody SystemErrorLog systemErrorLog)
    {
        return toAjax(systemErrorLogService.insertSystemErrorLog(systemErrorLog));
    }

    /**
     * 修改系统错误日志
     */
    @PreAuthorize("@ss.hasPermi('system:log:edit')")
    @Log(title = "系统错误日志", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SystemErrorLog systemErrorLog)
    {
        return toAjax(systemErrorLogService.updateSystemErrorLog(systemErrorLog));
    }

    /**
     * 删除系统错误日志
     */
    @PreAuthorize("@ss.hasPermi('system:log:remove')")
    @Log(title = "系统错误日志", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(systemErrorLogService.deleteSystemErrorLogByIds(ids));
    }
}