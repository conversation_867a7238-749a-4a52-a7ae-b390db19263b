package com.ruoyi.web.controller.drama;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.drama.domain.AppWebPageConfig;
import com.ruoyi.drama.service.IAppWebPageConfigService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * App页面配置Controller
 * 
 * <AUTHOR>
 * @date 2025-08-05
 */
@RestController
@RequestMapping("/drama/page-config")
public class AppWebPageConfigController extends BaseController
{
    @Autowired
    private IAppWebPageConfigService appWebPageConfigService;

    /**
     * 查询App页面配置列表
     */
    @PreAuthorize("@ss.hasPermi('system:config:list')")
    @GetMapping("/list")
    public TableDataInfo list(AppWebPageConfig appWebPageConfig)
    {
        startPage();
        List<AppWebPageConfig> list = appWebPageConfigService.selectAppWebPageConfigList(appWebPageConfig);
        return getDataTable(list);
    }

    /**
     * 导出App页面配置列表
     */
    @PreAuthorize("@ss.hasPermi('system:config:export')")
    @Log(title = "App页面配置", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, AppWebPageConfig appWebPageConfig)
    {
        List<AppWebPageConfig> list = appWebPageConfigService.selectAppWebPageConfigList(appWebPageConfig);
        ExcelUtil<AppWebPageConfig> util = new ExcelUtil<AppWebPageConfig>(AppWebPageConfig.class);
        util.exportExcel(response, list, "App页面配置数据");
    }

    /**
     * 获取App页面配置详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:config:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(appWebPageConfigService.selectAppWebPageConfigById(id));
    }

    /**
     * 新增App页面配置
     */
    @PreAuthorize("@ss.hasPermi('system:config:add')")
    @Log(title = "App页面配置", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody AppWebPageConfig appWebPageConfig)
    {
        return toAjax(appWebPageConfigService.insertAppWebPageConfig(appWebPageConfig));
    }

    /**
     * 修改App页面配置
     */
    @PreAuthorize("@ss.hasPermi('system:config:edit')")
    @Log(title = "App页面配置", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody AppWebPageConfig appWebPageConfig)
    {
        return toAjax(appWebPageConfigService.updateAppWebPageConfig(appWebPageConfig));
    }

    /**
     * 删除App页面配置
     */
    @PreAuthorize("@ss.hasPermi('system:config:remove')")
    @Log(title = "App页面配置", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(appWebPageConfigService.deleteAppWebPageConfigByIds(ids));
    }
}
