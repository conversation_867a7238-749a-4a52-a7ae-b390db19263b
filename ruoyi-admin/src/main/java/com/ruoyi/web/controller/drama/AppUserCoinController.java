package com.ruoyi.web.controller.drama;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.drama.domain.AppUserCoin;
import com.ruoyi.drama.service.IAppUserCoinService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 用户金币Controller
 *
 * <AUTHOR>
 * @date 2025-07-29
 */
@Api(tags = "用户金币")
@RestController
@RequestMapping("/drama/userCoin")
public class AppUserCoinController extends BaseController
{
    @Autowired
    private IAppUserCoinService appUserCoinService;

    /**
     * 查询用户金币列表
     */
    @ApiOperation("查询用户金币列表")
    @PreAuthorize("@ss.hasPermi('system:coin:list')")
    @GetMapping("/list")
    public TableDataInfo list(AppUserCoin appUserCoin)
    {
        startPage();
        List<AppUserCoin> list = appUserCoinService.selectAppUserCoinList(appUserCoin);
        return getDataTable(list);
    }

    /**
     * 导出用户金币列表
     */
    @ApiOperation("导出用户金币列表")
    @PreAuthorize("@ss.hasPermi('system:coin:export')")
    @Log(title = "用户金币", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, AppUserCoin appUserCoin)
    {
        List<AppUserCoin> list = appUserCoinService.selectAppUserCoinList(appUserCoin);
        ExcelUtil<AppUserCoin> util = new ExcelUtil<AppUserCoin>(AppUserCoin.class);
        util.exportExcel(response, list, "用户金币数据");
    }

    /**
     * 获取用户金币详细信息
     */
    @ApiOperation("获取用户金币详细信息")
    @PreAuthorize("@ss.hasPermi('system:coin:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(appUserCoinService.selectAppUserCoinById(id));
    }

    /**
     * 新增用户金币
     */
    @ApiOperation("新增用户金币")
    @PreAuthorize("@ss.hasPermi('system:coin:add')")
    @Log(title = "用户金币", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody AppUserCoin appUserCoin)
    {
        return toAjax(appUserCoinService.insertAppUserCoin(appUserCoin));
    }

    /**
     * 修改用户金币
     */
    @ApiOperation("修改用户金币")
    @PreAuthorize("@ss.hasPermi('system:coin:edit')")
    @Log(title = "用户金币", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody AppUserCoin appUserCoin)
    {
        return toAjax(appUserCoinService.updateAppUserCoin(appUserCoin));
    }

    /**
     * 删除用户金币
     */
    @ApiOperation("删除用户金币")
    @PreAuthorize("@ss.hasPermi('system:coin:remove')")
    @Log(title = "用户金币", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(appUserCoinService.deleteAppUserCoinByIds(ids));
    }
}
