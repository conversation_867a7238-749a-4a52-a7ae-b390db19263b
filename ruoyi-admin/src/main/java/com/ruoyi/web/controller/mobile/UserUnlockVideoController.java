package com.ruoyi.web.controller.mobile;

import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.drama.dto.DramaRoomMovieUnlockVideoDTO;
import com.ruoyi.drama.service.IDramaRoomUserUnlockVideoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

@Api(tags = "用户解锁视频")
@RestController
@RequestMapping("/mobile/unlock")
public class UserUnlockVideoController {

    @Autowired
    private IDramaRoomUserUnlockVideoService dramaRoomUserUnlockVideoService;

    // 解锁视频
    @ApiOperation("解锁视频")
    @PostMapping("/unlockVideo")
    public R<?> unlockVideo(@Valid @RequestBody DramaRoomMovieUnlockVideoDTO dto) {
        dramaRoomUserUnlockVideoService.unlockVideo(dto);
        return R.ok();
    }
}
