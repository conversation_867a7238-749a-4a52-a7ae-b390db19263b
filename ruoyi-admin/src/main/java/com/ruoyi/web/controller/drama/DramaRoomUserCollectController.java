package com.ruoyi.web.controller.drama;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.core.domain.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.drama.domain.DramaRoomUserCollect;
import com.ruoyi.drama.service.IDramaRoomUserCollectService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 【请填写功能名称】Controller
 *
 * <AUTHOR>
 * @date 2025-07-04
 */
@Api(tags = "用户收藏接口")
@RestController
@RequestMapping("/drama/collect")
public class DramaRoomUserCollectController extends BaseController<DramaRoomUserCollect>
{
    @Autowired
    private IDramaRoomUserCollectService dramaRoomUserFavoriteService;

    /**
     * 获取用户收藏列表
     */
    @ApiOperation("获取用户收藏列表")
    @PreAuthorize("@ss.hasPermi('system:favorite:list')")
    @GetMapping("/list")
    public TableDataInfo<DramaRoomUserCollect> list(DramaRoomUserCollect dramaRoomUserFavorite)
    {
        startPage();
        List<DramaRoomUserCollect> list = dramaRoomUserFavoriteService.selectDramaRoomUserCollectList(dramaRoomUserFavorite);
        return getDataTable(list);
    }

    /**
     * 导出用户收藏列表
     */
    @ApiOperation("导出用户收藏列表")
    @PreAuthorize("@ss.hasPermi('system:favorite:export')")
    @Log(title = "【请填写功能名称】", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DramaRoomUserCollect dramaRoomUserFavorite)
    {
        List<DramaRoomUserCollect> list = dramaRoomUserFavoriteService.selectDramaRoomUserCollectList(dramaRoomUserFavorite);
        ExcelUtil<DramaRoomUserCollect> util = new ExcelUtil<DramaRoomUserCollect>(DramaRoomUserCollect.class);
        util.exportExcel(response, list, "【请填写功能名称】数据");
    }

    /**
     * 查询用户收藏详情
     */
    @ApiOperation("查询用户收藏详情")
    @PreAuthorize("@ss.hasPermi('system:favorite:query')")
    @GetMapping(value = "/{id}")
    public R<DramaRoomUserCollect> getInfo(@PathVariable("id") Long id)
    {
        return R.ok(dramaRoomUserFavoriteService.selectDramaRoomUserCollectById(id));
    }

    /**
     * 新增用户收藏
     */
    @ApiOperation("新增用户收藏")
    @PreAuthorize("@ss.hasPermi('system:favorite:add')")
    @Log(title = "【请填写功能名称】", businessType = BusinessType.INSERT)
    @PostMapping
    public R<Integer> add(@RequestBody DramaRoomUserCollect dramaRoomUserFavorite)
    {
        return R.toR(dramaRoomUserFavoriteService.insertDramaRoomUserCollect(dramaRoomUserFavorite));
    }

    /**
     * 修改用户收藏
     */
    @ApiOperation("修改用户收藏")
    @PreAuthorize("@ss.hasPermi('system:favorite:edit')")
    @Log(title = "【请填写功能名称】", businessType = BusinessType.UPDATE)
    @PutMapping
    public R<Integer> edit(@RequestBody DramaRoomUserCollect dramaRoomUserFavorite)
    {
        return R.toR(dramaRoomUserFavoriteService.updateDramaRoomUserCollect(dramaRoomUserFavorite));
    }

    /**
     * 删除【请填写功能名称】
     */
    @ApiOperation("删除用户收藏")
    @PreAuthorize("@ss.hasPermi('system:favorite:remove')")
    @Log(title = "【请填写功能名称】", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public R<Integer> remove(@PathVariable Long[] ids)
    {
        return R.toR(dramaRoomUserFavoriteService.deleteDramaRoomUserCollectByIds(ids));
    }
}
