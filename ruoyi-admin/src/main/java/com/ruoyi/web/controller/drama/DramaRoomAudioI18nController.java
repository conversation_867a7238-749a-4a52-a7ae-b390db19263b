package com.ruoyi.web.controller.drama;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.drama.dto.DramaRoomGeneralI18nDTO;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.drama.domain.DramaRoomAudioI18n;
import com.ruoyi.drama.service.IDramaRoomAudioI18nService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 音讯多语言Controller
 * 
 * <AUTHOR>
 * @date 2025-08-05
 */
@RestController
@RequestMapping("/drama/audioI18n")
public class DramaRoomAudioI18nController extends BaseController<DramaRoomAudioI18n>
{
    @Autowired
    private IDramaRoomAudioI18nService dramaRoomAudioI18nService;

    /**
     * 查询音讯多语言列表
     */
    @PreAuthorize("@ss.hasPermi('drama:audioI18n:list')")
    @GetMapping("/list")
    public TableDataInfo<DramaRoomAudioI18n> list(DramaRoomAudioI18n dramaRoomAudioI18n)
    {
        startPage();
        List<DramaRoomAudioI18n> list = dramaRoomAudioI18nService.selectDramaRoomAudioI18nList(dramaRoomAudioI18n);
        return getDataTable(list);
    }

    /**
     * 导出音讯多语言列表
     */
    @PreAuthorize("@ss.hasPermi('drama:audioI18n:export')")
    @Log(title = "音讯多语言", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DramaRoomAudioI18n dramaRoomAudioI18n)
    {
        List<DramaRoomAudioI18n> list = dramaRoomAudioI18nService.selectDramaRoomAudioI18nList(dramaRoomAudioI18n);
        ExcelUtil<DramaRoomAudioI18n> util = new ExcelUtil<DramaRoomAudioI18n>(DramaRoomAudioI18n.class);
        util.exportExcel(response, list, "音讯多语言数据");
    }

    /**
     * 获取音讯多语言详细信息
     */
    @PreAuthorize("@ss.hasPermi('drama:audioI18n:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(dramaRoomAudioI18nService.selectDramaRoomAudioI18nById(id));
    }

    /**
     * 新增音讯多语言
     */
    @PreAuthorize("@ss.hasPermi('drama:audioI18n:add')")
    @Log(title = "音讯多语言", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DramaRoomAudioI18n dramaRoomAudioI18n)
    {
        return toAjax(dramaRoomAudioI18nService.insertDramaRoomAudioI18n(dramaRoomAudioI18n));
    }

    /**
     * 修改音讯多语言
     */
    @PreAuthorize("@ss.hasPermi('drama:audioI18n:edit')")
    @Log(title = "音讯多语言", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DramaRoomAudioI18n dramaRoomAudioI18n)
    {
        return toAjax(dramaRoomAudioI18nService.updateDramaRoomAudioI18n(dramaRoomAudioI18n));
    }

    /**
     * 删除音讯多语言
     */
    @PreAuthorize("@ss.hasPermi('drama:audioI18n:remove')")
    @Log(title = "音讯多语言", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(dramaRoomAudioI18nService.deleteDramaRoomAudioI18nByIds(ids));
    }

    /**
     * 单语种翻译
     */
    @PreAuthorize("@ss.hasPermi('drama:audioI18n:translate')")
    @Log(title = "音讯多语言", businessType = BusinessType.INSERT)
    @PostMapping("/translate")
    public AjaxResult translate(@RequestBody DramaRoomGeneralI18nDTO dto){
        return dramaRoomAudioI18nService.translate(dto);
    }

    /**
     * 多语种翻译列表
     */
    @PreAuthorize("@ss.hasPermi('drama:audioI18n:translateList')")
    @GetMapping("/list/translate/{id}")
    public AjaxResult listTranslate(@PathVariable("id") Long id){
        return dramaRoomAudioI18nService.listTranslate(id);
    }
}
