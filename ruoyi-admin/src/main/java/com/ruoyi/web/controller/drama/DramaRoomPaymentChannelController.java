package com.ruoyi.web.controller.drama;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.drama.domain.DramaRoomPaymentChannel;
import com.ruoyi.drama.service.IDramaRoomPaymentChannelService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 支付渠道配置Controller
 * 
 * <AUTHOR>
 * @date 2025-08-08
 */
@RestController
@RequestMapping("/drama/channel")
public class DramaRoomPaymentChannelController extends BaseController
{
    @Autowired
    private IDramaRoomPaymentChannelService dramaRoomPaymentChannelService;

    /**
     * 查询支付渠道配置列表
     */
    @PreAuthorize("@ss.hasPermi('system:channel:list')")
    @GetMapping("/list")
    public TableDataInfo list(DramaRoomPaymentChannel dramaRoomPaymentChannel)
    {
        startPage();
        List<DramaRoomPaymentChannel> list = dramaRoomPaymentChannelService.selectDramaRoomPaymentChannelList(dramaRoomPaymentChannel);
        return getDataTable(list);
    }

    /**
     * 导出支付渠道配置列表
     */
    @PreAuthorize("@ss.hasPermi('system:channel:export')")
    @Log(title = "支付渠道配置", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DramaRoomPaymentChannel dramaRoomPaymentChannel)
    {
        List<DramaRoomPaymentChannel> list = dramaRoomPaymentChannelService.selectDramaRoomPaymentChannelList(dramaRoomPaymentChannel);
        ExcelUtil<DramaRoomPaymentChannel> util = new ExcelUtil<DramaRoomPaymentChannel>(DramaRoomPaymentChannel.class);
        util.exportExcel(response, list, "支付渠道配置数据");
    }

    /**
     * 获取支付渠道配置详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:channel:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(dramaRoomPaymentChannelService.selectDramaRoomPaymentChannelById(id));
    }

    /**
     * 新增支付渠道配置
     */
    @PreAuthorize("@ss.hasPermi('system:channel:add')")
    @Log(title = "支付渠道配置", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DramaRoomPaymentChannel dramaRoomPaymentChannel)
    {
        return toAjax(dramaRoomPaymentChannelService.insertDramaRoomPaymentChannel(dramaRoomPaymentChannel));
    }

    /**
     * 修改支付渠道配置
     */
    @PreAuthorize("@ss.hasPermi('system:channel:edit')")
    @Log(title = "支付渠道配置", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DramaRoomPaymentChannel dramaRoomPaymentChannel)
    {
        return toAjax(dramaRoomPaymentChannelService.updateDramaRoomPaymentChannel(dramaRoomPaymentChannel));
    }

    /**
     * 删除支付渠道配置
     */
    @PreAuthorize("@ss.hasPermi('system:channel:remove')")
    @Log(title = "支付渠道配置", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(dramaRoomPaymentChannelService.deleteDramaRoomPaymentChannelByIds(ids));
    }
}