package com.ruoyi.web.controller.drama;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.page.PageResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.drama.domain.DramaRoomTag;
import com.ruoyi.drama.service.IDramaRoomTagService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 标签Controller
 *
 * <AUTHOR>
 * @date 2025-07-04
 */
@Api(tags = "短剧标签接口")
@RestController
@RequestMapping("/system/tag")
public class DramaRoomTagController extends BaseController<DramaRoomTag>
{
    @Autowired
    private IDramaRoomTagService dramaRoomTagService;

    /**
     * 查询标签列表
     */
    @ApiOperation("获取标签列表")
    @PreAuthorize("@ss.hasPermi('system:tag:list')")
    @GetMapping("/list")
    public TableDataInfo<DramaRoomTag> list(DramaRoomTag dramaRoomTag)
    {
        startPage();
        List<DramaRoomTag> list = dramaRoomTagService.selectDramaRoomTagList(dramaRoomTag);
        return getDataTable(list);
    }

    /**
     * 导出标签列表
     */
    @ApiOperation("导出标签列表")
    @PreAuthorize("@ss.hasPermi('system:tag:export')")
    @Log(title = "标签", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DramaRoomTag dramaRoomTag)
    {
        List<DramaRoomTag> list = dramaRoomTagService.selectDramaRoomTagList(dramaRoomTag);
        ExcelUtil<DramaRoomTag> util = new ExcelUtil<DramaRoomTag>(DramaRoomTag.class);
        util.exportExcel(response, list, "标签数据");
    }

    /**
     * 获取标签详细信息
     */
    @ApiOperation("获取标签详细信息")
    @PreAuthorize("@ss.hasPermi('system:tag:query')")
    @GetMapping(value = "/{tagId}")
    public R<DramaRoomTag> getInfo(@PathVariable("tagId") Long tagId)
    {
        return R.ok(dramaRoomTagService.selectDramaRoomTagByTagId(tagId));
    }

    /**
     * 新增标签
     */
    @ApiOperation("新增标签")
    @PreAuthorize("@ss.hasPermi('system:tag:add')")
    @Log(title = "标签", businessType = BusinessType.INSERT)
    @PostMapping
    public R<Integer> add(@RequestBody DramaRoomTag dramaRoomTag)
    {
        return R.toR(dramaRoomTagService.insertDramaRoomTag(dramaRoomTag));
    }

    /**
     * 修改标签
     */
    @ApiOperation("修改标签")
    @PreAuthorize("@ss.hasPermi('system:tag:edit')")
    @Log(title = "标签", businessType = BusinessType.UPDATE)
    @PutMapping
    public R<Integer> edit(@RequestBody DramaRoomTag dramaRoomTag)
    {
        return R.toR(dramaRoomTagService.updateDramaRoomTag(dramaRoomTag));
    }

    /**
     * 删除标签
     */
    @ApiOperation("删除标签")
    @PreAuthorize("@ss.hasPermi('system:tag:remove')")
    @Log(title = "标签", businessType = BusinessType.DELETE)
	@DeleteMapping("/{tagIds}")
    public R<Integer> remove(@PathVariable Long[] tagIds)
    {
        return R.toR(dramaRoomTagService.deleteDramaRoomTagByTagIds(tagIds));
    }
}
