package com.ruoyi.web.filter;

import java.io.IOException;
import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;
import com.ruoyi.common.core.domain.model.mobile.AppLoginUser;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.mobile.AppTokenService;

/**
 * APP token过滤器 验证token有效性
 * 
 * <AUTHOR>
 */
@Component
public class AppJwtAuthenticationTokenFilter extends OncePerRequestFilter
{
    @Autowired
    private AppTokenService appTokenService;

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain chain)
            throws ServletException, IOException
    {
        // 只对移动端接口进行token验证
        String requestURI = request.getRequestURI();
        if (requestURI.startsWith("/mobile/") && !isExcludedPath(requestURI))
        {
            AppLoginUser appLoginUser = appTokenService.getLoginUser(request);
            if (StringUtils.isNotNull(appLoginUser))
            {
                // 将用户信息存储到请求中，供后续使用
                request.setAttribute("appLoginUser", appLoginUser);
            }
            else
            {
                // Token无效，返回401错误
                response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
                response.setContentType("application/json;charset=UTF-8");
                response.getWriter().write("{\"code\":401,\"msg\":\"token无效或已过期\"}");
                return;
            }
        }
        
        chain.doFilter(request, response);
    }

    /**
     * 判断是否为排除路径（不需要token验证的接口）
     */
    private boolean isExcludedPath(String requestURI)
    {
        // 不需要token验证的接口
        String[] excludedPaths = {
            "/mobile/auth/guest-login",
            "/mobile/auth/refresh-token",
            "/mobile/config/language",
            "/mobile/auth/third-party-login",
            "/mobile/config/app-config"
        };
        
        for (String path : excludedPaths)
        {
            if (requestURI.equals(path))
            {
                return true;
            }
        }
        return false;
    }
}