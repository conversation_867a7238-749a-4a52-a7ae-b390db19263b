# 项目相关配置
ruoyi:
  # 名称
  name: RuoYi
  # 版本
  version: 3.9.0
  # 版权年份
  copyrightYear: 2025
  # 文件路径 示例（ Windows配置D:/ruoyi/uploadPath，Linux配置 /home/<USER>/uploadPath）
  profile: D:/ruoyi/uploadPath
  # 获取ip地址开关
  addressEnabled: false
  # 验证码类型 math 数字计算 char 字符验证
  captchaType: math

# 雪花算法配置
snowflake:
  # 机器ID (0-31) - APP服务
  worker-id: 1
  # 数据中心ID (0-31) 
  datacenter-id: 1
  # 是否启用监控
  monitor-enabled: true

# APP相关配置
app:
  # 第三方登录配置
  third-party:
    # HTTP请求超时时间（毫秒）
    timeout: 10000
    # 重试次数
    retry: 3
    # 是否启用第三方登录
    enabled: true
    # Google登录配置
    google:
      # Google OAuth2客户端ID
      client-id: ${GOOGLE_CLIENT_ID:}
      # Google OAuth2客户端密钥
      client-secret: ${GOOGLE_CLIENT_SECRET:}
      # 是否启用Google登录
      enabled: true
      # 用户信息API地址
      userinfo-url: https://www.googleapis.com/oauth2/v2/userinfo
      # Token验证API地址
      tokeninfo-url: https://oauth2.googleapis.com/tokeninfo
    # Apple登录配置
    apple:
      # Apple服务ID（客户端ID）
      client-id: ${APPLE_CLIENT_ID:}
      # Apple团队ID
      team-id: ${APPLE_TEAM_ID:}
      # Apple密钥ID
      key-id: ${APPLE_KEY_ID:}
      # 是否启用Apple登录
      enabled: true
      # Apple公钥获取地址
      keys-url: https://appleid.apple.com/auth/keys
      # Apple签发者
      issuer: https://appleid.apple.com
  # 用户相关配置
  user:
    # 默认语言
    default-language: en
    # 游客用户默认昵称前缀
    guest-nickname-prefix: "游客"
    # 第三方用户默认昵称后缀
    third-party-nickname-suffix: "用户"
    # 最大设备绑定数量
    max-device-count: 5

# 开发环境配置
server:
  # 服务器的HTTP端口，默认为8080
  port: 8080
  servlet:
    # 应用的访问路径
    context-path: /
  tomcat:
    # tomcat的URI编码
    uri-encoding: UTF-8
    # 连接数满后的排队数，默认为100
    accept-count: 1000
    threads:
      # tomcat最大线程数，默认为200
      max: 800
      # Tomcat启动初始化的线程数，默认值10
      min-spare: 100

# 日志配置
logging:
  level:
    com.ruoyi: debug
    org.springframework: warn

# 用户配置
user:
  password:
    # 密码最大错误次数
    maxRetryCount: 5
    # 密码锁定时间（默认10分钟）
    lockTime: 10

# Spring配置
spring:
  # 资源信息
  messages:
    # 国际化资源文件路径
    basename: i18n/messages
  profiles:
    active: local
  # 文件上传
  servlet:
    multipart:
      # 单个文件大小
      max-file-size: 10MB
      # 设置总上传的文件大小
      max-request-size: 20MB
  # 服务模块
  devtools:
    restart:
      # 热部署开关
      enabled: true

# token配置
token:
  # 令牌自定义标识
  header: Authorization
  # 令牌密钥
  secret: abcdefghijklmnopqrstuvwxyz
  # 令牌有效期（默认30分钟）
  expireTime: 30
  # refreshToken有效期（默认30天）
  refreshExpireTime: 43200

# MyBatis配置
mybatis:
  # 搜索指定包别名
  typeAliasesPackage: com.ruoyi.**.domain
  # 配置mapper的扫描，找到所有的mapper.xml映射文件
  mapperLocations: classpath*:mapper/**/*Mapper.xml
  # 加载全局的配置文件
  configLocation: classpath:mybatis/mybatis-config.xml

# PageHelper分页插件
pagehelper:
  helperDialect: mysql
  supportMethodsArguments: true
  params: count=countSql

# Swagger配置
swagger:
  # 是否开启swagger
  enabled: true
  # 请求前缀
  pathMapping: /dev-api

# 防止XSS攻击
xss:
  # 过滤开关
  enabled: true
  # 排除链接（多个用逗号分隔）
  excludes: /system/notice
  # 匹配链接
  urlPatterns: /system/*,/monitor/*,/tool/*
# 添加R2配置项
cloud:
  r2:
    file-prefix: https://resource.dramaroom.cc/
    access-key: ********************************
    secret-key: 8fb142abff43943760f63a456fb222ea4fa571428f0a295d60e8c4ee285a1d6f
    endpoint: https://ee93a2dd66a3a741438af78442815b3d.r2.cloudflarestorage.com
    bucket-name: dramaroom
    region: auto  # R2特殊区域标识
