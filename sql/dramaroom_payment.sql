-- 1、订单表
CREATE TABLE `drama_room_order`
(
    `id`               bigint                                 NOT NULL COMMENT '分布式ID',
    `order_no`         varchar(64) COLLATE utf8mb4_general_ci NOT NULL COMMENT '订单编号',
    `user_id`          bigint                                 NOT NULL COMMENT '用户ID',
    `order_type`       tinyint                                NOT NULL COMMENT '订单类型(1:充值,2:订阅)',
    `package_id`       bigint                                          DEFAULT NULL COMMENT '套餐ID',
    `amount`           decimal(10, 2)                         NOT NULL COMMENT '订单金额',
    `currency`         varchar(10) COLLATE utf8mb4_general_ci          DEFAULT 'USD' COMMENT '货币类型',
    `platform`         tinyint                                NOT NULL COMMENT '支付平台(7:Google支付,8:Apple pay,9:其他)',
    `channel`          tinyint                                NOT NULL COMMENT '渠道(1:内购,2:第三方。。。)',
    `status`           char(1)                                         DEFAULT '0' COMMENT '状态(0:创建,1:支付中,2:支付成功,3:支付失败,4:已退款，5取消支付)',
    `transaction_id`   varchar(100) COLLATE utf8mb4_general_ci         DEFAULT NULL COMMENT '平台交易ID',
    `receipt_data`     text COLLATE utf8mb4_general_ci COMMENT '支付凭证',
    `callback_time`    datetime                                        DEFAULT NULL COMMENT '回调时间',
    `notify_status`    char(1)                                         DEFAULT '0' COMMENT '通知业务系统状态(0:未通知,1:通知中,2:通知成功,3:通知失败)',
    `notify_times`     int                                             DEFAULT '0' COMMENT '通知次数',
    `next_notify_time` datetime                                        DEFAULT NULL COMMENT '下次通知时间',
    `create_by`        bigint                                          DEFAULT NULL COMMENT '创建人',
    `create_time`      datetime                               NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `update_by`        bigint                                          DEFAULT NULL COMMENT '修改人',
    `update_time`      datetime                               NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `order_no` (`order_no`),
    KEY                `user_id` (`user_id`),
    KEY                `transaction_id` (`transaction_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='订单表';

-- 2、用户账户表（钱包）
CREATE TABLE `drama_room_user_account`
(
    `id`                     bigint   NOT NULL COMMENT '分布式ID',
    `user_id`                bigint   NOT NULL COMMENT '用户ID',
    `gold_coin_balance`      bigint            DEFAULT '0' COMMENT '金币余额',
    `membership_type`        char(1)           DEFAULT '0' COMMENT '会员类型(0:非会员,1:月度会员,2:年度会员)',
    `membership_expire_time` datetime          DEFAULT NULL COMMENT '会员到期时间',
    `create_by`              bigint            DEFAULT NULL COMMENT '创建人',
    `create_time`            datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `update_by`              bigint            DEFAULT NULL COMMENT '修改人',
    `update_time`            datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='用户账户表';

-- 3、充值套餐表
CREATE TABLE `drama_room_recharge_package`
(
    `id`               bigint                                  NOT NULL COMMENT '分布式ID',
    `package_name`     varchar(100) COLLATE utf8mb4_general_ci NOT NULL COMMENT '套餐名称',
    `gold_coin_amount` bigint                                  NOT NULL COMMENT '金币数量',
    `price`            decimal(10, 2)                          NOT NULL COMMENT '价格',
    `platform`         tinyint                                 NOT NULL COMMENT '平台(1:iOS,2:Android)',
    `product_id`       varchar(100) COLLATE utf8mb4_general_ci NOT NULL COMMENT '平台产品ID',
    `status`           char(1)                                          DEFAULT '1' COMMENT '状态(0:下架,1:上架)',
    `sort_order`       int                                              DEFAULT '0' COMMENT '排序',
    `deleted`          char(1)                                          DEFAULT '0' COMMENT '删除标志（0存在 2删除）',
    `create_by`        bigint                                           DEFAULT NULL COMMENT '创建人',
    `create_time`      datetime                                NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `update_by`        bigint                                           DEFAULT NULL COMMENT '修改人',
    `update_time`      datetime                                NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='充值套餐表';

-- 4、会员套餐表
CREATE TABLE `drama_room_membership_package`
(
    `id`              bigint                                  NOT NULL COMMENT '分布式ID',
    `package_name`    varchar(100) COLLATE utf8mb4_general_ci NOT NULL COMMENT '套餐名称',
    `membership_type` char(1)                                 NOT NULL COMMENT '会员类型(1:月度,2:年度)',
    `duration_days`   int                                     NOT NULL COMMENT '有效期(天)',
    `price`           decimal(10, 2)                          NOT NULL COMMENT '价格',
    `platform`        char(1)                                 NOT NULL COMMENT '平台(1:iOS,2:Android)',
    `product_id`      varchar(100) COLLATE utf8mb4_general_ci NOT NULL COMMENT '平台产品ID',
    `status`          char(1)                                          DEFAULT '1' COMMENT '状态(0:下架,1:上架)',
    `sort_order`      int                                              DEFAULT '0' COMMENT '排序',
    `deleted`         char(1)                                          DEFAULT '0' COMMENT '删除标志（0存在 2删除）',
    `create_by`       bigint                                           DEFAULT NULL COMMENT '创建人',
    `create_time`     datetime                                NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `update_by`       bigint                                           DEFAULT NULL COMMENT '修改人',
    `update_time`     datetime                                NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='会员套餐表';

-- 5、金币交易记录表
CREATE TABLE `drama_room_gold_coin_transaction`
(
    `id`               bigint   NOT NULL COMMENT '分布式ID',
    `user_id`          bigint   NOT NULL COMMENT '用户ID',
    `order_id`         bigint                                  DEFAULT NULL COMMENT '关联订单ID',
    `amount`           bigint   NOT NULL COMMENT '金币数量(正数为增加,负数为减少)',
    `balance`          bigint   NOT NULL COMMENT '交易后余额',
    `transaction_type` char(1)  NOT NULL COMMENT '交易类型(1:充值,2:消费,3:退款,4:赠送,5:系统调整)',
    `description`      varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '交易描述',
    `create_by`        bigint                                  DEFAULT NULL COMMENT '创建人',
    `create_time`      datetime NOT NULL                       DEFAULT CURRENT_TIMESTAMP,
    `update_by`        bigint                                  DEFAULT NULL COMMENT '修改人',
    `update_time`      datetime NOT NULL                       DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY                `user_id` (`user_id`),
    KEY                `order_id` (`order_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='金币交易记录表';

-- 6、会员订阅记录表
CREATE TABLE `drama_room_membership_subscription`
(
    `id`                      bigint   NOT NULL COMMENT '分布式ID',
    `user_id`                 bigint   NOT NULL COMMENT '用户ID',
    `order_id`                bigint                                  DEFAULT NULL COMMENT '关联订单ID',
    `package_id`              bigint   NOT NULL COMMENT '套餐ID',
    `membership_type`         tinyint  NOT NULL COMMENT '会员类型(1:月度,2:年度)',
    `start_time`              datetime NOT NULL COMMENT '开始时间',
    `end_time`                datetime NOT NULL COMMENT '结束时间',
    `status`                  char(1)                                 DEFAULT '1' COMMENT '状态(0:已取消,1:生效中,2:已过期)',
    `is_auto_renew`           tinyint                                 DEFAULT '0' COMMENT '是否自动续订(0:否,1:是)',
    `platform_transaction_id` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '平台订阅ID',
    `create_by`               bigint                                  DEFAULT NULL COMMENT '创建人',
    `create_time`             datetime NOT NULL                       DEFAULT CURRENT_TIMESTAMP,
    `update_by`               bigint                                  DEFAULT NULL COMMENT '修改人',
    `update_time`             datetime NOT NULL                       DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY                       `user_id` (`user_id`),
    KEY                       `order_id` (`order_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='会员订阅记录表';

-- 7、支付配置表
CREATE TABLE `drama_room_config`
(
    `id`           bigint                                 NOT NULL COMMENT '分布式ID',
    `config_key`   varchar(50) COLLATE utf8mb4_general_ci NOT NULL COMMENT '配置键',
    `config_value` text COLLATE utf8mb4_general_ci        NOT NULL COMMENT '配置值',
    `description`  varchar(255) COLLATE utf8mb4_general_ci         DEFAULT NULL COMMENT '配置描述',
    `create_by`    bigint                                          DEFAULT NULL COMMENT '创建人',
    `create_time`  datetime                               NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `update_by`    bigint                                          DEFAULT NULL COMMENT '修改人',
    `update_time`  datetime                               NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `config_key` (`config_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='支付配置表';