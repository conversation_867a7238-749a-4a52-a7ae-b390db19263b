SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- 1. APP用户主表 (app_user)
-- 用户身份和基础资料信息
    -- 新增用户当前选择语言
-- ----------------------------
DROP TABLE IF EXISTS `app_user`;
CREATE TABLE `app_user` (
  `user_id`           bigint(20)    NOT NULL                   COMMENT '用户ID (分布式雪花算法ID)',
  `nickname`          varchar(30)   DEFAULT ''                 COMMENT '用户昵称',
  `avatar`            varchar(255)  DEFAULT ''                 COMMENT '头像地址',
  `email`             varchar(255)  DEFAULT NULL               COMMENT '用户邮箱',
  `phone_number`      varchar(20)   DEFAULT NULL               COMMENT '手机号码',
  `gender`            char(1)       DEFAULT '2'                COMMENT '用户性别（0男 1女 2未知）',
  `account_type`      varchar(20)   DEFAULT 'GUEST'            COMMENT '账户类型 (GUEST-游客, REGISTERED-正式)',
  `status`            char(1)       DEFAULT '0'                COMMENT '帐号状态（0正常 1停用）',
  `deleted`          char(1)       DEFAULT '0'                COMMENT '删除标志（0存在 1删除）',
  `language_key`      varchar(10)   DEFAULT 'en'               COMMENT 'APP语言Key（如en, zh）',
  `create_time`       datetime(3)   NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
  `update_time`       datetime(3)   NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '更新时间',
  `remark`            varchar(500)  DEFAULT NULL               COMMENT '备注',
  `create_by`         bigint        DEFAULT NULL               COMMENT '创建者',
  `update_by`         bigint        DEFAULT NULL               COMMENT '更新者',
  PRIMARY KEY (`user_id`),
  UNIQUE KEY `uni_email` (`email`),
  UNIQUE KEY `uni_phone_number` (`phone_number`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='APP端客户信息表';

-- ----------------------------
-- 2. 用户认证表 (app_user_auth)
-- 用于存储用户的多种登录认证方式，实现认证与用户身份的解耦。
-- ----------------------------
DROP TABLE IF EXISTS `app_user_auth`;
CREATE TABLE `app_user_auth` (
  `id`                bigint(20)    NOT NULL AUTO_INCREMENT    COMMENT '主键ID',
  `user_id`           bigint(20)    NOT NULL                   COMMENT '用户ID (关联app_user.user_id)',
  `auth_type`         varchar(20)   NOT NULL                   COMMENT '认证类型 (EMAIL, PHONE, GOOGLE, APPLE, FACEBOOK)',
  `identifier`        varchar(255)  NOT NULL                   COMMENT '唯一标识 (如邮箱地址、手机号、第三方应用的openId)',
  `credential`        varchar(255)  DEFAULT NULL               COMMENT '凭证 (如密码的哈希值，或第三方授权的access_token)',
  `create_time`       datetime(3)   NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
  `update_time`       datetime(3)   NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uni_auth_type_identifier` (`auth_type`, `identifier`),
  KEY `idx_user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户认证表';

-- ----------------------------
-- 3. 用户设备绑定表 (app_user_device)
-- 用于实现游客无感登录和设备管理。
-- ----------------------------
DROP TABLE IF EXISTS `app_user_device`;
CREATE TABLE `app_user_device` (
  `id`                bigint(20)    NOT NULL AUTO_INCREMENT    COMMENT '主键ID',
  `user_id`           bigint(20)    NOT NULL                   COMMENT '用户ID (关联app_user.user_id)',
  `device_id`         varchar(128)  NOT NULL                   COMMENT '客户端生成的唯一设备ID',
  `device_type`       varchar(20)   DEFAULT 'UNKNOWN'          COMMENT '设备类型 (ANDROID, IOS, WEB)',
  `status`            char(1)       DEFAULT '0'                COMMENT '绑定状态（0绑定 1解绑）',
  `last_login_ip`     varchar(128)  DEFAULT ''                 COMMENT '当前设备最后登录IP',
  `last_login_date`   datetime(3)                              COMMENT '当前设备最后登录时间',
  `create_by`         bigint        DEFAULT NULL               COMMENT '创建者',
  `update_by`         bigint        DEFAULT NULL               COMMENT '更新者',
  `create_time`       datetime(3)   NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '绑定时间',
  `update_time`       datetime(3)   NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '更新时间',
  `remark`            varchar(500)  DEFAULT NULL               COMMENT '备注',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_device_id` (`device_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户设备绑定表';

SET FOREIGN_KEY_CHECKS = 1;


-- =====================================================
-- 优化后的数据库索引脚本
-- 1. app_user 表核心索引
-- =====================================================
-- 状态和删除标志复合索引 - 用于列表查询和统计
CREATE INDEX idx_app_user_status_deleted ON app_user (status, deleted);

-- 创建时间索引 - 用于排序
CREATE INDEX idx_app_user_create_time ON app_user (create_time);

-- 昵称模糊查询索引 - 实际项目中有昵称搜索需求
CREATE INDEX idx_app_user_nickname ON app_user (nickname);


-- 2. app_user_device 表核心索引
-- =====================================================
-- 设备ID和状态复合索引 - 最高频的查询组合
CREATE INDEX idx_app_user_device_device_status ON app_user_device (device_id, status);

-- 用户ID和状态复合索引 - 查询用户设备列表
CREATE INDEX idx_app_user_device_user_status ON app_user_device (user_id, status);

-- 最后登录时间索引 - 用于查找最旧设备（设备清理逻辑）
CREATE INDEX idx_app_user_device_last_login ON app_user_device (last_login_date);

-- =====================================================
-- 性能监控索引（可选）
-- =====================================================

-- 活跃用户统计索引 - 如果有定期统计需求可保留
-- CREATE INDEX IF NOT EXISTS idx_app_user_active_stats ON app_user (account_type, status, deleted);

-- =====================================================
-- 索引维护建议
-- =====================================================

-- 1. 定期分析表统计信息
-- ANALYZE TABLE app_user, app_user_auth, app_user_device;

-- 2. 监控慢查询日志
-- SET GLOBAL slow_query_log = 'ON';
-- SET GLOBAL long_query_time = 1;

-- 3. 检查索引使用情况
-- SELECT
--     table_name,
--     index_name,
--     seq_in_index,
--     column_name,
--     cardinality
-- FROM information_schema.statistics
-- WHERE table_schema = DATABASE()
--   AND table_name IN ('app_user', 'app_user_auth', 'app_user_device')
-- ORDER BY table_name, index_name, seq_in_index;