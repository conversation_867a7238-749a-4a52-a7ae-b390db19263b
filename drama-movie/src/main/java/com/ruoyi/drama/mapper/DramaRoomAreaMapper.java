package com.ruoyi.drama.mapper;

import com.ruoyi.drama.domain.DramaRoomArea;

import java.util.List;

/**
 * 短剧地区Mapper接口
 *
 * <AUTHOR>
 * @date 2025-07-28
 */
public interface DramaRoomAreaMapper
{
    /**
     * 查询短剧地区
     *
     * @param id 短剧地区主键
     * @return 短剧地区
     */
    public DramaRoomArea selectDramaRoomAreaById(Long id);

    /**
     * 查询短剧地区列表
     *
     * @param dramaRoomArea 短剧地区
     * @return 短剧地区集合
     */
    public List<DramaRoomArea> selectDramaRoomAreaList(DramaRoomArea dramaRoomArea);

    /**
     * 新增短剧地区
     *
     * @param dramaRoomArea 短剧地区
     * @return 结果
     */
    public int insertDramaRoomArea(DramaRoomArea dramaRoomArea);

    /**
     * 修改短剧地区
     *
     * @param dramaRoomArea 短剧地区
     * @return 结果
     */
    public int updateDramaRoomArea(DramaRoomArea dramaRoomArea);

    /**
     * 删除短剧地区
     *
     * @param id 短剧地区主键
     * @return 结果
     */
    public int deleteDramaRoomAreaById(Long id);

    /**
     * 批量删除短剧地区
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteDramaRoomAreaByIds(Long[] ids);
}
