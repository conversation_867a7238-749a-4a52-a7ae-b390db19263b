package com.ruoyi.drama.mapper;

import com.ruoyi.drama.domain.DramaRoomOrder;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 订单Mapper接口
 *
 * <AUTHOR>
 * @date 2025-08-08
 */
public interface DramaRoomOrderMapper {
    /**
     * 查询订单
     *
     * @param id 订单主键
     * @return 订单
     */
    public DramaRoomOrder selectDramaRoomOrderById(Long id);

    /**
     * 查询订单列表
     *
     * @param dramaRoomOrder 订单
     * @return 订单集合
     */
    public List<DramaRoomOrder> selectDramaRoomOrderList(DramaRoomOrder dramaRoomOrder);

    /**
     * 新增订单
     *
     * @param dramaRoomOrder 订单
     * @return 结果
     */
    public int insertDramaRoomOrder(DramaRoomOrder dramaRoomOrder);

    /**
     * 修改订单
     *
     * @param dramaRoomOrder 订单
     * @return 结果
     */
    public int updateDramaRoomOrder(DramaRoomOrder dramaRoomOrder);

    /**
     * 删除订单
     *
     * @param id 订单主键
     * @return 结果
     */
    public int deleteDramaRoomOrderById(Long id);

    /**
     * 批量删除订单
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteDramaRoomOrderByIds(Long[] ids);

    /**
     * 查询用户待支付订单
     *
     * @param userId
     * @param productId
     * @param status
     * @return
     */
    DramaRoomOrder selectPendingPayOrder(@Param("userId") Long userId, @Param("productId") Long productId, @Param("status") int status);
}