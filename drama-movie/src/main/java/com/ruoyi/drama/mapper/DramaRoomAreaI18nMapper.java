package com.ruoyi.drama.mapper;

import java.util.List;
import com.ruoyi.drama.domain.DramaRoomAreaI18n;
import org.apache.ibatis.annotations.Param;

/**
 * 短剧地区多语言Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-08-05
 */
public interface DramaRoomAreaI18nMapper 
{
    /**
     * 查询短剧地区多语言
     * 
     * @param id 短剧地区多语言主键
     * @return 短剧地区多语言
     */
    public DramaRoomAreaI18n selectDramaRoomAreaI18nById(Long id);

    /**
     * 查询短剧地区多语言列表
     * 
     * @param dramaRoomAreaI18n 短剧地区多语言
     * @return 短剧地区多语言集合
     */
    public List<DramaRoomAreaI18n> selectDramaRoomAreaI18nList(DramaRoomAreaI18n dramaRoomAreaI18n);

    /**
     * 新增短剧地区多语言
     * 
     * @param dramaRoomAreaI18n 短剧地区多语言
     * @return 结果
     */
    public int insertDramaRoomAreaI18n(DramaRoomAreaI18n dramaRoomAreaI18n);

    /**
     * 修改短剧地区多语言
     * 
     * @param dramaRoomAreaI18n 短剧地区多语言
     * @return 结果
     */
    public int updateDramaRoomAreaI18n(DramaRoomAreaI18n dramaRoomAreaI18n);

    /**
     * 删除短剧地区多语言
     * 
     * @param id 短剧地区多语言主键
     * @return 结果
     */
    public int deleteDramaRoomAreaI18nById(Long id);

    /**
     * 批量删除短剧地区多语言
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteDramaRoomAreaI18nByIds(Long[] ids);

    /**
     * 批量插入多语言
     * @param i18ns
     */
    void insertDramaRoomAreaI18nList(@Param("i18ns") List<DramaRoomAreaI18n> i18ns);

    /**
     * 根据地区id查询多语言
     * @param areaIds
     * @return
     */
    List<DramaRoomAreaI18n> selectDramaRoomAreaI18nListByAreaIds(@Param("areaIds") List<Long> areaIds);

    /**
     * 根据地区id删除多语言
     * @param areaId
     */
    void deleteDramaRoomAreaI18nByAreaId(Long areaId);

    /**
     * 根据地区id批量删除多语言
     * @param ids
     */
    void deleteDramaRoomAreaI18nByAreaIds(@Param("ids") Long[] ids);
}
