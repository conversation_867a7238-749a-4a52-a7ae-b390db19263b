package com.ruoyi.drama.mapper;

import java.util.List;
import com.ruoyi.drama.domain.DramaRoomMovieCategory;
import org.apache.ibatis.annotations.Param;

/**
 * 【请填写功能名称】Mapper接口
 *
 * <AUTHOR>
 * @date 2025-07-04
 */
public interface DramaRoomMovieCategoryMapper
{
    /**
     * 查询【请填写功能名称】
     *
     * @param id 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    public DramaRoomMovieCategory selectDramaRoomMovieCategoryById(Long id);

    /**
     * 查询【请填写功能名称】列表
     *
     * @param dramaRoomMovieCategory 【请填写功能名称】
     * @return 【请填写功能名称】集合
     */
    public List<DramaRoomMovieCategory> selectDramaRoomMovieCategoryList(DramaRoomMovieCategory dramaRoomMovieCategory);

    /**
     * 新增【请填写功能名称】
     *
     * @param dramaRoomMovieCategory 【请填写功能名称】
     * @return 结果
     */
    public int insertDramaRoomMovieCategory(DramaRoomMovieCategory dramaRoomMovieCategory);

    /**
     * 修改【请填写功能名称】
     *
     * @param dramaRoomMovieCategory 【请填写功能名称】
     * @return 结果
     */
    public int updateDramaRoomMovieCategory(DramaRoomMovieCategory dramaRoomMovieCategory);

    /**
     * 删除【请填写功能名称】
     *
     * @param id 【请填写功能名称】主键
     * @return 结果
     */
    public int deleteDramaRoomMovieCategoryById(Long id);

    /**
     * 批量删除【请填写功能名称】
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteDramaRoomMovieCategoryByIds(Long[] ids);

    /**
     * 根据分类id查询
     * @param list
     * @return
     */
    List<DramaRoomMovieCategory> selectByCategoryIdList(List<Long> list);

    /**
     * 根据分类id查询分类关联
     * @param categoryId
     * @return
     */
    List<DramaRoomMovieCategory> selectDramaRoomMovieCategoryByCategoryId(Long categoryId);

    /**
     * 根据分类id删除分类关联
     * @param categoryId
     */
    void deleteDramaRoomMovieCategoryByCategoryId(Long categoryId);

    /**
     * 批量插入分类关联
     * @param list
     */
    void insertDramaRoomMovieCategoryList(@Param("list") List<DramaRoomMovieCategory> list);
}
