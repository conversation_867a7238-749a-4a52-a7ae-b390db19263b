package com.ruoyi.drama.mapper;

import com.ruoyi.drama.domain.DramaRoomVideo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 剧集Mapper接口
 *
 * <AUTHOR>
 * @date 2025-07-04
 */
public interface DramaRoomVideoMapper
{
    /**
     * 查询剧集
     *
     * @param id 剧集主键
     * @return 剧集
     */
    public DramaRoomVideo selectDramaRoomVideoById(Long id);

    /**
     * 查询剧集列表
     *
     * @param dramaRoomVideo 剧集
     * @return 剧集集合
     */
    public List<DramaRoomVideo> selectDramaRoomVideoList(DramaRoomVideo dramaRoomVideo);

    /**
     * 新增剧集
     *
     * @param dramaRoomVideo 剧集
     * @return 结果
     */
    public int insertDramaRoomVideo(DramaRoomVideo dramaRoomVideo);

    /**
     * 修改剧集
     *
     * @param dramaRoomVideo 剧集
     * @return 结果
     */
    public int updateDramaRoomVideo(DramaRoomVideo dramaRoomVideo);

    /**
     * 删除剧集
     *
     * @param id 剧集主键
     * @return 结果
     */
    public int deleteDramaRoomVideoById(Long id);

    /**
     * 批量删除剧集
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteDramaRoomVideoByIds(Long[] ids);

    /**
     * 根据电影id查询剧集
     *
     * @param movieId
     * @return
     */
    List<DramaRoomVideo> selectDramaRoomVideoByMovieId(Long movieId);
}
