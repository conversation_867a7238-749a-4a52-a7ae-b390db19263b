package com.ruoyi.drama.mapper;

import java.util.List;

import com.ruoyi.drama.domain.DramaRoomMovie;
import com.ruoyi.drama.domain.DramaRoomMovieI18n;
import org.apache.ibatis.annotations.Param;

/**
 * 短剧翻译Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-08-05
 */
public interface DramaRoomMovieI18nMapper 
{
    /**
     * 查询短剧翻译
     * 
     * @param id 短剧翻译主键
     * @return 短剧翻译
     */
    public DramaRoomMovieI18n selectDramaRoomMovieI18nById(Long id);

    /**
     * 查询短剧翻译列表
     * 
     * @param dramaRoomMovieI18n 短剧翻译
     * @return 短剧翻译集合
     */
    public List<DramaRoomMovieI18n> selectDramaRoomMovieI18nList(DramaRoomMovieI18n dramaRoomMovieI18n);

    /**
     * 新增短剧翻译
     * 
     * @param dramaRoomMovieI18n 短剧翻译
     * @return 结果
     */
    public int insertDramaRoomMovieI18n(DramaRoomMovieI18n dramaRoomMovieI18n);

    /**
     * 修改短剧翻译
     * 
     * @param dramaRoomMovieI18n 短剧翻译
     * @return 结果
     */
    public int updateDramaRoomMovieI18n(DramaRoomMovieI18n dramaRoomMovieI18n);

    /**
     * 删除短剧翻译
     * 
     * @param id 短剧翻译主键
     * @return 结果
     */
    public int deleteDramaRoomMovieI18nById(Long id);

    /**
     * 批量删除短剧翻译
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteDramaRoomMovieI18nByIds(Long[] ids);

    List<DramaRoomMovieI18n> selectDramaRoomMovieI18nByMovieIdList(List<Long> list);

    List<DramaRoomMovieI18n> selectDramaRoomMovieI18nByMovieIdListAndLanguageCode(@Param("list") List<Long> list, @Param("languageCode") String languageCode);

    int deleteMovieI18n(@Param("movieId") Long movieId, @Param("languageCode") String languageCode);

    List<DramaRoomMovieI18n> selectByMovieName(String name);
}
