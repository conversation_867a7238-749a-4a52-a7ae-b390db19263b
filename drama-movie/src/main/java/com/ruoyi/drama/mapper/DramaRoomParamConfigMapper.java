package com.ruoyi.drama.mapper;

import java.util.List;
import com.ruoyi.drama.domain.DramaRoomParamConfig;

/**
 * 【请填写功能名称】Mapper接口
 *
 * <AUTHOR>
 * @date 2025-07-04
 */
public interface DramaRoomParamConfigMapper
{
    /**
     * 查询【请填写功能名称】
     *
     * @param id 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    public DramaRoomParamConfig selectDramaRoomParamConfigById(Long id);

    /**
     * 查询【请填写功能名称】列表
     *
     * @param dramaRoomParamConfig 【请填写功能名称】
     * @return 【请填写功能名称】集合
     */
    public List<DramaRoomParamConfig> selectDramaRoomParamConfigList(DramaRoomParamConfig dramaRoomParamConfig);

    /**
     * 新增【请填写功能名称】
     *
     * @param dramaRoomParamConfig 【请填写功能名称】
     * @return 结果
     */
    public int insertDramaRoomParamConfig(DramaRoomParamConfig dramaRoomParamConfig);

    /**
     * 修改【请填写功能名称】
     *
     * @param dramaRoomParamConfig 【请填写功能名称】
     * @return 结果
     */
    public int updateDramaRoomParamConfig(DramaRoomParamConfig dramaRoomParamConfig);

    /**
     * 删除【请填写功能名称】
     *
     * @param id 【请填写功能名称】主键
     * @return 结果
     */
    public int deleteDramaRoomParamConfigById(Long id);

    /**
     * 批量删除【请填写功能名称】
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteDramaRoomParamConfigByIds(Long[] ids);

    /**
     * 根据key查询参数
     * @param key
     * @return
     */
    String selectDramaRoomParamConfigByKey(String key);
}
