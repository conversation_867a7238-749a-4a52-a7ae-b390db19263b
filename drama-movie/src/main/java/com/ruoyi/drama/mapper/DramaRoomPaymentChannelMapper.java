package com.ruoyi.drama.mapper;

import com.ruoyi.drama.domain.DramaRoomPaymentChannel;

import java.util.List;

/**
 * 支付渠道配置Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-08-08
 */
public interface DramaRoomPaymentChannelMapper
{
    /**
     * 查询支付渠道配置
     * 
     * @param id 支付渠道配置主键
     * @return 支付渠道配置
     */
    public DramaRoomPaymentChannel selectDramaRoomPaymentChannelById(Long id);

    /**
     * 查询支付渠道配置列表
     * 
     * @param dramaRoomPaymentChannel 支付渠道配置
     * @return 支付渠道配置集合
     */
    public List<DramaRoomPaymentChannel> selectDramaRoomPaymentChannelList(DramaRoomPaymentChannel dramaRoomPaymentChannel);

    /**
     * 新增支付渠道配置
     * 
     * @param dramaRoomPaymentChannel 支付渠道配置
     * @return 结果
     */
    public int insertDramaRoomPaymentChannel(DramaRoomPaymentChannel dramaRoomPaymentChannel);

    /**
     * 修改支付渠道配置
     * 
     * @param dramaRoomPaymentChannel 支付渠道配置
     * @return 结果
     */
    public int updateDramaRoomPaymentChannel(DramaRoomPaymentChannel dramaRoomPaymentChannel);

    /**
     * 删除支付渠道配置
     * 
     * @param id 支付渠道配置主键
     * @return 结果
     */
    public int deleteDramaRoomPaymentChannelById(Long id);

    /**
     * 批量删除支付渠道配置
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteDramaRoomPaymentChannelByIds(Long[] ids);

    /**
     * 根据渠道编码查询支付渠道配置
     *
     * @param channelCode 渠道编码
     * @return 支付渠道配置
     */
    DramaRoomPaymentChannel selectDramaRoomPaymentChannel(String channelCode);
}