package com.ruoyi.drama.mapper;

import java.util.List;
import com.ruoyi.drama.domain.DramaRoomVideoI18n;
import org.apache.ibatis.annotations.Param;
import org.springframework.web.bind.annotation.PathVariable;

/**
 * 剧集多语言Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-08-05
 */
public interface DramaRoomVideoI18nMapper 
{
    /**
     * 查询剧集多语言
     * 
     * @param id 剧集多语言主键
     * @return 剧集多语言
     */
    public DramaRoomVideoI18n selectDramaRoomVideoI18nById(Long id);

    /**
     * 查询剧集多语言列表
     * 
     * @param dramaRoomVideoI18n 剧集多语言
     * @return 剧集多语言集合
     */
    public List<DramaRoomVideoI18n> selectDramaRoomVideoI18nList(DramaRoomVideoI18n dramaRoomVideoI18n);

    /**
     * 新增剧集多语言
     * 
     * @param dramaRoomVideoI18n 剧集多语言
     * @return 结果
     */
    public int insertDramaRoomVideoI18n(DramaRoomVideoI18n dramaRoomVideoI18n);

    /**
     * 修改剧集多语言
     * 
     * @param dramaRoomVideoI18n 剧集多语言
     * @return 结果
     */
    public int updateDramaRoomVideoI18n(DramaRoomVideoI18n dramaRoomVideoI18n);

    /**
     * 删除剧集多语言
     * 
     * @param id 剧集多语言主键
     * @return 结果
     */
    public int deleteDramaRoomVideoI18nById(Long id);

    /**
     * 批量删除剧集多语言
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteDramaRoomVideoI18nByIds(Long[] ids);


    List<DramaRoomVideoI18n> selectDramaRoomVideoI18nByVideoIds(@Param("videoIdList") List<Long> videoIdList, @Param("languageCodeList") List<String> languageCodeList);
}
