package com.ruoyi.drama.mapper;

import java.util.List;

import com.ruoyi.drama.domain.DramaRoomVideo;
import com.ruoyi.drama.domain.DramaRoomVideoChannelCoin;
import org.apache.ibatis.annotations.Param;

/**
 * 视频渠道扣金币配置Mapper接口
 *
 * <AUTHOR>
 * @date 2025-08-05
 */
public interface DramaRoomVideoChannelCoinMapper
{
    /**
     * 查询视频渠道扣金币配置
     *
     * @param id 视频渠道扣金币配置主键
     * @return 视频渠道扣金币配置
     */
    public DramaRoomVideoChannelCoin selectDramaRoomVideoChannelCoinById(Long id);

    /**
     * 查询视频渠道扣金币配置列表
     *
     * @param dramaRoomVideoChannelCoin 视频渠道扣金币配置
     * @return 视频渠道扣金币配置集合
     */
    public List<DramaRoomVideoChannelCoin> selectDramaRoomVideoChannelCoinList(DramaRoomVideoChannelCoin dramaRoomVideoChannelCoin);

    /**
     * 新增视频渠道扣金币配置
     *
     * @param dramaRoomVideoChannelCoin 视频渠道扣金币配置
     * @return 结果
     */
    public int insertDramaRoomVideoChannelCoin(DramaRoomVideoChannelCoin dramaRoomVideoChannelCoin);

    /**
     * 修改视频渠道扣金币配置
     *
     * @param dramaRoomVideoChannelCoin 视频渠道扣金币配置
     * @return 结果
     */
    public int updateDramaRoomVideoChannelCoin(DramaRoomVideoChannelCoin dramaRoomVideoChannelCoin);

    /**
     * 删除视频渠道扣金币配置
     *
     * @param id 视频渠道扣金币配置主键
     * @return 结果
     */
    public int deleteDramaRoomVideoChannelCoinById(Long id);

    /**
     * 批量删除视频渠道扣金币配置
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteDramaRoomVideoChannelCoinByIds(Long[] ids);

    int insertDramaRoomVideoChannelCoinBatch(List<DramaRoomVideoChannelCoin> list);

    int updateDramaRoomVideoChannelCoinBatch(List<DramaRoomVideoChannelCoin> list);

    DramaRoomVideoChannelCoin selectDramaRoomVideoChannelCoinByCoinRuleIdAndMovieIdAndVideoId(@Param("coinRuleId") Long coinRuleId, @Param("movieId") Long movieId, @Param("videoId") Long videoId);

    List<DramaRoomVideoChannelCoin> selectDramaRoomVideoChannelCoinByCoinRuleIdAndMovieId(@Param("coinRuleId") Long coinRuleId, @Param("movieId") Long id);

    List<DramaRoomVideoChannelCoin> selectDramaRoomVideoChannelCoinByMovieId(Long movieId);
}
