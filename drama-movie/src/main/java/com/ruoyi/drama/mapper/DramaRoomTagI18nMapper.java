package com.ruoyi.drama.mapper;

import java.util.List;
import com.ruoyi.drama.domain.DramaRoomTagI18n;
import org.apache.ibatis.annotations.Param;

/**
 * 标签多语言Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-08-05
 */
public interface DramaRoomTagI18nMapper 
{
    /**
     * 查询标签多语言
     * 
     * @param id 标签多语言主键
     * @return 标签多语言
     */
    public DramaRoomTagI18n selectDramaRoomTagI18nById(Long id);

    /**
     * 查询标签多语言列表
     * 
     * @param dramaRoomTagI18n 标签多语言
     * @return 标签多语言集合
     */
    public List<DramaRoomTagI18n> selectDramaRoomTagI18nList(DramaRoomTagI18n dramaRoomTagI18n);

    /**
     * 新增标签多语言
     * 
     * @param dramaRoomTagI18n 标签多语言
     * @return 结果
     */
    public int insertDramaRoomTagI18n(DramaRoomTagI18n dramaRoomTagI18n);

    /**
     * 修改标签多语言
     * 
     * @param dramaRoomTagI18n 标签多语言
     * @return 结果
     */
    public int updateDramaRoomTagI18n(DramaRoomTagI18n dramaRoomTagI18n);

    /**
     * 删除标签多语言
     * 
     * @param id 标签多语言主键
     * @return 结果
     */
    public int deleteDramaRoomTagI18nById(Long id);

    /**
     * 批量删除标签多语言
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteDramaRoomTagI18nByIds(Long[] ids);

    /**
     * 批量插入标签多语言
     *
     * @param i18ns 需要插入的数据集合
     * @return 插入结果
     */
    void insertDramaRoomTagI18nList(List<DramaRoomTagI18n> i18ns);

    /**
     * 根据标签id批量查询标签多语言
     *
     * @param tagIds 标签id集合
     * @return 批量查询结果
     */
    List<DramaRoomTagI18n> selectDramaRoomTagI18nByTagIds(@Param("tagIds") List<Long> tagIds);

    /**
     * 根据标签id批量删除标签多语言
     *
     * @param tagIds 批量删除的标签id集合
     * @return 删除结果
     */
    void deleteDramaRoomTagI18nByTagIds(@Param("tagIds") Long[] tagIds);

    /**
     * 根据标签id批量查询标签多语言
     *
     * @param tagId 标签id
     * @return 批量查询结果
     */
    void deleteDramaRoomTagI18nByTagId(@Param("tagId") Long tagId);

    List<DramaRoomTagI18n> selectDramaRoomTagI18nByTagIdsAndLanguageCode(@Param("tagIds") List<Long> tagIds, @Param("languageCode") String languageCode);
}
