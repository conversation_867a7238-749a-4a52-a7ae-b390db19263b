package com.ruoyi.drama.mapper;

import java.util.List;
import com.ruoyi.drama.domain.DramaRoomAudioI18n;
import org.apache.ibatis.annotations.Param;

/**
 * 音讯多语言Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-08-05
 */
public interface DramaRoomAudioI18nMapper 
{
    /**
     * 查询音讯多语言
     * 
     * @param id 音讯多语言主键
     * @return 音讯多语言
     */
    public DramaRoomAudioI18n selectDramaRoomAudioI18nById(Long id);

    /**
     * 查询音讯多语言列表
     * 
     * @param dramaRoomAudioI18n 音讯多语言
     * @return 音讯多语言集合
     */
    public List<DramaRoomAudioI18n> selectDramaRoomAudioI18nList(DramaRoomAudioI18n dramaRoomAudioI18n);

    /**
     * 新增音讯多语言
     * 
     * @param dramaRoomAudioI18n 音讯多语言
     * @return 结果
     */
    public int insertDramaRoomAudioI18n(DramaRoomAudioI18n dramaRoomAudioI18n);

    /**
     * 修改音讯多语言
     * 
     * @param dramaRoomAudioI18n 音讯多语言
     * @return 结果
     */
    public int updateDramaRoomAudioI18n(DramaRoomAudioI18n dramaRoomAudioI18n);

    /**
     * 删除音讯多语言
     * 
     * @param id 音讯多语言主键
     * @return 结果
     */
    public int deleteDramaRoomAudioI18nById(Long id);

    /**
     * 批量删除音讯多语言
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteDramaRoomAudioI18nByIds(Long[] ids);

    /**
     * 批量新增音讯多语言
     *
     * @param i18ns 音讯多语言
     * @return 结果
     */
    void insertDramaRoomAudioI18nList(@Param("i18ns") List<DramaRoomAudioI18n> i18ns);

    /**
     * 根据audioIds批量查询多语言
     *
     * @param audioIds 音频id
     * @return 音讯多语言
     */
    List<DramaRoomAudioI18n> selectDramaRoomAudioI18nByAudioIds(@Param("audioIds") List<Long> audioIds);

    /**
     * 根据audioId批量删除多语言
     *
     * @param ids 音频id
     * @return 音讯多语言
     */
    void deleteDramaRoomAudioI18nByAudioIds(@Param("ids") Long[] ids);

    /**
     * 根据audioId删除多语言
     *
     * @param id 音频id
     * @return 音讯多语言
     */
    void deleteDramaRoomAudioI18nByAudioId(@Param("id") Long id);
}
