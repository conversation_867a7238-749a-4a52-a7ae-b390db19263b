package com.ruoyi.drama.mapper;

import com.ruoyi.drama.domain.DramaRoomTransactionRecord;

import java.util.List;

/**
 * 交易记录（包含支付和退款）Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-08-08
 */
public interface DramaRoomTransactionRecordMapper 
{
    /**
     * 查询交易记录（包含支付和退款）
     * 
     * @param id 交易记录（包含支付和退款）主键
     * @return 交易记录（包含支付和退款）
     */
    public DramaRoomTransactionRecord selectDramaRoomTransactionRecordById(Long id);

    /**
     * 查询交易记录（包含支付和退款）列表
     * 
     * @param dramaRoomTransactionRecord 交易记录（包含支付和退款）
     * @return 交易记录（包含支付和退款）集合
     */
    public List<DramaRoomTransactionRecord> selectDramaRoomTransactionRecordList(DramaRoomTransactionRecord dramaRoomTransactionRecord);

    /**
     * 新增交易记录（包含支付和退款）
     * 
     * @param dramaRoomTransactionRecord 交易记录（包含支付和退款）
     * @return 结果
     */
    public int insertDramaRoomTransactionRecord(DramaRoomTransactionRecord dramaRoomTransactionRecord);

    /**
     * 修改交易记录（包含支付和退款）
     * 
     * @param dramaRoomTransactionRecord 交易记录（包含支付和退款）
     * @return 结果
     */
    public int updateDramaRoomTransactionRecord(DramaRoomTransactionRecord dramaRoomTransactionRecord);

    /**
     * 删除交易记录（包含支付和退款）
     * 
     * @param id 交易记录（包含支付和退款）主键
     * @return 结果
     */
    public int deleteDramaRoomTransactionRecordById(Long id);

    /**
     * 批量删除交易记录（包含支付和退款）
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteDramaRoomTransactionRecordByIds(Long[] ids);
}