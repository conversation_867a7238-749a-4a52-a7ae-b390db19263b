package com.ruoyi.drama.mapper;

import com.ruoyi.drama.domain.SystemErrorLog;

import java.util.List;

/**
 * 系统错误日志Mapper接口
 *
 * <AUTHOR>
 * @date 2025-08-06
 */
public interface SystemErrorLogMapper
{
    /**
     * 查询系统错误日志
     *
     * @param id 系统错误日志主键
     * @return 系统错误日志
     */
    public SystemErrorLog selectSystemErrorLogById(Long id);

    /**
     * 查询系统错误日志列表
     *
     * @param systemErrorLog 系统错误日志
     * @return 系统错误日志集合
     */
    public List<SystemErrorLog> selectSystemErrorLogList(SystemErrorLog systemErrorLog);

    /**
     * 新增系统错误日志
     *
     * @param systemErrorLog 系统错误日志
     * @return 结果
     */
    public int insertSystemErrorLog(SystemErrorLog systemErrorLog);

    /**
     * 修改系统错误日志
     *
     * @param systemErrorLog 系统错误日志
     * @return 结果
     */
    public int updateSystemErrorLog(SystemErrorLog systemErrorLog);

    /**
     * 删除系统错误日志
     *
     * @param id 系统错误日志主键
     * @return 结果
     */
    public int deleteSystemErrorLogById(Long id);

    /**
     * 批量删除系统错误日志
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSystemErrorLogByIds(Long[] ids);
}