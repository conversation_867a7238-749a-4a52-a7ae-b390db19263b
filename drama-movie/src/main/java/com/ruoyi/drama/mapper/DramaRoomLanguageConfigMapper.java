package com.ruoyi.drama.mapper;

import java.util.List;
import com.ruoyi.drama.domain.DramaRoomLanguageConfig;

/**
 * 大包多语言配置Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-08-05
 */
public interface DramaRoomLanguageConfigMapper 
{
    /**
     * 查询大包多语言配置
     * 
     * @param id 大包多语言配置主键
     * @return 大包多语言配置
     */
    public DramaRoomLanguageConfig selectDramaRoomLanguageConfigById(Long id);

    /**
     * 查询大包多语言配置列表
     * 
     * @param dramaRoomLanguageConfig 大包多语言配置
     * @return 大包多语言配置集合
     */
    public List<DramaRoomLanguageConfig> selectDramaRoomLanguageConfigList(DramaRoomLanguageConfig dramaRoomLanguageConfig);

    /**
     * 新增大包多语言配置
     * 
     * @param dramaRoomLanguageConfig 大包多语言配置
     * @return 结果
     */
    public int insertDramaRoomLanguageConfig(DramaRoomLanguageConfig dramaRoomLanguageConfig);

    /**
     * 修改大包多语言配置
     * 
     * @param dramaRoomLanguageConfig 大包多语言配置
     * @return 结果
     */
    public int updateDramaRoomLanguageConfig(DramaRoomLanguageConfig dramaRoomLanguageConfig);

    /**
     * 删除大包多语言配置
     * 
     * @param id 大包多语言配置主键
     * @return 结果
     */
    public int deleteDramaRoomLanguageConfigById(Long id);

    /**
     * 批量删除大包多语言配置
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteDramaRoomLanguageConfigByIds(Long[] ids);
}
