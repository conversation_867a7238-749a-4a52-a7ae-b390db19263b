package com.ruoyi.drama.mapper;

import java.util.List;
import com.ruoyi.drama.domain.AppUserSubscription;

/**
 * 用户订阅Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-09-01
 */
public interface AppUserSubscriptionMapper 
{
    /**
     * 查询用户订阅
     * 
     * @param id 用户订阅主键
     * @return 用户订阅
     */
    public AppUserSubscription selectAppUserSubscriptionById(Long id);

    /**
     * 查询用户订阅列表
     * 
     * @param appUserSubscription 用户订阅
     * @return 用户订阅集合
     */
    public List<AppUserSubscription> selectAppUserSubscriptionList(AppUserSubscription appUserSubscription);

    /**
     * 新增用户订阅
     * 
     * @param appUserSubscription 用户订阅
     * @return 结果
     */
    public int insertAppUserSubscription(AppUserSubscription appUserSubscription);

    /**
     * 修改用户订阅
     * 
     * @param appUserSubscription 用户订阅
     * @return 结果
     */
    public int updateAppUserSubscription(AppUserSubscription appUserSubscription);

    /**
     * 删除用户订阅
     * 
     * @param id 用户订阅主键
     * @return 结果
     */
    public int deleteAppUserSubscriptionById(Long id);

    /**
     * 批量删除用户订阅
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteAppUserSubscriptionByIds(Long[] ids);
}