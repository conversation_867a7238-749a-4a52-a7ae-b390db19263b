package com.ruoyi.drama.mapper;

import java.util.List;
import com.ruoyi.drama.domain.DramaRoomMovieTag;
import org.apache.ibatis.annotations.Param;

/**
 * 短剧标签关联Mapper接口
 *
 * <AUTHOR>
 * @date 2025-07-04
 */
public interface DramaRoomMovieTagMapper
{
    /**
     * 查询短剧标签关联
     *
     * @param id 短剧标签关联主键
     * @return 短剧标签关联
     */
    public DramaRoomMovieTag selectDramaRoomMovieTagById(Long id);

    /**
     * 查询短剧标签关联列表
     *
     * @param DramaRoomMovieTag 短剧标签关联
     * @return 短剧标签关联集合
     */
    public List<DramaRoomMovieTag> selectDramaRoomMovieTagList(DramaRoomMovieTag DramaRoomMovieTag);

    /**
     * 新增短剧标签关联
     *
     * @param DramaRoomMovieTag 短剧标签关联
     * @return 结果
     */
    public int insertDramaRoomMovieTag(DramaRoomMovieTag DramaRoomMovieTag);

    /**
     * 修改短剧标签关联
     *
     * @param DramaRoomMovieTag 短剧标签关联
     * @return 结果
     */
    public int updateDramaRoomMovieTag(DramaRoomMovieTag DramaRoomMovieTag);

    /**
     * 删除短剧标签关联
     *
     * @param id 短剧标签关联主键
     * @return 结果
     */
    public int deleteDramaRoomMovieTagById(Long id);

    /**
     * 批量删除短剧标签关联
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteDramaRoomMovieTagByIds(Long[] ids);

    /**
     * 根据标签id查询所有绑定的短剧
     *
     * @param tagId 标签id
     * @return 结果
     */
    List<DramaRoomMovieTag> selectDramaRoomMovieTagByTagId(Long tagId);

    /**
     * 根据标签id删除所有绑定的短剧
     *
     * @param tagId 标签id
     * @return 删除结果
     */
    void deleteDramaRoomMovieTagByTagId(Long tagId);

    /**
     * 批量插入短剧标签关联
     *
     * @param list 短剧标签关联列表
     */
    void insertDramaRoomMovieTagList(@Param("list") List<DramaRoomMovieTag> list);
}
