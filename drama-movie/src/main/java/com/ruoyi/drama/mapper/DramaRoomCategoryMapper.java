package com.ruoyi.drama.mapper;

import com.ruoyi.drama.domain.DramaRoomCategory;
import com.ruoyi.drama.domain.DramaRoomCategoryI18n;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * 分类Mapper接口
 *
 * <AUTHOR>
 * @date 2025-07-03
 */
public interface DramaRoomCategoryMapper
{
    /**
     * 查询分类
     *
     * @param id 分类主键
     * @return 分类
     */
    public DramaRoomCategory selectDramaRoomCategoryById(Long id);

    /**
     * 查询分类列表
     *
     * @param dramaRoomCategory 分类
     * @return 分类集合
     */
    public List<DramaRoomCategory> selectDramaRoomCategoryList(DramaRoomCategory dramaRoomCategory);

    /**
     * 新增分类
     *
     * @param dramaRoomCategory 分类
     * @return 结果
     */
    public int insertDramaRoomCategory(DramaRoomCategory dramaRoomCategory);

    /**
     * 修改分类
     *
     * @param dramaRoomCategory 分类
     * @return 结果
     */
    public int updateDramaRoomCategory(DramaRoomCategory dramaRoomCategory);

    /**
     * 删除分类
     *
     * @param id 分类主键
     * @return 结果
     */
    public int deleteDramaRoomCategoryById(Long id);

    /**
     * 批量删除分类
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteDramaRoomCategoryByIds(Long[] ids);

    /**
     * 根据分类id查询分类
     *
     * @param ids 分类id
     * @return 分类列表
     */
    List<DramaRoomCategory> selectDramaRoomCategoryByIds(Long[] ids);
}
