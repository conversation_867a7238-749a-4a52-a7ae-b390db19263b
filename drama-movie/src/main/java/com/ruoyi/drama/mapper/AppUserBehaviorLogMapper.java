package com.ruoyi.drama.mapper;

import com.ruoyi.drama.domain.AppUserBehaviorLog;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 用户行为记录Mapper接口
 *
 * <AUTHOR>
 * @date 2025-07-29
 */
public interface AppUserBehaviorLogMapper {

    /**
     * 查询用户行为记录
     *
     * @param id 主键
     * @return 记录
     */
    AppUserBehaviorLog selectAppUserBehaviorLogById(Long id);

    /**
     * 查询用户行为记录列表
     *
     * @param appUserBehaviorLog 查询条件
     * @return 列表
     */
    List<AppUserBehaviorLog> selectAppUserBehaviorLogList(AppUserBehaviorLog appUserBehaviorLog);

    /**
     * 新增用户行为记录
     *
     * @param appUserBehaviorLog 记录
     * @return 结果
     */
    int insertAppUserBehaviorLog(AppUserBehaviorLog appUserBehaviorLog);

    /**
     * 修改用户行为记录
     *
     * @param appUserBehaviorLog 记录
     * @return 结果
     */
    int updateAppUserBehaviorLog(AppUserBehaviorLog appUserBehaviorLog);

    /**
     * 删除用户行为记录
     *
     * @param id 主键
     * @return 结果
     */
    int deleteAppUserBehaviorLogById(Long id);

    /**
     * 批量删除用户行为记录
     *
     * @param ids 主键集合
     * @return 结果
     */
    int deleteAppUserBehaviorLogByIds(Long[] ids);

    int getSumMinutes(@Param("userId") Long userId, @Param("type") String type);
}


