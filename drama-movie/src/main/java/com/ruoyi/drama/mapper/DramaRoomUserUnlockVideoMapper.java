package com.ruoyi.drama.mapper;

import java.util.List;

import com.ruoyi.common.drama.vo.UnlockEpisodeRecordsVO;
import com.ruoyi.drama.domain.DramaRoomUserUnlockVideo;
import org.apache.ibatis.annotations.Param;

/**
 * 用户解锁视频记录Mapper接口
 *
 * <AUTHOR>
 * @date 2025-08-07
 */
public interface DramaRoomUserUnlockVideoMapper
{
    /**
     * 查询用户解锁视频记录
     *
     * @param id 用户解锁视频记录主键
     * @return 用户解锁视频记录
     */
    public DramaRoomUserUnlockVideo selectDramaRoomUserUnlockVideoById(Long id);

    /**
     * 查询用户解锁视频记录列表
     *
     * @param dramaRoomUserUnlockVideo 用户解锁视频记录
     * @return 用户解锁视频记录集合
     */
    public List<DramaRoomUserUnlockVideo> selectDramaRoomUserUnlockVideoList(DramaRoomUserUnlockVideo dramaRoomUserUnlockVideo);

    /**
     * 根据用户ID查询解锁视频列表
     *
     * @param userId 用户ID
     * @return 用户解锁视频记录集合
     */
    public List<DramaRoomUserUnlockVideo> selectDramaRoomUserUnlockVideoByUserId(Long userId);

    /**
     * 根据用户ID和影片ID查询解锁视频列表
     *
     * @param userId 用户ID
     * @param movieId 影片ID
     * @return 用户解锁视频记录集合
     */
    public List<DramaRoomUserUnlockVideo> selectDramaRoomUserUnlockVideoByUserIdAndMovieId(@Param("userId") Long userId, @Param("movieId") Long movieId);

    /**
     * 检查用户是否已解锁指定视频
     *
     * @param userId 用户ID
     * @param videoId 视频ID
     * @return 解锁记录，如果未解锁返回null
     */
    public DramaRoomUserUnlockVideo selectDramaRoomUserUnlockVideoByUserIdAndVideoId(@Param("userId") Long userId, @Param("videoId") Long videoId);

    /**
     * 统计用户解锁视频总数
     *
     * @param userId 用户ID
     * @return 解锁视频总数
     */
    public int countUnlockVideosByUserId(Long userId);

    /**
     * 统计影片解锁视频数量
     *
     * @param userId 用户ID
     * @param movieId 影片ID
     * @return 该影片解锁视频数量
     */
    public int countUnlockVideosByUserIdAndMovieId(@Param("userId") Long userId, @Param("movieId") Long movieId);

    /**
     * 新增用户解锁视频记录
     *
     * @param dramaRoomUserUnlockVideo 用户解锁视频记录
     * @return 结果
     */
    public int insertDramaRoomUserUnlockVideo(DramaRoomUserUnlockVideo dramaRoomUserUnlockVideo);

    /**
     * 修改用户解锁视频记录
     *
     * @param dramaRoomUserUnlockVideo 用户解锁视频记录
     * @return 结果
     */
    public int updateDramaRoomUserUnlockVideo(DramaRoomUserUnlockVideo dramaRoomUserUnlockVideo);

    /**
     * 删除用户解锁视频记录
     *
     * @param id 用户解锁视频记录主键
     * @return 结果
     */
    public int deleteDramaRoomUserUnlockVideoById(Long id);

    /**
     * 批量删除用户解锁视频记录
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteDramaRoomUserUnlockVideoByIds(Long[] ids);

    /**
     * 根据用户ID删除所有解锁记录
     *
     * @param userId 用户ID
     * @return 结果
     */
    public int deleteDramaRoomUserUnlockVideoByUserId(Long userId);

    /**
     * 根据影片ID删除所有解锁记录
     *
     * @param movieId 影片ID
     * @return 结果
     */
    public int deleteDramaRoomUserUnlockVideoByMovieId(Long movieId);

    /**
     * 根据视频ID删除所有解锁记录
     *
     * @param videoId 视频ID
     * @return 结果
     */
    public int deleteDramaRoomUserUnlockVideoByVideoId(Long videoId);

    DramaRoomUserUnlockVideo selectByUserIdAndMovieIdAndVideoId(@Param("userId") Long userId, @Param("movieId") Long movieId, @Param("videoId") Long videoId);

    List<UnlockEpisodeRecordsVO> unlockEpisodeRecords(@Param("userId") Long userId);
}