package com.ruoyi.drama.mapper;

import com.ruoyi.common.drama.vo.DramaRoomPayTemplateAmtVO;
import com.ruoyi.common.drama.vo.DramaRoomPayTemplateSubscriptionVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface IapMapper {

    List<DramaRoomPayTemplateAmtVO> selectAmtByPayTemplateId(@Param("payTemplateId") Long payTemplateId);

    List<DramaRoomPayTemplateSubscriptionVO> selectSubscriptionByPayTemplateId(@Param("payTemplateId") Long payTemplateId);
}
