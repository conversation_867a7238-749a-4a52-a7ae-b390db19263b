package com.ruoyi.drama.mapper;

import java.util.List;
import com.ruoyi.drama.domain.DramaRoomPayTemplateSubscription;

/**
 * 订阅模板：定义不同周期和等级下的金币订阅计划Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-09-01
 */
public interface DramaRoomPayTemplateSubscriptionMapper 
{
    /**
     * 查询订阅模板：定义不同周期和等级下的金币订阅计划
     * 
     * @param id 订阅模板：定义不同周期和等级下的金币订阅计划主键
     * @return 订阅模板：定义不同周期和等级下的金币订阅计划
     */
    public DramaRoomPayTemplateSubscription selectDramaRoomPayTemplateSubscriptionById(String id);

    /**
     * 查询订阅模板：定义不同周期和等级下的金币订阅计划列表
     * 
     * @param dramaRoomPayTemplateSubscription 订阅模板：定义不同周期和等级下的金币订阅计划
     * @return 订阅模板：定义不同周期和等级下的金币订阅计划集合
     */
    public List<DramaRoomPayTemplateSubscription> selectDramaRoomPayTemplateSubscriptionList(DramaRoomPayTemplateSubscription dramaRoomPayTemplateSubscription);

    /**
     * 新增订阅模板：定义不同周期和等级下的金币订阅计划
     * 
     * @param dramaRoomPayTemplateSubscription 订阅模板：定义不同周期和等级下的金币订阅计划
     * @return 结果
     */
    public int insertDramaRoomPayTemplateSubscription(DramaRoomPayTemplateSubscription dramaRoomPayTemplateSubscription);

    /**
     * 修改订阅模板：定义不同周期和等级下的金币订阅计划
     * 
     * @param dramaRoomPayTemplateSubscription 订阅模板：定义不同周期和等级下的金币订阅计划
     * @return 结果
     */
    public int updateDramaRoomPayTemplateSubscription(DramaRoomPayTemplateSubscription dramaRoomPayTemplateSubscription);

    /**
     * 删除订阅模板：定义不同周期和等级下的金币订阅计划
     * 
     * @param id 订阅模板：定义不同周期和等级下的金币订阅计划主键
     * @return 结果
     */
    public int deleteDramaRoomPayTemplateSubscriptionById(String id);

    /**
     * 批量删除订阅模板：定义不同周期和等级下的金币订阅计划
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteDramaRoomPayTemplateSubscriptionByIds(String[] ids);
}