package com.ruoyi.drama.mapper;

import com.ruoyi.drama.domain.AppWebPageConfig;

import java.util.List;


/**
 * App页面配置Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-08-05
 */
public interface AppWebPageConfigMapper 
{
    /**
     * 查询App页面配置
     * 
     * @param id App页面配置主键
     * @return App页面配置
     */
    public AppWebPageConfig selectAppWebPageConfigById(Long id);

    /**
     * 查询App页面配置列表
     * 
     * @param appWebPageConfig App页面配置
     * @return App页面配置集合
     */
    public List<AppWebPageConfig> selectAppWebPageConfigList(AppWebPageConfig appWebPageConfig);

    /**
     * 新增App页面配置
     * 
     * @param appWebPageConfig App页面配置
     * @return 结果
     */
    public int insertAppWebPageConfig(AppWebPageConfig appWebPageConfig);

    /**
     * 修改App页面配置
     * 
     * @param appWebPageConfig App页面配置
     * @return 结果
     */
    public int updateAppWebPageConfig(AppWebPageConfig appWebPageConfig);

    /**
     * 删除App页面配置
     * 
     * @param id App页面配置主键
     * @return 结果
     */
    public int deleteAppWebPageConfigById(Long id);

    /**
     * 批量删除App页面配置
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteAppWebPageConfigByIds(Long[] ids);
}
