package com.ruoyi.drama.mapper;

import com.ruoyi.common.core.domain.entity.AppUserDevice;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 用户设备关联数据访问层
 * 
 * <AUTHOR>
 */
@Mapper
public interface AppUserDeviceMapper
{
    /**
     * 根据ID查询用户设备关联信息
     * 
     * @param id 主键ID
     * @return 用户设备关联信息，如果未找到返回null
     */
    AppUserDevice selectAppUserDeviceById(Long id);

    /**
     * 根据用户ID查询设备关联列表
     * 
     * @param userId 用户ID
     * @return 该用户的所有设备关联列表
     */
    List<AppUserDevice> selectAppUserDevicesByUserId(Long userId);

    /**
     * 根据设备ID查询用户关联信息
     * 
     * @param deviceId 设备ID
     * @return 与该设备关联的用户设备信息，如果未找到返回null
     */
    AppUserDevice selectAppUserDeviceByDeviceId(String deviceId);

    /**
     * 根据条件查询用户设备关联列表
     * 支持多条件组合查询，用于分页列表展示
     * 
     * @param appUserDevice 查询条件对象
     * @return 符合条件的用户设备关联列表
     */
    List<AppUserDevice> selectAppUserDeviceList(AppUserDevice appUserDevice);

    /**
     * 新增用户设备关联
     * 
     * @param appUserDevice 用户设备关联信息
     * @return 影响行数，成功返回1
     */
    int insertAppUserDevice(AppUserDevice appUserDevice);

    /**
     * 修改用户设备关联信息
     * 
     * @param appUserDevice 用户设备关联信息（必须包含id）
     * @return 影响行数，成功返回1
     */
    int updateAppUserDevice(AppUserDevice appUserDevice);

    /**
     * 删除用户设备关联（物理删除）
     * 
     * @param id 主键ID
     * @return 影响行数，成功返回1
     */
    int deleteAppUserDeviceById(Long id);

    /**
     * 批量删除用户设备关联（物理删除）
     * 
     * @param ids 主键ID数组
     * @return 影响行数
     */
    int deleteAppUserDeviceByIds(Long[] ids);

    /**
     * 根据用户ID删除所有设备关联
     * 
     * @param userId 用户ID
     * @return 影响行数
     */
    int deleteAppUserDeviceByUserId(Long userId);

    /**
     * 校验设备是否已被其他用户绑定
     * 
     * @param deviceId 设备ID
     * @param userId 排除的用户ID（修改时使用，新增时传null）
     * @return 绑定该设备的其他用户数量
     */
    int checkDeviceBinding(@Param("deviceId") String deviceId, @Param("userId") Long userId);

    /**
     * 统计用户设备关联总数
     * 
     * @param appUserDevice 统计条件（可为null表示统计全部）
     * @return 关联总数
     */
    int countAppUserDevices(AppUserDevice appUserDevice);

    /**
     * 根据用户ID和设备ID查询关联信息
     * 
     * @param userId 用户ID
     * @param deviceId 设备ID
     * @return 用户设备关联信息，如果未找到返回null
     */
    AppUserDevice selectAppUserDeviceByUserIdAndDeviceId(@Param("userId") Long userId, @Param("deviceId") String deviceId);

    int countAppUserDevicesByUserId(@Param("userId") Long userId);

    List<AppUserDevice> selectAppUserDeviceListByUserId(Long userId);

    List<AppUserDevice> selectAppUserDeviceListInUserIds(@Param("userIds") List<Long> userIds);
}