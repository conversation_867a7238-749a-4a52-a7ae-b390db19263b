package com.ruoyi.drama.mapper;

import java.util.List;
import com.ruoyi.drama.domain.DramaRoomTag;
import org.apache.ibatis.annotations.Param;

/**
 * 标签Mapper接口
 *
 * <AUTHOR>
 * @date 2025-07-04
 */
public interface DramaRoomTagMapper
{
    /**
     * 查询标签
     *
     * @param tagId 标签主键
     * @return 标签
     */
    public DramaRoomTag selectDramaRoomTagByTagId(Long tagId);

    /**
     * 查询标签列表
     *
     * @param dramaRoomTag 标签
     * @return 标签集合
     */
    public List<DramaRoomTag> selectDramaRoomTagList(DramaRoomTag dramaRoomTag);

    /**
     * 新增标签
     *
     * @param dramaRoomTag 标签
     * @return 结果
     */
    public int insertDramaRoomTag(DramaRoomTag dramaRoomTag);

    /**
     * 修改标签
     *
     * @param dramaRoomTag 标签
     * @return 结果
     */
    public int updateDramaRoomTag(DramaRoomTag dramaRoomTag);

    /**
     * 删除标签
     *
     * @param tagId 标签主键
     * @return 结果
     */
    public int deleteDramaRoomTagByTagId(Long tagId);

    /**
     * 批量删除标签
     *
     * @param tagIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteDramaRoomTagByTagIds(Long[] tagIds);

    List<DramaRoomTag> selectTagListByMovieId(@Param("movieId") Long movieId);
}
