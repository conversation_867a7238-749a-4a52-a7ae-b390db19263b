package com.ruoyi.drama.mapper;

import com.ruoyi.drama.domain.DramaRoomMovieCoinRule;
import com.ruoyi.drama.domain.DramaRoomVideoChannelCoin;

import java.util.List;

/**
 * 短剧扣费规则Mapper接口
 *
 * <AUTHOR>
 * @date 2025-08-05
 */
public interface DramaRoomMovieCoinRuleMapper
{
    /**
     * 查询短剧扣费规则
     *
     * @param id 短剧扣费规则主键
     * @return 短剧扣费规则
     */
    public DramaRoomMovieCoinRule selectDramaRoomMovieCoinRuleById(Long id);

    /**
     * 查询短剧扣费规则列表
     *
     * @param dramaRoomMovieCoinRule 短剧扣费规则
     * @return 短剧扣费规则集合
     */
    public List<DramaRoomMovieCoinRule> selectDramaRoomMovieCoinRuleList(DramaRoomMovieCoinRule dramaRoomMovieCoinRule);

    /**
     * 新增短剧扣费规则
     *
     * @param dramaRoomMovieCoinRule 短剧扣费规则
     * @return 结果
     */
    public int insertDramaRoomMovieCoinRule(DramaRoomMovieCoinRule dramaRoomMovieCoinRule);

    /**
     * 修改短剧扣费规则
     *
     * @param dramaRoomMovieCoinRule 短剧扣费规则
     * @return 结果
     */
    public int updateDramaRoomMovieCoinRule(DramaRoomMovieCoinRule dramaRoomMovieCoinRule);

    /**
     * 删除短剧扣费规则
     *
     * @param id 短剧扣费规则主键
     * @return 结果
     */
    public int deleteDramaRoomMovieCoinRuleById(Long id);

    /**
     * 批量删除短剧扣费规则
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteDramaRoomMovieCoinRuleByIds(Long[] ids);

    /**
     * 根据电影id查询
     * @param movieId
     * @return
     */
    public DramaRoomMovieCoinRule selectDramaRoomMovieCoinRuleByMoiveId(Long movieId);
}
