package com.ruoyi.drama.mapper;

import com.ruoyi.drama.domain.DramaRoomUserLike;

import java.util.List;

/**
 * 用户点赞记录Mapper接口
 *
 * <AUTHOR>
 * @date 2025-07-08
 */
public interface DramaRoomUserLikeMapper
{
    /**
     * 查询用户点赞记录
     *
     * @param id 用户点赞记录主键
     * @return 用户点赞记录
     */
    public DramaRoomUserLike selectDramaRoomUserLikeById(Long id);

    /**
     * 查询用户点赞记录列表
     *
     * @param dramaRoomUserLike 用户点赞记录
     * @return 用户点赞记录集合
     */
    public List<DramaRoomUserLike> selectDramaRoomUserLikeList(DramaRoomUserLike dramaRoomUserLike);

    /**
     * 新增用户点赞记录
     *
     * @param dramaRoomUserLike 用户点赞记录
     * @return 结果
     */
    public int insertDramaRoomUserLike(DramaRoomUserLike dramaRoomUserLike);

    /**
     * 修改用户点赞记录
     *
     * @param dramaRoomUserLike 用户点赞记录
     * @return 结果
     */
    public int updateDramaRoomUserLike(DramaRoomUserLike dramaRoomUserLike);

    /**
     * 删除用户点赞记录
     *
     * @param id 用户点赞记录主键
     * @return 结果
     */
    public int deleteDramaRoomUserLikeById(Long id);

    /**
     * 批量删除用户点赞记录
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteDramaRoomUserLikeByIds(Long[] ids);
}
