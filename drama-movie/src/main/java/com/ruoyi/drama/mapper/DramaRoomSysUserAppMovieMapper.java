package com.ruoyi.drama.mapper;

import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.drama.domain.DramaRoomSysUserAppMovie;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 运营用户关联app，短剧Mapper接口
 *
 * <AUTHOR>
 * @date 2025-08-11
 */
public interface DramaRoomSysUserAppMovieMapper
{
    /**
     * 查询运营用户关联app，短剧
     *
     * @param id 运营用户关联app，短剧主键
     * @return 运营用户关联app，短剧
     */
    public DramaRoomSysUserAppMovie selectDramaRoomSysUserAppMovieById(Long id);

    /**
     * 查询运营用户关联app，短剧列表
     *
     * @param dramaRoomSysUserAppMovie 运营用户关联app，短剧
     * @return 运营用户关联app，短剧集合
     */
    public List<DramaRoomSysUserAppMovie> selectDramaRoomSysUserAppMovieList(DramaRoomSysUserAppMovie dramaRoomSysUserAppMovie);

    /**
     * 新增运营用户关联app，短剧
     *
     * @param dramaRoomSysUserAppMovie 运营用户关联app，短剧
     * @return 结果
     */
    public int insertDramaRoomSysUserAppMovie(DramaRoomSysUserAppMovie dramaRoomSysUserAppMovie);

    /**
     * 修改运营用户关联app，短剧
     *
     * @param dramaRoomSysUserAppMovie 运营用户关联app，短剧
     * @return 结果
     */
    public int updateDramaRoomSysUserAppMovie(DramaRoomSysUserAppMovie dramaRoomSysUserAppMovie);

    /**
     * 删除运营用户关联app，短剧
     *
     * @param id 运营用户关联app，短剧主键
     * @return 结果
     */
    public int deleteDramaRoomSysUserAppMovieById(Long id);

    /**
     * 批量删除运营用户关联app，短剧
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteDramaRoomSysUserAppMovieByIds(Long[] ids);

    List<SysUser> getChildUserList(String postId);

    void updateSysUserIdByPIdAndCId(@Param("pId") Long pId,@Param("cId") Long cId);
}