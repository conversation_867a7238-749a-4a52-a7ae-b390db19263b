package com.ruoyi.drama.mapper;

import java.util.List;
import com.ruoyi.drama.domain.DramaRoomPayTemplateAmt;

/**
 * 金额模板Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-08-11
 */
public interface DramaRoomPayTemplateAmtMapper 
{
    /**
     * 查询金额模板
     * 
     * @param id 金额模板主键
     * @return 金额模板
     */
    public DramaRoomPayTemplateAmt selectDramaRoomPayTemplateAmtById(Long id);

    /**
     * 查询金额模板列表
     * 
     * @param dramaRoomPayTemplateAmt 金额模板
     * @return 金额模板集合
     */
    public List<DramaRoomPayTemplateAmt> selectDramaRoomPayTemplateAmtList(DramaRoomPayTemplateAmt dramaRoomPayTemplateAmt);

    /**
     * 新增金额模板
     * 
     * @param dramaRoomPayTemplateAmt 金额模板
     * @return 结果
     */
    public int insertDramaRoomPayTemplateAmt(DramaRoomPayTemplateAmt dramaRoomPayTemplateAmt);

    /**
     * 修改金额模板
     * 
     * @param dramaRoomPayTemplateAmt 金额模板
     * @return 结果
     */
    public int updateDramaRoomPayTemplateAmt(DramaRoomPayTemplateAmt dramaRoomPayTemplateAmt);

    /**
     * 删除金额模板
     * 
     * @param id 金额模板主键
     * @return 结果
     */
    public int deleteDramaRoomPayTemplateAmtById(Long id);

    /**
     * 批量删除金额模板
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteDramaRoomPayTemplateAmtByIds(Long[] ids);
}