package com.ruoyi.drama.mapper;

import com.ruoyi.common.drama.dto.DramaRoomMovieQueryDTO;
import com.ruoyi.drama.domain.DramaRoomMovie;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 短剧Mapper接口
 *
 * <AUTHOR>
 * @date 2025-07-04
 */
public interface DramaRoomMovieMapper {
    /**
     * 查询短剧
     *
     * @param id 短剧主键
     * @return 短剧
     */
    public DramaRoomMovie selectDramaRoomMovieById(Long id);

    /**
     * 查询短剧列表
     *
     * @param dramaRoomMovie 短剧
     * @return 短剧集合
     */
    public List<DramaRoomMovie> selectDramaRoomMovieList(DramaRoomMovie dramaRoomMovie);

    /**
     * 新增短剧
     *
     * @param dramaRoomMovie 短剧
     * @return 结果
     */
    public int insertDramaRoomMovie(DramaRoomMovie dramaRoomMovie);

    /**
     * 修改短剧
     *
     * @param dramaRoomMovie 短剧
     * @return 结果
     */
    public int updateDramaRoomMovie(DramaRoomMovie dramaRoomMovie);

    /**
     * 删除短剧
     *
     * @param id 短剧主键
     * @return 结果
     */
    public int deleteDramaRoomMovieById(Long id);

    /**
     * 批量删除短剧
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteDramaRoomMovieByIds(Long[] ids);

    /**
     * 根据ids查询短剧
     *
     * @param movieIds ids
     * @return 短剧
     */
    List<DramaRoomMovie> selectDramaRoomMovieByIds(Long[] movieIds);

    /**
     * 根据分类id查询短剧
     *
     * @param categoryId 分类id
     * @return 短剧
     */
    List<DramaRoomMovie> selectDramaRoomMovieByCategoryId(@Param("categoryId") Long categoryId);

    /**
     * 获取热播短剧
     * @param i
     * @return
     */
    List<DramaRoomMovie> getHotRecommend(int i);

    /**
     * 获取最新短剧
     * @param i = 10
     * @return
     */
    List<DramaRoomMovie> selectDramaRoomMovieNewList(int i);

    /**
     * 条件查询短剧
     *
     * @param queryDTO
     * @return
     */
    List<DramaRoomMovie> queryMovie(DramaRoomMovieQueryDTO queryDTO);


    /**
     * 获取热搜短剧
     * @param i
     * @return
     */
    List<DramaRoomMovie> getHotSearch(int i);
}
