package com.ruoyi.drama.mapper;

import java.util.List;
import com.ruoyi.drama.domain.DramaRoomCategoryI18n;
import org.apache.ibatis.annotations.Param;

/**
 * 分类多语言Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-08-05
 */
public interface DramaRoomCategoryI18nMapper 
{
    /**
     * 查询分类多语言
     * 
     * @param id 分类多语言主键
     * @return 分类多语言
     */
    public DramaRoomCategoryI18n selectDramaRoomCategoryI18nById(Long id);

    /**
     * 查询分类多语言列表
     * 
     * @param dramaRoomCategoryI18n 分类多语言
     * @return 分类多语言集合
     */
    public List<DramaRoomCategoryI18n> selectDramaRoomCategoryI18nList(DramaRoomCategoryI18n dramaRoomCategoryI18n);

    /**
     * 新增分类多语言
     * 
     * @param dramaRoomCategoryI18n 分类多语言
     * @return 结果
     */
    public int insertDramaRoomCategoryI18n(DramaRoomCategoryI18n dramaRoomCategoryI18n);

    /**
     * 修改分类多语言
     * 
     * @param dramaRoomCategoryI18n 分类多语言
     * @return 结果
     */
    public int updateDramaRoomCategoryI18n(DramaRoomCategoryI18n dramaRoomCategoryI18n);

    /**
     * 删除分类多语言
     * 
     * @param id 分类多语言主键
     * @return 结果
     */
    public int deleteDramaRoomCategoryI18nById(Long id);

    /**
     * 批量删除分类多语言
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteDramaRoomCategoryI18nByIds(Long[] ids);

    List<DramaRoomCategoryI18n> selectByCategoryIdListAndLanguageCode(@Param("list") List<Long> list, @Param("languageCode") String languageCode);

    /**
     * 批量插入多语言
     * @param i18ns
     */
    void insertDramaRoomCategoryI18nList(@Param("i18ns") List<DramaRoomCategoryI18n> i18ns);

    /**
     * 根据分类id列表查询多语言
     * @param categoryIds
     * @return
     */
    List<DramaRoomCategoryI18n> selectDramaRoomCategoryI18nListByCategoryIds(@Param("categoryIds") List<Long> categoryIds);

    /**
     * 根据分类id列表删除多语言
     * @param ids
     */
    void deleteDramaRoomCategoryI18nByCategoryIds(@Param("ids") Long[] ids);

    /**
     * 根据分类id删除多语言
     * @param id
     */
    void deleteDramaRoomCategoryI18nByCategoryId(@Param("id") Long id);
}
