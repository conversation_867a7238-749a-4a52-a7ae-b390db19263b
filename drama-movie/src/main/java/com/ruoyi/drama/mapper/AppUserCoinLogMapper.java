package com.ruoyi.drama.mapper;

import com.ruoyi.common.drama.vo.UnlockEpisodeRecordsVO;
import com.ruoyi.common.drama.vo.UserCoinLogVO;
import com.ruoyi.drama.domain.AppUserCoinLog;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 金币记录Mapper接口
 *
 * <AUTHOR>
 * @date 2025-07-29
 */
public interface AppUserCoinLogMapper
{
    /**
     * 查询金币记录
     *
     * @param id 金币记录主键
     * @return 金币记录
     */
    public AppUserCoinLog selectAppUserCoinLogById(Long id);

    /**
     * 查询金币记录列表
     *
     * @param appUserCoinLog 金币记录
     * @return 金币记录集合
     */
    public List<AppUserCoinLog> selectAppUserCoinLogList(AppUserCoinLog appUserCoinLog);

    /**
     * 新增金币记录
     *
     * @param appUserCoinLog 金币记录
     * @return 结果
     */
    public int insertAppUserCoinLog(AppUserCoinLog appUserCoinLog);

    /**
     * 修改金币记录
     *
     * @param appUserCoinLog 金币记录
     * @return 结果
     */
    public int updateAppUserCoinLog(AppUserCoinLog appUserCoinLog);

    /**
     * 删除金币记录
     *
     * @param id 金币记录主键
     * @return 结果
     */
    public int deleteAppUserCoinLogById(Long id);

    /**
     * 批量删除金币记录
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteAppUserCoinLogByIds(Long[] ids);

    List<UserCoinLogVO> getSignInByUserId(Long userId);

    List<AppUserCoinLog> selectUserCoinLogByUserIdAndTypeIncomeType(@Param("userId") Long userId,@Param("incomeType") Integer incomeType);

    int selectByTypeAndUserIdAndSourceId(@Param("type") Long type, @Param("userId") Long userId, @Param("signInRuleInfoId") Long signInRuleInfoId);
}
