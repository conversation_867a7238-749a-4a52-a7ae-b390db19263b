package com.ruoyi.drama.mapper;

import com.ruoyi.drama.domain.DramaRoomSignInRule;

import java.util.List;

/**
 * 签到规则Mapper接口
 *
 * <AUTHOR>
 * @date 2025-07-29
 */
public interface DramaRoomSignInRuleMapper
{
    /**
     * 查询签到规则
     *
     * @param id 签到规则主键
     * @return 签到规则
     */
    public DramaRoomSignInRule selectDramaRoomSignInRuleById(Long id);

    /**
     * 查询签到规则列表
     *
     * @param dramaRoomSignInRule 签到规则
     * @return 签到规则集合
     */
    public List<DramaRoomSignInRule> selectDramaRoomSignInRuleList(DramaRoomSignInRule dramaRoomSignInRule);

    /**
     * 新增签到规则
     *
     * @param dramaRoomSignInRule 签到规则
     * @return 结果
     */
    public int insertDramaRoomSignInRule(DramaRoomSignInRule dramaRoomSignInRule);

    /**
     * 修改签到规则
     *
     * @param dramaRoomSignInRule 签到规则
     * @return 结果
     */
    public int updateDramaRoomSignInRule(DramaRoomSignInRule dramaRoomSignInRule);

    /**
     * 删除签到规则
     *
     * @param id 签到规则主键
     * @return 结果
     */
    public int deleteDramaRoomSignInRuleById(Long id);

    /**
     * 批量删除签到规则
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteDramaRoomSignInRuleByIds(Long[] ids);
}
