package com.ruoyi.drama.strategy;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.github.xiaoymin.knife4j.core.util.CollectionUtils;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.drama.domain.ShortAppPurchaseVerifyResult;
import com.ruoyi.common.drama.dto.IapNotifyDTO;
import com.ruoyi.common.drama.dto.IapVerifyDTO;
import com.ruoyi.common.drama.vo.IapVerifyResultVO;
import com.ruoyi.common.enums.PayChannelEnum;
import com.ruoyi.common.utils.IAPUtil;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.drama.manager.MovieAsyncManager;
import com.ruoyi.drama.manager.factory.OrderStrategy;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class AppleIapStrategy implements PaymentStrategy {

    @Value(value = "${iap.sandbox}")
    private Boolean isSandBox;

    @Override
    public String getChannelCode() {
        return PayChannelEnum.APPLE_IAP.getCode();
    }

    /**
     * 验证内购票据(一次性购买喝订阅)
     *
     * @param dto
     * @return
     */
    @Override
    public R<?> verifyPayment(IapVerifyDTO dto) {
        String receipt = dto.getReceipt();
        // 1.验证票据
        String verificationJson = IAPUtil.verifyApple(receipt, isSandBox);
        // 2.构建vo
        IapVerifyResultVO vo = new IapVerifyResultVO();
        vo.setSuccess(false);
        // 3.解析票据
        if (StringUtils.isEmpty(verificationJson)) {
            log.error("用户【{}】票据【{}】验证失败，请检查票据", dto.getUserId(), receipt);
            return R.fail(vo);
        }
        JSONObject receiptData = JSONObject.parseObject(verificationJson);
        log.info("【成功】苹果内购验证返回结果:{}", receiptData.toJSONString());
        Integer status = receiptData.getInteger("status");
        // 4.验证结果
        if (status == 0 || status == 21006) {
            // 状态码0表示验证成功
            // 状态码21006表示收据有效但订阅已过期，这种情况我们也需要处理
            // 4.1解析验证返回数据
            List<ShortAppPurchaseVerifyResult> appleVerifyResultList = setVerifyAppleResult(receipt, receiptData);

            // 4.2异步处理订单和流水
            MovieAsyncManager.me().execute(OrderStrategy.iapBatchCreateOrder(appleVerifyResultList, dto, verificationJson));

            // 4.3返回结果
            List<String> transactionIds = appleVerifyResultList.stream()
                    .map(ShortAppPurchaseVerifyResult::getTransactionId)
                    .distinct()
                    .collect(Collectors.toList());
            vo.setValidTransactions(transactionIds);
            vo.setSuccess(true);
            return R.ok(vo);
        } else {
            return R.fail(vo);
        }
    }

    @Override
    public R<?> handleSubscriptionNotify(IapNotifyDTO dto) {
        try {
            log.info("开始处理苹果订阅通知: {}", dto);
            String notificationType = dto.getNotificationType();
            Map<String, Object> data = dto.getData();
            if (data == null) {
                log.warn("苹果订阅通知数据为空");
                return R.fail("通知数据为空");
            }

            // 根据不同类型的通知进行处理
            switch (notificationType) {
                case "DID_RENEW":
                    // 订阅续订成功
                    log.info("处理订阅续订成功通知");
                    // 这里可以更新用户的订阅状态和有效期
                    break;
                case "EXPIRED":
                    // 订阅过期
                    log.info("处理订阅过期通知");
                    // 这里可以更新用户的订阅状态为过期
                    break;
                case "DID_FAIL_TO_RENEW":
                    // 订阅续订失败
                    log.info("处理订阅续订失败通知");
                    // 可以通知用户续订失败
                    break;
                case "CANCEL":
                    // 用户取消订阅
                    log.info("处理用户取消订阅通知");
                    // 更新用户的订阅状态
                    break;
                default:
                    log.info("收到未处理的通知类型: {}", notificationType);
                    break;
            }

            // 可以根据data中的originalTransactionId等信息查找用户并更新其订阅状态
            // String originalTransactionId = (String) data.get("originalTransactionId");

            return R.ok("处理成功");
        } catch (Exception e) {
            log.error("处理苹果订阅通知异常", e);
            return R.fail("处理通知失败: " + e.getMessage());
        }
    }

    private List<ShortAppPurchaseVerifyResult> setVerifyAppleResult(String extra, JSONObject receiptData) {
        JSONObject receiptInfo = receiptData.getJSONObject("receipt");

        ArrayList<ShortAppPurchaseVerifyResult> resultList = new ArrayList<>();

        // 处理最新的交易信息（适用于订阅）
        JSONArray latestReceiptInfo = receiptData.getJSONArray("latest_receipt_info");
        if (CollectionUtils.isNotEmpty(latestReceiptInfo)) {
            log.info("处理latest_receipt_info数据，共{}条记录", latestReceiptInfo.size());
            // 对于订阅，我们主要关注最新的交易信息
            latestReceiptInfo.forEach(obj -> {
                JSONObject transactionJson = (JSONObject) obj;

                ShortAppPurchaseVerifyResult verifyResult = new ShortAppPurchaseVerifyResult();
                verifyResult.setOrderType("订阅");
                verifyResult.setReceipt(extra);
                verifyResult.setAppType("Apple");
                verifyResult.setJsonObject(String.valueOf(receiptData));
                verifyResult.setStatusCode(receiptData.getInteger("status"));
                verifyResult.setEnvironment(receiptData.getString("environment"));

                // 设置订阅相关信息
                verifyResult.setProductId(transactionJson.getString("product_id"));
                verifyResult.setTransactionId(transactionJson.getString("transaction_id"));
                verifyResult.setOriginalTransactionId(transactionJson.getString("original_transaction_id"));
                verifyResult.setPurchaseDate(transactionJson.getString("purchase_date"));
                verifyResult.setExpiresDate(transactionJson.getString("expires_date"));
                verifyResult.setIsTrialPeriod(transactionJson.getString("is_trial_period"));
                verifyResult.setIsInIntroOfferPeriod(transactionJson.getString("is_in_intro_offer_period"));
                verifyResult.setSubscriptionAutoRenewStatus(
                        transactionJson.containsKey("auto_renew_status") ?
                                transactionJson.getString("auto_renew_status") : "0");

                resultList.add(verifyResult);
            });
        } else if (receiptInfo != null) {
            // 处理传统的in_app数据（适用于一次性购买）
            JSONArray inAppList = receiptInfo.getJSONArray("in_app");
            // ios7之前的数据格式
            if (CollectionUtils.isNotEmpty(inAppList)) {
                log.info("有【in_app】数据:{}", inAppList.toJSONString());
                inAppList.forEach(inApp -> {
                    JSONObject inAppJson = (JSONObject) inApp;

                    ShortAppPurchaseVerifyResult verifyResult = new ShortAppPurchaseVerifyResult();
                    verifyResult.setOrderType("充值");
                    verifyResult.setReceipt(extra);// 保存前端传递的收据数据
                    verifyResult.setAppType("Apple");
                    verifyResult.setJsonObject(String.valueOf(receiptData));
                    verifyResult.setStatusCode(receiptData.getInteger("status"));
                    verifyResult.setEnvironment(receiptData.getString("environment"));

                    verifyResult.setAdamId(receiptInfo.getString("adam_id"));
                    verifyResult.setAppItemId(receiptInfo.getString("app_item_id"));
                    verifyResult.setBundleId(receiptInfo.getString("bundle_id"));
                    verifyResult.setApplicationVersion(receiptInfo.getString("application_version"));
                    verifyResult.setReceiptCreationDate(receiptInfo.getString("receipt_creation_date"));
                    verifyResult.setRequestDate(receiptInfo.getString("request_date"));

                    verifyResult.setQuantity(inAppJson.getString("quantity"));
                    verifyResult.setProductId(inAppJson.getString("product_id"));
                    verifyResult.setTransactionId(inAppJson.getString("transaction_id"));
                    verifyResult.setPurchaseDate(inAppJson.getString("purchase_date"));

                    resultList.add(verifyResult);
                });
            }
            // ios7之后的数据格式
            if (CollectionUtils.isEmpty(inAppList)) {
                log.info("无【in_app】数据:{}", receiptInfo.toJSONString());

                ShortAppPurchaseVerifyResult verifyResult = new ShortAppPurchaseVerifyResult();
                verifyResult.setOrderType("充值");
                verifyResult.setReceipt(extra);// 保存前端传递的收据数据
                verifyResult.setAppType("Apple");
                verifyResult.setJsonObject(String.valueOf(receiptData));
                verifyResult.setStatusCode(receiptData.getInteger("status"));
                verifyResult.setEnvironment(receiptData.getString("environment"));

                verifyResult.setAdamId(receiptInfo.getString("adam_id"));
                verifyResult.setAppItemId(receiptInfo.getString("app_item_id"));
                verifyResult.setBundleId(receiptInfo.getString("bundle_id"));
                verifyResult.setApplicationVersion(receiptInfo.getString("application_version"));
                verifyResult.setReceiptCreationDate(receiptInfo.getString("receipt_creation_date"));
                verifyResult.setRequestDate(receiptInfo.getString("request_date"));

                verifyResult.setQuantity(receiptInfo.getString("quantity"));
                verifyResult.setProductId(receiptInfo.getString("product_id"));
                verifyResult.setTransactionId(receiptInfo.getString("transaction_id"));
                verifyResult.setPurchaseDate(receiptInfo.getString("purchase_date"));

                resultList.add(verifyResult);
            }
        }
        return resultList;
    }
}
