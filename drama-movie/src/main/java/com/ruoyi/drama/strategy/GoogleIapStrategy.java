package com.ruoyi.drama.strategy;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.google.api.client.googleapis.auth.oauth2.GoogleCredential;
import com.google.api.client.googleapis.javanet.GoogleNetHttpTransport;
import com.google.api.client.json.jackson2.JacksonFactory;
import com.google.api.services.androidpublisher.AndroidPublisher;
import com.google.api.services.androidpublisher.AndroidPublisherScopes;
import com.google.api.services.androidpublisher.model.ProductPurchase;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.drama.dto.IapNotifyDTO;
import com.ruoyi.common.drama.dto.IapVerifyDTO;
import com.ruoyi.common.enums.PayChannelEnum;
import com.ruoyi.drama.domain.DramaRoomPaymentChannel;
import com.ruoyi.drama.mapper.DramaRoomPaymentChannelMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.ByteArrayInputStream;
import java.util.Collections;

@Slf4j
@Service
@RequiredArgsConstructor
public class GoogleIapStrategy implements PaymentStrategy {

    @Autowired
    private DramaRoomPaymentChannelMapper dramaRoomPaymentChannelMapper;

    @Value(value = "${iap.sandbox}")
    private Boolean isSandBox;

    @Override
    public String getChannelCode() {
        return PayChannelEnum.GOOGLE_IAP.getCode();
    }

    @Override
    public R<?> verifyPayment(IapVerifyDTO dto) {
        // 票据
        String receipt = dto.getReceipt();

        // 1. 获取渠道配置
        DramaRoomPaymentChannel channel = getValidChannel(dto.getChannelCode());
        if (channel == null) {
            return R.fail("渠道不存在");
        }
        JSONObject config = JSON.parseObject(channel.getConfig());
        String packageName = config.getString("packageName");
        String serviceAccountJson = config.getString("serviceAccountJson");

        // 2. 解析购买令牌
        JSONObject extra = JSON.parseObject(receipt);
        String productId = extra.getString("productId");
        String purchaseToken = extra.getString("purchaseToken");
        if (purchaseToken == null) {
            return R.fail("购买令牌不能为空");
        }

        try {
            // 3. 初始化谷歌API客户端
            GoogleCredential credential = GoogleCredential.fromStream(
                            new ByteArrayInputStream(serviceAccountJson.getBytes()))
                    .createScoped(Collections.singleton(AndroidPublisherScopes.ANDROIDPUBLISHER));

            AndroidPublisher publisher = new AndroidPublisher.Builder(
                    GoogleNetHttpTransport.newTrustedTransport(),
                    JacksonFactory.getDefaultInstance(),
                    credential)
                    .setApplicationName(packageName)
                    .build();

            // 4. 验证购买凭证
            AndroidPublisher.Purchases.Products.Get request = publisher.purchases().products()
                    .get(packageName, productId, purchaseToken);
            ProductPurchase purchase = request.execute();

            // 5. 验证状态（PURCHASED为成功）
            if (!"PURCHASED".equals(purchase.getPurchaseState())) {
                log.error("谷歌内购未完成: {}", purchase.getPurchaseState());
                return R.fail("谷歌内购未完成");
            }
            // 6. 异步处理订单和流水
//            MovieAsyncManager.me().execute(OrderStrategy.iapBatchCreateOrder(null, dto, receipt));

            return R.ok("success");
        } catch (Exception e) {
            log.error("Google IAP 验证失败：{}", e.getMessage());
            return R.fail("Google IAP 验证失败");
        }
    }

    @Override
    public R<?> handleSubscriptionNotify(IapNotifyDTO dto) {
        return null;
    }

    private DramaRoomPaymentChannel getValidChannel(String channelCode) {
        return dramaRoomPaymentChannelMapper.selectDramaRoomPaymentChannel(channelCode);
    }
}
