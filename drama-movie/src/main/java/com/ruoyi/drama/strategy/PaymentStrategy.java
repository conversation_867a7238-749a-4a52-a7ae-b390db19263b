package com.ruoyi.drama.strategy;

import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.drama.dto.IapNotifyDTO;
import com.ruoyi.common.drama.dto.IapVerifyDTO;

/**
 * 支付策略接口（内购实现需遵循此标准）
 */
public interface PaymentStrategy {
    // 获取渠道编码（APPLE_IAP/GOOGLE_IAP）
    String getChannelCode();

    // 验证支付凭证
    R<?> verifyPayment(IapVerifyDTO dto);

    // 处理订阅内购回调
    R<?> handleSubscriptionNotify(IapNotifyDTO dto);
}
