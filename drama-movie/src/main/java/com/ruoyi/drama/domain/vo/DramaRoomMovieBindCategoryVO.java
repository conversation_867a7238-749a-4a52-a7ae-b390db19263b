package com.ruoyi.drama.domain.vo;

import com.ruoyi.common.utils.bean.BeanUtils;
import com.ruoyi.drama.domain.DramaRoomMovie;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class DramaRoomMovieBindCategoryVO extends DramaRoomMovie {

    // 是否绑定
    private Boolean isBind;

    /**
     * 根据已绑定短剧ID列表设置所有短剧的绑定状态
     *
     * @param allMovie 所有短剧列表
     * @param movieIds 已绑定短剧ID列表
     */
    public static List<DramaRoomMovieBindCategoryVO> setBindStatus(List<DramaRoomMovie> allMovie, List<Long> movieIds) {
        if (CollectionUtils.isEmpty(allMovie)) {
            return Collections.emptyList();
        }
        List<DramaRoomMovieBindCategoryVO> vos = new ArrayList<>();
        if (CollectionUtils.isEmpty(movieIds)) {
            for (DramaRoomMovie dramaRoomMovie : allMovie) {
                DramaRoomMovieBindCategoryVO vo = new DramaRoomMovieBindCategoryVO();
                BeanUtils.copyBeanProp(vo, dramaRoomMovie);
                vo.setIsBind(false);
                vos.add(vo);
            }
            return vos;
        }
        for (DramaRoomMovie dramaRoomMovie : allMovie) {
            DramaRoomMovieBindCategoryVO vo = new DramaRoomMovieBindCategoryVO();
            BeanUtils.copyBeanProp(vo, dramaRoomMovie);
            vo.setIsBind(movieIds.contains(dramaRoomMovie.getId()));
            vos.add(vo);
        }
        return vos;
    }
}
