package com.ruoyi.drama.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 用户订阅对象 app_user_subscription
 * 
 * <AUTHOR>
 * @date 2025-09-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("AppUserSubscription对象")
public class AppUserSubscription extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    @ApiModelProperty("主键ID")
    private Long id;

    /** 用户ID，关联用户表 */
    @ApiModelProperty("用户ID，关联用户表")
    @Excel(name = "用户ID，关联用户表")
    private Long userId;

    /** vip id */
    @ApiModelProperty("vip id")
    @Excel(name = "vip id")
    private Long vipId;

    /** 订阅方案ID，关联订阅方案表 */
    @ApiModelProperty("订阅方案ID，关联订阅方案表")
    @Excel(name = "订阅方案ID，关联订阅方案表")
    private Long subscriptionId;

    /** 关联的订单ID */
    @ApiModelProperty("关联的订单ID")
    @Excel(name = "关联的订单ID")
    private Long orderId;

    /** 订阅状态：0-未生效，1-生效中，2-已过期，3-已取消 */
    @ApiModelProperty("订阅状态：0-未生效，1-生效中，2-已过期，3-已取消")
    @Excel(name = "订阅状态：0-未生效，1-生效中，2-已过期，3-已取消")
    private Long subscriptionStatus;

    /** 订阅时间 */
    @ApiModelProperty("订阅时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "订阅时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date subscribeTime;

    /** 生效开始时间 */
    @ApiModelProperty("生效开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "生效开始时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    /** 生效结束时间 */
    @ApiModelProperty("生效结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "生效结束时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    /** 订阅时长(天) */
    @ApiModelProperty("订阅时长(天)")
    @Excel(name = "订阅时长(天)")
    private Long durationDays;

    /** 是否自动续费：0-否，1-是 */
    @ApiModelProperty("是否自动续费：0-否，1-是")
    @Excel(name = "是否自动续费：0-否，1-是")
    private Long autoRenew;

    /** 最后一次续费时间 */
    @ApiModelProperty("最后一次续费时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "最后一次续费时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date lastRenewTime;

    /** 取消订阅时间 */
    @ApiModelProperty("取消订阅时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "取消订阅时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date cancelTime;
}