package com.ruoyi.drama.domain;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class DramaRoomMovieAction {

    /** 电影ID*/
    private Long id;

    /** 播放次数 */
    @ApiModelProperty(value = "播放次数，后台统计不填")
    private Long playCount;

    /** 点赞次数 */
    @ApiModelProperty(value = "点赞次数，后台统计不填")
    private Long likeCount;

    @ApiModelProperty(value = "收藏次数，后台统计不填")
    private Long collectCount;

    @ApiModelProperty(value = "搜索次数，后台统计不填")
    private Long searchCount;

}
