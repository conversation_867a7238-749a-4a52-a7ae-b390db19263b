package com.ruoyi.drama.domain;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import com.ruoyi.common.drama.vo.VideoVO;
import com.ruoyi.common.utils.ResourceLanguageConvert;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 剧集对象 drama_room_video
 *
 * <AUTHOR>
 * @date 2025-07-04
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(description = "剧集对象")
public class DramaRoomVideo extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 剧集ID
     */
    @ApiModelProperty(value = "剧集ID")
    private Long id;

    /**
     * 短剧ID
     */
    @ApiModelProperty(value = "短剧ID")
    @Excel(name = "短剧ID")
    private Long movieId;

    /**
     * 集数
     */
    @ApiModelProperty(value = "集数")
    @Excel(name = "集数")
    private Long videoNum;

    /**
     * 视频地址
     */
    @ApiModelProperty(value = "视频地址")
    @Excel(name = "视频地址")
    private String videoUrl;

    /**
     * 时长(秒)
     */
    @ApiModelProperty(value = "时长(秒)")
    @Excel(name = "时长(秒)")
    private Long duration;

    /**
     * 播放次数
     */
    @ApiModelProperty(value = "播放次数，后台统计不填")
    @Excel(name = "播放次数")
    private Long playCount;


    /**
     * 点赞次数
     */
    @ApiModelProperty(value = "点赞次数，后台统计不填")
    @Excel(name = "点赞次数")
    private Long likeCount;

    /**
     * 状态（0正常 1停用 2禁用）
     */
    @ApiModelProperty(value = "状态（0正常 1停用 2禁用）")
    @Excel(name = "状态", readConverterExp = "0=正常,1=停用,2=禁用")
    private String status;

    @ApiModelProperty("封面图片")
    private String coverImage;

    @ApiModelProperty("字幕地址")
    private String subtitleUrl;

    @ApiModelProperty("默认扣费金币")
    private Integer coin;

    @ApiModelProperty("剧集多语言信息")
    private List<DramaRoomVideoI18n> videoI18nList;



    public VideoVO toVideoVO() {
        VideoVO videoVO = new VideoVO();
        videoVO.setId(this.id);
        videoVO.setCoverImage(this.coverImage);
        videoVO.setVideoNum(this.videoNum);
        videoVO.setVideoUrl(this.videoUrl);
        videoVO.setDuration(this.duration.toString());
        videoVO.setPlayCount(this.playCount);
        videoVO.setLikeCount(this.likeCount);
        videoVO.setLike(false); // 默认未点赞
        videoVO.setUnlockCoin(this.coin);
        return videoVO;
    }

    public static List<VideoVO> toVideoVOList(List<DramaRoomVideo> videoList) {
        if (CollectionUtils.isEmpty(videoList)) {
            return Collections.emptyList();
        }
        return videoList.stream().map(DramaRoomVideo::toVideoVO)
                .collect(Collectors.toList());
    }
}
