package com.ruoyi.drama.domain;

import java.math.BigDecimal;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 订单对象 drama_room_order
 *
 * <AUTHOR>
 * @date 2025-08-14
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("DramaRoomOrder对象")
public class DramaRoomOrder extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 订单ID
     */
    @ApiModelProperty("订单ID")
    private Long id;

    /**
     * 订单编号
     */
    @ApiModelProperty("订单编号")
    @Excel(name = "订单编号")
    private String orderNo;

    /**
     * 用户ID
     */
    @ApiModelProperty("用户ID")
    @Excel(name = "用户ID")
    private Long userId;

    /**
     * 商品ID
     */
    @ApiModelProperty("商品ID")
    @Excel(name = "商品ID")
    private String productId;

    /**
     * 商品类型,0充值，1订阅
     */
    @ApiModelProperty("商品类型")
    @Excel(name = "商品类型")
    private Integer productType;

    /**
     * 订单金额
     */
    @ApiModelProperty("订单金额")
    @Excel(name = "订单金额")
    private BigDecimal amount;

    /**
     * 订单状态：0-待支付，1-已支付，2-已取消，3-已关闭
     */
    @ApiModelProperty("订单状态：0-待支付，1-已支付，2-已取消，3-已关闭")
    @Excel(name = "订单状态：0-待支付，1-已支付，2-已取消，3-已关闭")
    private Integer status;

    /**
     * 订单过期时间
     */
    @ApiModelProperty("订单过期时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "订单过期时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date expireTime;

    /**
     * 关闭原因：EXPIRE-过期自动关闭，USER_CANCEL-用户取消，SYSTEM-系统关闭
     */
    @ApiModelProperty("关闭原因：EXPIRE-过期自动关闭，USER_CANCEL-用户取消，SYSTEM-系统关闭")
    @Excel(name = "关闭原因：EXPIRE-过期自动关闭，USER_CANCEL-用户取消，SYSTEM-系统关闭")
    private String closeReason;

    /**
     * 退款状态：0-未退款，1-退款中，2-部分退款，3-全额退款
     */
    @ApiModelProperty("退款状态：0-未退款，1-退款中，2-部分退款，3-全额退款")
    @Excel(name = "退款状态：0-未退款，1-退款中，2-部分退款，3-全额退款")
    private Integer refundStatus;

    /**
     * 支付渠道编码：APPLE_IAP、GOOGLE_IAP等
     */
    @ApiModelProperty("支付渠道编码：APPLE_IAP、GOOGLE_IAP等")
    @Excel(name = "支付渠道编码：APPLE_IAP、GOOGLE_IAP等")
    private String channelCode;

    /**
     * 支付方式
     */
    @ApiModelProperty("支付方式")
    @Excel(name = "支付方式")
    private String paymentMethod;

    /**
     * 支付时间
     */
    @ApiModelProperty("支付时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "支付时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date paymentTime;

    /**
     * 渠道订单号
     */
    @ApiModelProperty("渠道订单号")
    @Excel(name = "渠道订单号")
    private String channelOrderId;

    /**
     * 渠道交易流水号
     */
    @ApiModelProperty("渠道交易流水号")
    @Excel(name = "渠道交易流水号")
    private String channelTransactionId;

    /**
     * 支付环境：0：沙盒，1正式
     */
    @ApiModelProperty("支付环境：0：沙盒，1正式")
    @Excel(name = "支付环境：0：沙盒，1正式")
    private Integer payEnvironment;
}