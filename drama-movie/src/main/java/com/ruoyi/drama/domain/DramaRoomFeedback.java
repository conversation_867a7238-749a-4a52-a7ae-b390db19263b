package com.ruoyi.drama.domain;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 用户反馈对象 drama_room_feedback
 *
 * <AUTHOR>
 * @date 2025-07-28
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class DramaRoomFeedback extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** id */
    private Long id;

    /** 用户id */
    @Excel(name = "用户id")
    private Long userId;

    /** 反馈内容 */
    @Excel(name = "反馈内容")
    private String content;

    /** 用户邮箱 **/
    @Excel(name = "用户邮箱")
    private String email;

    /** 状态（1启用 0停用） */
    @Excel(name = "状态", readConverterExp = "1=启用,0=停用")
    private String status;


    @Excel(name = "返回类型")
    private String type;
}
