package com.ruoyi.drama.domain;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 短剧标签关联对象 drama_room_video_tag
 *
 * <AUTHOR>
 * @date 2025-07-04
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class DramaRoomMovieTag extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private Long id;

    /**
     * 短剧ID
     */
    @Excel(name = "短剧ID")
    private Long movieId;

    /**
     * 标签ID
     */
    @Excel(name = "标签ID")
    private Long tagId;

}
