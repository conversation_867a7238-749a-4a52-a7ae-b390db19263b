package com.ruoyi.drama.domain;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 【请填写功能名称】对象 drama_room_param_config
 *
 * <AUTHOR>
 * @date 2025-07-04
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(description = "app配置model")
public class DramaRoomParamConfig extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** id */
    private Long id;

    /** key */
    @ApiModelProperty("key")
    @Excel(name = "key")
    private String paramKey;

    /** value */
    @ApiModelProperty("value")
    @Excel(name = "value")
    private String paramValue;

}
