package com.ruoyi.drama.domain;

import com.ruoyi.common.drama.vo.MovieAudioVO;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * 音讯对象 drama_room_audio
 *
 * <AUTHOR>
 * @date 2025-07-28
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(description = "音讯对象")
public class DramaRoomAudio extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** id */
    private Long id;

    /** 音讯类别 */
    @Excel(name = "音讯类别")
    private String audio;

    /** 音讯多语言列表 */
    private List<DramaRoomAudioI18n> i18nList;

    public static List<MovieAudioVO> toVOList(List<DramaRoomAudio> list) {
        if(CollectionUtils.isEmpty(list)){
            return Collections.emptyList();
        }
        List<MovieAudioVO> vos = new ArrayList<>();
        for (DramaRoomAudio audio : list) {
            vos.add(audio.toVO());
        }
        return vos;
    }

    private MovieAudioVO toVO() {
        return new MovieAudioVO(this.getId(), this.getAudio());
    }
}
