package com.ruoyi.drama.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import com.ruoyi.common.drama.vo.UserCollectVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 【请填写功能名称】对象 drama_room_user_favorite
 *
 * <AUTHOR>
 * @date 2025-07-04
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "用户收藏记录表")
public class DramaRoomUserCollect extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * $column.columnComment
     */
    private Long id;

    /**
     * 用户ID
     */
    @ApiModelProperty(value = "用户ID")
    @Excel(name = "用户ID")
    private Long userId;

    /**
     * 短剧ID
     */
    @ApiModelProperty(value = "短剧ID")
    @Excel(name = "短剧ID")
    private Long movieId;

    /**
     * 收藏状态：0收藏 1取消收藏
     */
    @ApiModelProperty(value = "收藏状态：0收藏 1取消收藏")
    @Excel(name = "收藏状态：0收藏 1取消收藏")
    private Long status;


    public UserCollectVO convert2UserCollectVO(){
        UserCollectVO userCollectVO = new UserCollectVO();
        userCollectVO.setUserId(this.getUserId());
        userCollectVO.setMovieId(this.getMovieId());
        userCollectVO.setStatus(this.getStatus());
        userCollectVO.setId(this.getId());
        return userCollectVO;
    }

    public static List<UserCollectVO> convert2UserCollectVOList(List<DramaRoomUserCollect> collectList){
        List<UserCollectVO> collectVOList = new ArrayList<>();
        for (DramaRoomUserCollect collect : collectList) {
            collectVOList.add(collect.convert2UserCollectVO());
        }
        return collectVOList;
    }

}
