package com.ruoyi.drama.domain;

import java.math.BigDecimal;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.ruoyi.common.annotation.Excel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 金额模板对象 drama_room_pay_template_amt
 * 
 * <AUTHOR>
 * @date 2025-08-11
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("DramaRoomPayTemplateAmt对象")
public class DramaRoomPayTemplateAmt extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    @ApiModelProperty("主键ID")
    private Long id;

    /** 价格 */
    @ApiModelProperty("价格")
    @Excel(name = "价格")
    private BigDecimal price;

    /** 金币数量 */
    @ApiModelProperty("金币数量")
    @Excel(name = "金币数量")
    private Long coin;

    /** 苹果内购商品id */
    @ApiModelProperty("苹果内购商品id")
    @Excel(name = "苹果内购商品id")
    private String appleProductId;

    /** 谷歌内购商品id */
    @ApiModelProperty("谷歌内购商品id")
    @Excel(name = "谷歌内购商品id")
    private String googleProductId;

}