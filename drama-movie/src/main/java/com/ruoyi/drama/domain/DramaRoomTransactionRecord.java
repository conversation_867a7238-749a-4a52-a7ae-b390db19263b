package com.ruoyi.drama.domain;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 交易记录（包含支付和退款）对象 drama_room_transaction_record
 *
 * <AUTHOR>
 * @date 2025-08-14
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("DramaRoomTransactionRecord对象")
public class DramaRoomTransactionRecord extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @ApiModelProperty("ID")
    private Long id;

    /**
     * 订单ID
     */
    @ApiModelProperty("订单ID")
    @Excel(name = "订单ID")
    private Long orderId;

    /**
     * 交易编号
     */
    @ApiModelProperty("交易编号")
    @Excel(name = "交易编号")
    private String transNo;

    /**
     * 用户ID
     */
    @ApiModelProperty("用户ID")
    @Excel(name = "用户ID")
    private Long userId;

    /**
     * 支付渠道编码
     */
    @ApiModelProperty("支付渠道编码")
    @Excel(name = "支付渠道编码")
    private String channelCode;

    /**
     * 交易金额
     */
    @ApiModelProperty("交易金额")
    @Excel(name = "交易金额")
    private BigDecimal amount;

    /**
     * 交易类型：1-支付，2-退款
     */
    @ApiModelProperty("交易类型：1-支付，2-退款")
    @Excel(name = "交易类型：1-支付，2-退款")
    private Integer type;

    /**
     * 状态：0-处理中，1-成功，2-失败
     */
    @ApiModelProperty("状态：0-处理中，1-成功，2-失败")
    @Excel(name = "状态：0-处理中，1-成功，2-失败")
    private Integer status;

    /**
     * 第三方交易号
     */
    @ApiModelProperty("第三方交易号")
    @Excel(name = "第三方交易号")
    private String transactionId;

    /**
     * 支付/退款方式
     */
    @ApiModelProperty("支付/退款方式")
    @Excel(name = "支付/退款方式")
    private String method;

    /**
     * 退款原因（仅退款时有效）
     */
    @ApiModelProperty("退款原因（仅退款时有效）")
    @Excel(name = "退款原因", readConverterExp = "仅=退款时有效")
    private String reason;

    /**
     * 额外信息（JSON格式）
     */
    @ApiModelProperty("额外信息（JSON格式）")
    @Excel(name = "额外信息", readConverterExp = "J=SON格式")
    private String extra;
}