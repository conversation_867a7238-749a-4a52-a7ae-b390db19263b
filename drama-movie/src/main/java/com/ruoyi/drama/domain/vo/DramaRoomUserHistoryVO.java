package com.ruoyi.drama.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@ApiModel(description = "用户观看历史记录VO")
@Data
public class DramaRoomUserHistoryVO extends BaseEntity {

    @ApiModelProperty("用户id")
    @Excel(name = "用户id")
    private Long userId;

    /** 短剧Id */
    @ApiModelProperty("短剧Id")
    @Excel(name = "短剧Id")
    private Long movieId;

    /** 剧集id */
    @ApiModelProperty("剧集id")
    @Excel(name = "剧集id")
    private Long videoId;

    /** 浏览时间 */
    @ApiModelProperty("浏览时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "浏览时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date viewDate;

    /** 浏览进度 */
    @ApiModelProperty("浏览进度")
    @Excel(name = "浏览进度")
    private Long progress;

}
