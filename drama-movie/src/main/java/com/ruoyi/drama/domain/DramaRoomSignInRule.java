package com.ruoyi.drama.domain;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 签到规则对象 drama_room_sign_in_rule
 *
 * <AUTHOR>
 * @date 2025-07-29
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class DramaRoomSignInRule extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    private Long id;

    /** 签到类型：0.基础签到1.断签归零2.自定义签到 */
    @Excel(name = "签到类型：0.基础签到1.断签归零2.自定义签到")
    private Long type;

    /** 签到名称：
     如：
     轮询签到---类型0
     断签归零---类型1
     ----以下对应类型2
     绑定邮箱
     支持我们
     账户登录
     在instagram关注我们
     在facebook关注我们
     在youtube关注我们
     每日观看/ 7个？
     观看完整剧集/20个？ */
    @Excel(name = "签到名称")
//    如：
//    轮询签到---类型0
//    断签归零---类型1
//----以下对应类型2
//        绑定邮箱
//    支持我们
//            账户登录
//    在instagram关注我们
//            在facebook关注我们
//    在youtube关注我们
//    每日观看/ 7个？
//    观看完整剧集/20个？")
    private String name;

    /** 状态（1启用 0停用） */
    @Excel(name = "状态", readConverterExp = "1=启用,0=停用")
    private Integer status;

    @Excel(name = "图标")
    private String icon;

}
