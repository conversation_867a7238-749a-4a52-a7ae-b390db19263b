package com.ruoyi.drama.domain;

import com.ruoyi.common.drama.vo.MovieAudioVO;
import com.ruoyi.common.drama.vo.MovieCategoryVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * 分类对象 drama_room_category
 *
 * <AUTHOR>
 * @date 2025-07-03
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(description = "短剧分类实体类")
public class DramaRoomCategory extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 分类ID */
    @ApiModelProperty(value = "分类ID", example = "1")
    private Long id;

    /** 分类名称 */
    @Excel(name = "分类名称")
    @ApiModelProperty(value = "分类名称", example = "悬疑推理")
    private String categoryName;

    /** 显示顺序 */
    @Excel(name = "显示顺序")
    @ApiModelProperty(value = "显示顺序", example = "1")
    private Long sort;

    /** 状态（0正常 1停用） */
    @Excel(name = "状态", readConverterExp = "0=正常,1=停用")
    @ApiModelProperty(value = "状态（0正常 1停用）", example = "0")
    private String status;


    /** 多语言列表 */
    @ApiModelProperty(value = "多语言列表")
    private List<DramaRoomCategoryI18n> i18nList;

    public static List<MovieCategoryVO> toVOList(List<DramaRoomCategory> list) {
        if(CollectionUtils.isEmpty(list)){
            return Collections.emptyList();
        }
        List<MovieCategoryVO> vos = new ArrayList<>();
        for (DramaRoomCategory category : list) {
            vos.add(category.toVO());
        }
        return vos;
    }

    private MovieCategoryVO toVO() {
        MovieCategoryVO movieCategoryVO = new MovieCategoryVO();
        movieCategoryVO.setId(this.getId());
        movieCategoryVO.setName(this.getCategoryName());
        movieCategoryVO.setSort(this.getSort());
        return movieCategoryVO;
    }
}
