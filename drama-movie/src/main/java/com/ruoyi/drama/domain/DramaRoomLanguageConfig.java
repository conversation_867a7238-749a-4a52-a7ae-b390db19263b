package com.ruoyi.drama.domain;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 大包多语言配置对象 drama_room_language_config
 * 
 * <AUTHOR>
 * @date 2025-08-05
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class DramaRoomLanguageConfig extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 语言名称-源语言 */
    @Excel(name = "语言名称-源语言")
    private String name;

    /** 中文名 */
    @Excel(name = "中文名")
    private String zhName;

    /** 语言编码 */
    @Excel(name = "语言编码")
    private String code;

    /** 语言对应国家图标 */
    @Excel(name = "语言对应国家图标")
    private String icon;

    /** 排序 */
    @Excel(name = "排序")
    private Long sort;

    private String translateStatus;

}
