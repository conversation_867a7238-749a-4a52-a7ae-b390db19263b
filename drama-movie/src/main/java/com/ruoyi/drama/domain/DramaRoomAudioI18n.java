package com.ruoyi.drama.domain;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 音讯多语言对象 drama_room_audio_i18n
 * 
 * <AUTHOR>
 * @date 2025-08-05
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class DramaRoomAudioI18n extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** id */
    private Long id;

    /** 音讯id */
    @Excel(name = "音讯id")
    private Long audioId;

    /** 音讯类别翻译 */
    @Excel(name = "音讯类别翻译")
    private String audio;

    /** 多语言标识 */
    @Excel(name = "多语言标识")
    private String languageCode;

}
