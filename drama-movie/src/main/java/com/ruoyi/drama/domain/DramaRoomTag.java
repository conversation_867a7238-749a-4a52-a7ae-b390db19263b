package com.ruoyi.drama.domain;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import com.ruoyi.common.drama.vo.MovieTagVO;
import com.ruoyi.common.drama.vo.MovieVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * 标签对象 drama_room_tag
 *
 * <AUTHOR>
 * @date 2025-07-04
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(description = "短剧标签对象")
public class DramaRoomTag extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 标签ID
     */
    private Long tagId;

    /**
     * 标签名称
     */
    @ApiModelProperty("标签名称")
    @Excel(name = "标签名称")
    private String tagName;

    /**
     * 显示顺序
     */
    @ApiModelProperty("显示顺序")
    @Excel(name = "显示顺序")
    private Long orderNum;

    /**
     * 状态（0正常 1停用）
     */
    @ApiModelProperty("状态（0正常 1停用）")
    @Excel(name = "状态", readConverterExp = "0=正常,1=停用")
    private String status;

    /**
     * 标签i18n
     */
    @ApiModelProperty("标签i18n")
    private List<DramaRoomTagI18n> i18nList;

    public static List<MovieTagVO> toVOList(List<DramaRoomTag> list) {
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }
        List<MovieTagVO> vos = new ArrayList<>();
        for (DramaRoomTag tag : list) {
            vos.add(tag.toVO());
        }
        return vos;
    }

    private MovieTagVO toVO() {
        MovieTagVO movieTagVO = new MovieTagVO();
        movieTagVO.setTag(this.getTagName());
        movieTagVO.setId(this.getTagId());
        return movieTagVO;
    }

}
