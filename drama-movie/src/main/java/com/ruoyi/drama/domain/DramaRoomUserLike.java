package com.ruoyi.drama.domain;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 用户点赞记录对象 drama_room_user_like
 *
 * <AUTHOR>
 * @date 2025-07-08
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class DramaRoomUserLike extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 用户ID */
    @Excel(name = "用户ID")
    private Long userId;

    /** 短剧ID */
    @Excel(name = "短剧ID")
    private Long movieId;

    /** 视频ID */
    @Excel(name = "视频ID")
    private Long videoId;

    /** 状态: 1=点赞, 0=取消点赞 */
    @Excel(name = "状态: 1=点赞, 0=取消点赞")
    private Long status;

}
