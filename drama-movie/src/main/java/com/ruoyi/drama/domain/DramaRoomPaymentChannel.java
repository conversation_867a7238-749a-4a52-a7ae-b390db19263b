package com.ruoyi.drama.domain;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 支付渠道配置对象 drama_room_payment_channel
 *
 * <AUTHOR>
 * @date 2025-08-08
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class DramaRoomPaymentChannel extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    private Long id;

    /**
     * 渠道编码：APPLE_IAP、GOOGLE_IAP等
     */
    @Excel(name = "渠道编码：APPLE_IAP、GOOGLE_IAP等")
    private String channelCode;

    /**
     * 渠道名称
     */
    @Excel(name = "渠道名称")
    private String channelName;

    /**
     * 渠道配置（JSON格式，如API密钥、商户ID等）
     */
    @Excel(name = "渠道配置", readConverterExp = "J=SON格式，如API密钥、商户ID等")
    private String config;

    /**
     * 状态：0-禁用，1-启用
     */
    @Excel(name = "状态：0-禁用，1-启用")
    private Long status;
}