package com.ruoyi.drama.domain;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.drama.vo.MovieVO;
import com.ruoyi.common.drama.vo.PushMovieVideoVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import org.springframework.util.CollectionUtils;

/**
 * 短剧对象 drama_room_movie
 *
 * <AUTHOR>
 * @date 2025-07-04
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(description = "短剧对象")
public class DramaRoomMovie extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 短剧ID */
    private Long id;

    /** 短剧标题 */
    @ApiModelProperty("短剧标题")
    @Excel(name = "短剧标题")
    private String title;

    @ApiModelProperty("短剧名称")
    @Excel(name = "短剧名称")
    private String name;

    /** 短剧简介 */
    @ApiModelProperty("短剧简介")
    @Excel(name = "短剧简介")
    private String description;

    /** 封面图片 */
    @ApiModelProperty("封面图片")
    @Excel(name = "封面图片")
    private String coverImage;

    /** 导演 */
    @ApiModelProperty("导演")
    @Excel(name = "导演")
    private String director;

    /** 演员 */
    @ApiModelProperty("演员")
    @Excel(name = "演员")
    private String actors;

    /** 发布日期 */
    @ApiModelProperty("发布日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "发布日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date releaseDate;

    /** 总集数 */
    @ApiModelProperty("总集数")
    @Excel(name = "总集数")
    private Long totalVideos;

    /** 状态（0正常 1停用） */
    @ApiModelProperty("状态（0正常 1停用）")
    @Excel(name = "状态", readConverterExp = "0=正常,1=停用")
    private String status;

    @ApiModelProperty("地区ID")
    private Integer areaId;

    @ApiModelProperty("语音ID")
    private Integer audioId;

    /** 播放次数 */
    @ApiModelProperty(value = "播放次数，后台统计不填")
    private Long playCount;

    /** 点赞次数 */
    @ApiModelProperty(value = "点赞次数，后台统计不填")
    private Long likeCount;

    @ApiModelProperty(value = "收藏次数，后台统计不填")
    private Long collectCount;

    @ApiModelProperty(value = "搜索次数，后台统计不填")
    private Long searchCount;

    @ApiModelProperty(value = "已翻译的语言")
    private List<DramaRoomLanguageConfig> languageConfigList;

    public static List<PushMovieVideoVO> toPushList(List<DramaRoomMovie> movies) {
        List<PushMovieVideoVO> list = new ArrayList<>();
        for (DramaRoomMovie movie : movies) {
            PushMovieVideoVO pushMovieVideoVO = new PushMovieVideoVO();
            pushMovieVideoVO.setId(movie.getId());
            pushMovieVideoVO.setTitle(movie.getTitle());
            pushMovieVideoVO.setName(movie.getName());
            pushMovieVideoVO.setDescription(movie.getDescription());
            pushMovieVideoVO.setTotalVideos(movie.getTotalVideos());
            pushMovieVideoVO.setLikeCount(movie.getLikeCount());
            pushMovieVideoVO.setCollectCount(movie.getCollectCount());
            list.add(pushMovieVideoVO);
        }
        return list;
    }


    public MovieVO toVO() {
        // todo 改为语言
//        ResourceLanguageConvert.convert(this, "zh");
        MovieVO vo = new MovieVO();
        vo.setId(this.getId());
        vo.setTitle(this.getTitle());
        vo.setName(this.getName());
        vo.setCoverImage(this.getCoverImage());
        vo.setReleaseDate(this.getReleaseDate());
        vo.setTotalVideos(this.getTotalVideos());
        vo.setPlayCount(this.getPlayCount());
        vo.setLikeCount(this.getLikeCount());
        vo.setCollectCount(this.getCollectCount());
        vo.setSearchCount(this.getSearchCount());
        vo.setRemark(this.getRemark());
        vo.setCreateTime(this.getCreateTime());
        vo.setUpdateTime(this.getUpdateTime());
        return vo;
    }

    public static List<MovieVO> toVOList(List<DramaRoomMovie> list) {
        if(CollectionUtils.isEmpty(list)){
            return Collections.emptyList();
        }
        List<MovieVO> vos = new ArrayList<>();
        for (DramaRoomMovie movie : list) {
            vos.add(movie.toVO());
        }
        return vos;
    }

    public String get(String key) {
        try {
            // 获取当前类的 Field 对象
            Field field = this.getClass().getDeclaredField(key);
            // 设置可访问（即使是 private 属性）
            field.setAccessible(true);
            // 获取属性值
            Object value = field.get(this);
            // 如果值为 null，返回空字符串，否则返回字符串形式
            return value == null ? "" : value.toString();
        } catch (NoSuchFieldException | IllegalAccessException e) {
            // 如果属性不存在或访问失败，返回空字符串或抛出异常
            return "";
            // 或者抛出异常：
            // throw new IllegalArgumentException("Invalid field name: " + key, e);
        }
    }
}
