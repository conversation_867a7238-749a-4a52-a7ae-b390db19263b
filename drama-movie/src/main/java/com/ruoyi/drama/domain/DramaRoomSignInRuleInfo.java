package com.ruoyi.drama.domain;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 签到规则明细对象 drama_room_sign_in_rule_info
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class DramaRoomSignInRuleInfo extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    private Long id;

    /** 签到规则id */
    @Excel(name = "签到规则id")
    private Long signInRuleId;

    /** 签到天数：第几天 */
    @Excel(name = "签到天数：第几天")
    private Long days;

    /** 金币数量 */
    @Excel(name = "金币数量")
    private Long num;

    /** 状态（1启用 0停用） */
    @Excel(name = "状态", readConverterExp = "1=启用,0=停用")
    private Integer status;

}
