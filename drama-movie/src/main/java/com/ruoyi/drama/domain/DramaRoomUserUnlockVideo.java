package com.ruoyi.drama.domain;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.EqualsAndHashCode;

/**
 * 用户解锁视频记录对象 drama_room_user_unlock_video
 *
 * <AUTHOR>
 * @date 2025-08-07
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class DramaRoomUserUnlockVideo extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** ID (分布式雪花算法ID) */
    @ApiModelProperty("ID (分布式雪花算法ID)")
    private Long id;

    /** 用户ID */
    @ApiModelProperty("用户ID")
    @Excel(name = "用户ID")
    private Long userId;

    /** 影片ID */
    @ApiModelProperty("影片ID")
    @Excel(name = "影片ID")
    private Long movieId;

    /** 视频ID */
    @ApiModelProperty("视频ID")
    @Excel(name = "视频ID")
    private Long videoId;

    /** 解锁类型 */
    @ApiModelProperty("解锁类型")
    @Excel(name = "解锁类型 (0-金币解锁，1-看广告解锁)")
    private Integer unlockType;

    /** 解锁消耗金币数 */
    @ApiModelProperty("解锁消耗金币数")
    @Excel(name = "解锁消耗金币数")
    private Integer costCoin;

    @Override
    public String toString() {
        return "DramaRoomUserUnlockVideo{" +
                "id=" + id +
                ", userId=" + userId +
                ", movieId=" + movieId +
                ", videoId=" + videoId +
                "} " + super.toString();
    }
}