package com.ruoyi.drama.domain;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 订阅模板：定义不同周期和等级下的金币订阅计划对象 drama_room_pay_template_subscription
 *
 * <AUTHOR>
 * @date 2025-09-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("DramaRoomPayTemplateSubscription对象")
public class DramaRoomPayTemplateSubscription extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     *
     */
    @ApiModelProperty("")
    private String id;

    /**
     * 订阅周期类型:1周，2月，3年
     */
    @ApiModelProperty("订阅周期类型:1周，2月，3年")
    @Excel(name = "订阅周期类型:1周，2月，3年")
    private String periodType;

    /**
     * 订阅等级，如1铜、2银、3金
     */
    @ApiModelProperty("订阅等级，如1铜、2银、3金")
    @Excel(name = "订阅等级，如1铜、2银、3金")
    private String level;

    /**
     * 该订阅的总有效天数，如周=7，月=30，年=365
     */
    @ApiModelProperty("该订阅的总有效天数，如周=7，月=30，年=365")
    @Excel(name = "该订阅的总有效天数，如周=7，月=30，年=365")
    private String durationDays;

    /**
     * 用户每天可领取的金币数
     */
    @ApiModelProperty("用户每天可领取的金币数")
    @Excel(name = "用户每天可领取的金币数")
    private String dailyCoins;

    /**
     * 该订阅周期内总共可获得的金币数
     */
    @ApiModelProperty("该订阅周期内总共可获得的金币数")
    @Excel(name = "该订阅周期内总共可获得的金币数")
    private String totalCoins;

    /**
     * 每日赠送用户金币数
     */
    @ApiModelProperty("每日赠送用户金币数")
    @Excel(name = "每日赠送用户金币数")
    private Long dailySendCoins;

    /**
     * 该订阅周期内总共赠送金币数
     */
    @ApiModelProperty("该订阅周期内总共赠送金币数")
    @Excel(name = "该订阅周期内总共赠送金币数")
    private Long totalSendCoins;

    /**
     * 前端展示名称，如“周卡（铜级）金币包”
     */
    @ApiModelProperty("前端展示名称，如“周卡（铜级）金币包”")
    @Excel(name = "前端展示名称，如“周卡", readConverterExp = "铜=级")
    private String displayName;

    /**
     * 订阅详情描述，如领取规则等
     */
    @ApiModelProperty("订阅详情描述，如领取规则等")
    @Excel(name = "订阅详情描述，如领取规则等")
    private String description;

    /**
     * 价格
     */
    @ApiModelProperty("价格")
    @Excel(name = "价格")
    private BigDecimal price;

    /**
     * 1=启用，0=禁用
     */
    @ApiModelProperty("1=启用，0=禁用")
    @Excel(name = "1=启用，0=禁用")
    private Integer isActive;

    /**
     * 排序，数字越小越靠前
     */
    @ApiModelProperty("排序，数字越小越靠前")
    @Excel(name = "排序，数字越小越靠前")
    private String sortOrder;

    /**
     * 苹果内购订阅id
     */
    @ApiModelProperty("苹果内购订阅id")
    @Excel(name = "苹果内购订阅id")
    private String appleProductId;

    /**
     * 谷歌内购订阅id
     */
    @ApiModelProperty("谷歌内购订阅id")
    @Excel(name = "谷歌内购订阅id")
    private String googleProductId;
}