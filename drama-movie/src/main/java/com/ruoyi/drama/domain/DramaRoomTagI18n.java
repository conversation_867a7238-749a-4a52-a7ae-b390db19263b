package com.ruoyi.drama.domain;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 标签多语言对象 drama_room_tag_i18n
 * 
 * <AUTHOR>
 * @date 2025-08-05
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class DramaRoomTagI18n extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** ID */
    private Long id;

    /** 标签ID */
    @Excel(name = "标签ID")
    private Long tagId;

    /** 标签名称翻译 */
    @Excel(name = "标签名称翻译")
    private String tagName;

    @Excel(name = "多语言标识")
    private String languageCode;
}
