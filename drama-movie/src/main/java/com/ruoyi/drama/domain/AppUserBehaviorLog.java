package com.ruoyi.drama.domain;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 用户行为日志对象 app_user_behavior_log
 *
 * <AUTHOR>
 * @date 2025-07-29
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class AppUserBehaviorLog extends BaseEntity
{
    /** $column.columnComment */
    private Long id;

    /** 用户id */
    private Long userId;

    /** 行为类型 */
    private String behaviorType;

    /** 用户行为进行前页面停留时间(毫秒) */
    private Long stayTime;

    /** 用户访问的页面 */
    private String pageUrl;

    /** 用户设备类型 */
    private String deviceType;

    /** 用户设备操作系统 */
    private String deviceOs;

    /** 用户设备厂商 */
    private String deviceBrand;

    /** 用户其他操作信息 */
    private String actionDetail;

    /** 行为描述 */
    private String behaviorDesc;

    /** 行为发生时间 */
    private Date behaviorTime;
}
