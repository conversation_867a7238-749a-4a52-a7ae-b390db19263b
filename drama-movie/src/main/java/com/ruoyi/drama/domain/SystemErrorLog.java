package com.ruoyi.drama.domain;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 系统错误日志对象 system_error_log
 *
 * <AUTHOR>
 * @date 2025-08-06
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class SystemErrorLog extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 请求跟踪ID */
    @Excel(name = "请求跟踪ID")
    private String traceId;

    /** 错误码 */
    @Excel(name = "错误码")
    @ApiModelProperty(value = "错误码")
    private String errorCode;

    /** 错误名称 */
    @Excel(name = "错误名称")
    @ApiModelProperty(value = "错误名称")
    private String errorName;

    /** 错误详细信息 */
    @Excel(name = "错误详细信息")
    @ApiModelProperty(value = "错误详细信息")
    private String errorMessage;

    /** 错误堆栈 */
    @Excel(name = "错误堆栈")
    @ApiModelProperty(value = "错误堆栈,具体报错代码")
    private String errorStack;

    /** 请求URL */
    @Excel(name = "请求URL")
    private String requestUrl;

    /** 请求方法 */
    @Excel(name = "请求方法")
    private String requestMethod;

    /** 请求参数 */
    @Excel(name = "请求参数")
    private String requestParams;

    /** 客户端IP */
    @Excel(name = "客户端IP")
    @ApiModelProperty(value = "客户端IP")
    private String ipAddress;

    /** 设备信息 */
    @Excel(name = "设备信息")
    @ApiModelProperty(value = "设备信息")
    private String deviceInfo;

    /** 用户代理 */
    @Excel(name = "用户代理")
    private String userAgent;

    /** 用户ID(未登录为0) */
    @Excel(name = "用户ID(未登录为0)")
    @ApiModelProperty(value = "用户ID")
    private Long userId;

    /** 处理状态(0-未处理,1-已处理) */
    @Excel(name = "处理状态(0-未处理,1-已处理)")
    private Long status;

    /** 记录系统错误类型 **/
    @Excel(name = "系统错误类型")
    @ApiModelProperty(value = "系统错误类型 ｜ MOBILE | DRAMA | SEM")
    private String systemType;

}