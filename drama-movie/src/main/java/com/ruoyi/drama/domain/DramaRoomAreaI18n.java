package com.ruoyi.drama.domain;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 短剧地区多语言对象 drama_room_area_i18n
 * 
 * <AUTHOR>
 * @date 2025-08-05
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class DramaRoomAreaI18n extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** id */
    private Long id;

    /** 地区ID */
    @Excel(name = "地区ID")
    private Long areaId;

    /** 地区翻译 */
    @Excel(name = "地区翻译")
    private String area;

    /** 多语言标识 */
    @Excel(name = "多语言标识")
    private String languageCode;
}
