package com.ruoyi.drama.domain;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.drama.vo.UserHistoryVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 【请填写功能名称】对象 drama_room_user_history
 *
 * <AUTHOR>
 * @date 2025-07-04
 */
@ApiModel(description = "用户观看历史记录表")
@Data
public class DramaRoomUserHistory extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 用户id */
    @ApiModelProperty("用户id")
    @Excel(name = "用户id")
    private Long userId;

    /** 短剧Id */
    @ApiModelProperty("短剧Id")
    @Excel(name = "短剧Id")
    private Long movieId;

    /** 剧集id */
    @ApiModelProperty("剧集id")
    @Excel(name = "剧集id")
    private Long videoId;

    @ApiModelProperty("视频剧集")
    @Excel(name = "视频剧集")
    private Integer videoNum;

    /** 浏览时间 */
    @ApiModelProperty("浏览时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "浏览时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date viewDate;

    /** 浏览进度 */
    @ApiModelProperty("浏览进度")
    @Excel(name = "浏览进度")
    private Long progress;

    public UserHistoryVO convert2UserHistoryVO() {
        UserHistoryVO userHistoryVO = new UserHistoryVO();
        userHistoryVO.setId(this.id);
        userHistoryVO.setMovieId(this.movieId);
        userHistoryVO.setVideoId(this.videoId);
        userHistoryVO.setViewDate(this.viewDate);
        userHistoryVO.setProgress(this.progress);
        return userHistoryVO;
    }

    public static List<UserHistoryVO> convert2UserHistoryVOList(List<DramaRoomUserHistory> dramaRoomUserHistories) {
        return dramaRoomUserHistories.stream().map(DramaRoomUserHistory::convert2UserHistoryVO).collect(Collectors.toList());
    }

}
