package com.ruoyi.drama.domain;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * App页面配置对象 app_web_page_config
 * 
 * <AUTHOR>
 * @date 2025-08-05
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class AppWebPageConfig extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 页面标识键(如:contact_us,terms_of_service,privacy_policy,dmca) */
    @Excel(name = "页面标识键(如:contact_us,terms_of_service,privacy_policy,dmca)")
    private String pageKey;

    /** 页面中文名称 */
    @Excel(name = "页面中文名称")
    private String pageCname;

    /** 页面英文名称 */
    @Excel(name = "页面英文名称")
    private String pageEname;

    /** 页面链接 */
    @Excel(name = "页面链接")
    private String pageLinkUrl;

    /** 是否激活(0:否，1:是) */
    @Excel(name = "是否激活(0:否，1:是)")
    private Integer isActive;


}
