package com.ruoyi.drama.domain;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.lang.reflect.Field;

/**
 * 短剧翻译对象 drama_room_movie_i18n
 *
 * <AUTHOR>
 * @date 2025-08-05
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class DramaRoomMovieI18n extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 短剧ID
     */
    @Excel(name = "短剧ID")
    private Long movieId;

    /**
     * 多语言编码
     */
    @Excel(name = "多语言编码")
    private String languageCode;

    /**
     * 短剧标题
     */
    @Excel(name = "短剧标题")
    private String title;

    /**
     * 短剧简介
     */
    @Excel(name = "短剧简介")
    private String description;

    /**
     * 封面图片
     */
    @Excel(name = "封面图片")
    private String coverImage;

    /**
     * 导演
     */
    @Excel(name = "导演")
    private String director;

    /**
     * 演员
     */
    @Excel(name = "演员")
    private String actors;

    /**
     * 审核状态 0-未通过 1-已通过
     */
    @Excel(name = "审核状态 0-未通过 1-已通过")
    private String status;

    public void set(String key, String translate) {
        try {
            Field field = this.getClass().getDeclaredField(key);
            field.setAccessible(true);

            Class<?> type = field.getType();
            Object value = translate; // 默认按String处理

            if (translate != null && !translate.isEmpty()) {
                if (type == Long.class || type == long.class) {
                    value = Long.parseLong(translate);
                } else if (type == Integer.class || type == int.class) {
                    value = Integer.parseInt(translate);
                } else if (type == Boolean.class || type == boolean.class) {
                    value = Boolean.parseBoolean(translate);
                } else if (type == Double.class || type == double.class) {
                    value = Double.parseDouble(translate);
                }
                // 可以继续添加其他类型的支持
            } else {
                // 处理空字符串的情况
                if (type == String.class) {
                    value = null;
                } else if (type.isPrimitive()) {
                    throw new IllegalArgumentException("Cannot set empty string to primitive field: " + key);
                }
            }

            field.set(this, value);
        } catch (Exception e) {
            throw new RuntimeException("Failed to set field [" + key + "] with value [" + translate + "]", e);
        }
    }
}
