package com.ruoyi.drama.domain;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

import java.util.List;

/**
 * 运营用户关联app，短剧对象 drama_room_sys_user_app_movie
 *
 * <AUTHOR>
 * @date 2025-08-11
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class DramaRoomSysUserAppMovie extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    private Long id;

    /** 用户id */
    @Excel(name = "用户id")
    private Long sysUserId;

    /** 0.关联app，1.关联短剧,2.关联组长组员 */
    @Excel(name = "0.关联app，1.关联短剧,2.关联组长组员")
    private String type;

    /** appid */
    @Excel(name = "appid")
    private Long appId;

    /** 短剧id */
    @Excel(name = "短剧id")
    private Long movieId;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private Long childId;

    private List<DramaRoomSysUserAppMovie> shortSysUserAppMovieList;

}