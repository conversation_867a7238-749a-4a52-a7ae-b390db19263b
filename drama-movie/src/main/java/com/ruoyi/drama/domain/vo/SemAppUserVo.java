package com.ruoyi.drama.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.entity.AppUser;
import com.ruoyi.common.core.domain.entity.AppUserDevice;
import com.ruoyi.drama.domain.AppUserCoin;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
@ApiModel(description = "Sem用户Vo对象")
public class SemAppUserVo {


    /** 用户ID */
    @ApiModelProperty(value = "用户ID", example = "1001")
    @Excel(name = "用户ID", type = Excel.Type.EXPORT, cellType = Excel.ColumnType.NUMERIC, prompt = "用户编号")
    private Long id;

    /** 用户昵称 */
    @ApiModelProperty(value = "用户昵称", example = "张三")
    @Excel(name = "用户昵称")
    private String nickname;

    /** 用户邮箱 */
    @ApiModelProperty(value = "用户邮箱", example = "<EMAIL>")
    @Excel(name = "用户邮箱")
    private String email;

    /** 账户类型（GUEST=游客，THIRD_PARTY=第三方账户类型） */
    @ApiModelProperty(value = "账户类型", allowableValues = "GUEST,THIRD_PARTY", notes = "GUEST=游客，THIRD_PARTY=第三方账户类型")
    @Excel(name = "账户类型", readConverterExp = "GUEST=游客,THIRD_PARTY=第三方账户")
    private String accountType;

    /** 账户状态（0=正常，1=停用） */
    @ApiModelProperty(value = "账户状态",allowableValues = "0,1", notes = "0=正常，1=停用")
    @Excel(name = "账户状态", readConverterExp = "0=正常,1=停用")
    private String status;


    /** 认证类型（GOOGLE | APPLE | FACEBOOK） */
    @ApiModelProperty(value = "认证类型",allowableValues = "GOOGLE,APPLE,FACEBOOK")
    @Excel(name = "认证类型")
    private String authType;


    /** 最后登录IP地址 */
    @ApiModelProperty(value = "最后登录IP地址", example = "***********")
    @Excel(name = "最后登录IP")
    private String lastLoginIp;

    /** 最后登录时间 */
    @ApiModelProperty(value = "最后登录时间", example = "2024-01-01 12:00:00")
    @Excel(name = "最后登录时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date lastLoginDate;

    /**
     * 是否为付费用户
     */
    @ApiModelProperty(value = "是否为付费用户", example = "true")
    private Boolean isPaid;

    /**
     * 用户来源
     */
    @ApiModelProperty(value = "用户来源", example = "APP")
    @Excel(name = "用户来源")
    private String source;

    /**
     * 推广链接ID
     */
    @ApiModelProperty(value = "推广链接ID", example = "123456")
    @Excel(name = "推广链接ID")
    private Long semLinkId;

    /**
     * 广告ID
     */
    @ApiModelProperty(value = "广告ID", example = "123456")
    @Excel(name = "广告ID")
    private String adId;

    /**
     * 用户当前金币余额 (金币表查询)
     */
    @ApiModelProperty(value = "用户当前金币余额", example = "10000")
    @Excel(name = "用户当前金币余额")
    private Long coinBalance;

    /**
     * 是否开通订阅 (订阅表查询)
     */
    @ApiModelProperty(value = "是否开通订阅", example = "true")
    @Excel(name = "是否开通订阅")
    private Boolean isSubscribe;

    /**
     * 当前订阅类型
     */
    @ApiModelProperty(value = "当前订阅类型", example = "MONTHLY")
    @Excel(name = "当前订阅类型")
    private String subscribeType;

    /**
     * 用户支付方式
     */
    @ApiModelProperty(value = "用户支付方式", example = "ALIPAY")
    @Excel(name = "用户支付方式")
    private String paymentType;

    /**
     * 用户设备信息
     */
    @ApiModelProperty(value = "用户设备信息")
    private List<AppUserDevice> appUserDevices;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    /**
     * 用户信息初始化
     * @param item
     * @param appUserCoin
     */
    public void init(AppUser item, AppUserCoin appUserCoin, List<AppUserDevice> appUserDevices) {
        this.id = item.getId();
        this.nickname = item.getNickname();
        this.email = item.getEmail();
        this.accountType = item.getAccountType();
        this.status = item.getStatus();
        this.authType = item.getAuthType();
        this.lastLoginIp = item.getLastLoginIp();
        this.lastLoginDate = item.getLastLoginDate();
        this.isPaid = item.getIsPaid();
        this.source = item.getSource();
        this.semLinkId = item.getSemLinkId();
        this.adId = item.getAdId();
        this.coinBalance = appUserCoin.getCoins();
        this.appUserDevices = appUserDevices;
        this.createTime = item.getCreateTime();
        this.updateTime = item.getUpdateTime();
        // TODO
        this.isSubscribe = false;
        this.subscribeType = "";
        this.paymentType = "";
    }
}
