package com.ruoyi.drama.domain;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 短剧扣费规则对象 drama_room_movie_coin_rule
 * 
 * <AUTHOR>
 * @date 2025-08-05
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class DramaRoomMovieCoinRule extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 规则名字 */
    @Excel(name = "规则名字")
    private String name;

    /** 影片ID */
    @Excel(name = "影片ID")
    private Long movieId;

    /** 弹出集数 */
    @Excel(name = "弹出集数")
    private Long popNum;
}
