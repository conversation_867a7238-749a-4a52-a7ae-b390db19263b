package com.ruoyi.drama.domain;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 视频渠道扣金币配置对象 drama_room_video_channel_coin
 * 
 * <AUTHOR>
 * @date 2025-08-05
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class DramaRoomVideoChannelCoin extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 单集金币（统一在这里进行配置） */
    @Excel(name = "单集金币", readConverterExp = "统一在这里进行配置")
    private Integer coin;

    /** 扣金币规则Id */
    @Excel(name = "扣金币规则Id")
    private Long coinRuleId;

    /** 影片ID */
    @Excel(name = "影片ID")
    private Long movieId;

    /** 视频ID */
    @Excel(name = "视频ID")
    private Long videoId;

    /** 第几集 */
    @Excel(name = "第几集")
    private Integer num;

    /**  状态 0待审核 1已发布 */
    @Excel(name = " 状态 0待审核 1已发布")
    private String state;

}
