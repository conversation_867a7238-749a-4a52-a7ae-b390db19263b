package com.ruoyi.drama.domain;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 金币记录对象 app_user_coin_log
 *
 * <AUTHOR>
 * @date 2025-07-29
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class AppUserCoinLog extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    private Long id;

    /** 0.充值1.赠送2.签到3.消费 */
    @Excel(name = "0.充值1.赠送2.签到3.解锁视频")
    private Long type;

    /** 交易类型 **/
    @Excel(name = "交易类型 0:入账 1:出账")
    private Integer tradeType;

    /** 入账类型 **/
    @Excel(name = "入账类型 0:充值 1:奖励")
    private Integer incomeType;

    /** 用户id */
    @Excel(name = "用户id")
    private Long userId;

    /** 变动金币数量 */
    @Excel(name = "变动金币数量")
    private Long coins;

    /** 变动前金币数量 */
    @Excel(name = "变动前金币数量")
    private Long beforeCoins;

    /** 变动后金币数量 */
    @Excel(name = "变动后金币数量")
    private Long afterCoins;

    /** 说明 */
    @Excel(name = "说明")
    private String content;

    /** 状态（1启用 0停用） */
    @Excel(name = "状态", readConverterExp = "1=启用,0=停用")
    private String status;

    @Excel(name = "来源id")
    private Long sourceId;

}
