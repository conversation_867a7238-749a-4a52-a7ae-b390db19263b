package com.ruoyi.drama.domain;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * vip管理对象 drama_room_vip
 *
 * <AUTHOR>
 * @date 2025-08-11
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("DramaRoomVip对象")
public class DramaRoomVip extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * $column.columnComment
     */
    @ApiModelProperty("ID")
    private Long id;

    /**
     * 充值模板
     */
    @ApiModelProperty("充值模板")
    @Excel(name = "充值模板")
    private Long payTemplateId;

    /**
     * 充值金额模板
     */
    @ApiModelProperty("充值金额模板")
    @Excel(name = "充值金额模板")
    private Long payTemplateAmountId;

    /**
     * 订阅金额模板
     */
    @ApiModelProperty("订阅金额模板")
    @Excel(name = "订阅金额模板")
    private Long payTemplateSubscriptionId;

    /**
     * vip名字
     */
    @ApiModelProperty("vip名字")
    @Excel(name = "vip名字")
    private String name;

    /**
     * 赠送金币
     */
    @ApiModelProperty("赠送金币")
    @Excel(name = "赠送金币")
    private Long sendCoin;

    /**
     * 类型 0-充值 1-订阅
     */
    @ApiModelProperty("类型 0-充值 1-订阅")
    @Excel(name = "类型 0-充值 1-订阅")
    private String payType;

    /**
     * 订阅类型
     */
    @ApiModelProperty("订阅类型")
    @Excel(name = "订阅类型")
    private String subscriptionType;

    /**
     * 排序
     */
    @ApiModelProperty("排序")
    @Excel(name = "排序")
    private Long sort;

    /**
     * 状态（0正常 1停用）
     */
    @ApiModelProperty("状态（0正常 1停用）")
    @Excel(name = "状态", readConverterExp = "0=正常,1=停用")
    private String status;

    /**
     * 支付面板
     */
    @ApiModelProperty("支付面板")
    @Excel(name = "支付面板")
    private String payPanel;

    /**
     * 推荐标记:0-否 1-是
     */
    @ApiModelProperty("推荐标记:0-否 1-是")
    @Excel(name = "推荐标记:0-否 1-是")
    private Long defaultRecommend;


    @Excel(name = "充值模板")
    @ApiModelProperty("充值模板名称")
    private String payTemplateName;

    @Excel(name = "价格")
    @ApiModelProperty("价格")
    private BigDecimal price;

    @Excel(name = "金币")
    @ApiModelProperty("金币")
    private Integer coin;
}