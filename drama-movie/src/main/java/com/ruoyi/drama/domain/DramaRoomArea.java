package com.ruoyi.drama.domain;

import com.ruoyi.common.drama.vo.MovieAreasVO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * 短剧地区对象 drama_room_area
 *
 * <AUTHOR>
 * @date 2025-07-28
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class DramaRoomArea extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** id */
    private Long id;

    /** 地区 */
    @Excel(name = "地区")
    private String area;

    /** 多语言列表 */
    private List<DramaRoomAreaI18n> i18nList;

    public static List<MovieAreasVO> toVOList(List<DramaRoomArea> list) {
        if(CollectionUtils.isEmpty(list)){
            return Collections.emptyList();
        }
        List<MovieAreasVO> vos = new ArrayList<>();
        for (DramaRoomArea area : list) {
            vos.add(area.toVO());
        }
        return vos;
    }

    private MovieAreasVO toVO() {
        return new MovieAreasVO(this.getId(), this.getArea());
    }
}
