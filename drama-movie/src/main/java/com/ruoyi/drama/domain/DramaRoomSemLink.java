package com.ruoyi.drama.domain;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 推广链接对象 drama_room_sem_link
 *
 * <AUTHOR>
 * @date 2025-08-11
 */
@ApiModel("DramaRoomSemLink对象")
@EqualsAndHashCode(callSuper = true)
@Data
public class DramaRoomSemLink extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ApiModelProperty("主键ID")
    private Long id;

    /**
     * 平台选择
     */
    @ApiModelProperty("平台选择")
    @Excel(name = "平台选择")
    private String platform;

    /**
     * 名称
     */
    @ApiModelProperty("名称")
    @Excel(name = "名称")
    private String name;

    /**
     * 投放目标
     */
    @ApiModelProperty("投放目标")
    @Excel(name = "投放目标")
    private String target;

    /**
     * 回传形式
     */
    @ApiModelProperty("回传形式")
    @Excel(name = "回传形式")
    private String callbackType;

    /**
     * 像素代码
     */
    @ApiModelProperty("像素代码")
    @Excel(name = "像素代码")
    private String pixelCode;

    /**
     * 落地页模板
     */
    @ApiModelProperty("落地页模板")
    @Excel(name = "落地页模板")
    private String landingTemplate;

    /**
     * 影片ID
     */
    @ApiModelProperty("影片ID")
    @Excel(name = "影片ID")
    private Long movieId;

    /**
     * 充值模板
     */
    @ApiModelProperty("充值模板")
    @Excel(name = "充值模板")
    private Long payTemplateId;

    /**
     * 落地页访问链接
     */
    @ApiModelProperty("落地页访问链接")
    @Excel(name = "落地页访问链接")
    private String link;

    /**
     * 投放系统
     */
    @ApiModelProperty("投放系统")
    @Excel(name = "投放系统")
    private String system;

    /**
     * 落地页地址
     */
    @ApiModelProperty("落地页地址")
    @Excel(name = "落地页地址")
    private String url;

    /**
     * 视频渠道扣金币配置
     */
    @ApiModelProperty("视频渠道扣金币配置")
    @Excel(name = "视频渠道扣金币配置")
    private Long videoCoinPlanId;

    /**
     * 落地页参数
     */
    @ApiModelProperty("落地页参数")
    @Excel(name = "落地页参数")
    private String pdata;

    /**
     * 支付样式id
     */
    @ApiModelProperty("支付样式id")
    @Excel(name = "支付样式id")
    private Long linkAppId;

    /**
     * facebook像素id
     */
    @ApiModelProperty("facebook像素id")
    @Excel(name = "facebook像素id")
    private Long pixelId;

    /**
     * 链接多语言标识
     */
    @ApiModelProperty("链接多语言标识")
    @Excel(name = "链接多语言标识")
    private String language;
}