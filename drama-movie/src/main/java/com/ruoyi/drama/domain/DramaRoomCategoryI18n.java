package com.ruoyi.drama.domain;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 分类多语言对象 drama_room_category_i18n
 * 
 * <AUTHOR>
 * @date 2025-08-05
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class DramaRoomCategoryI18n extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** ID */
    private Long id;

    /** 分类ID */
    @Excel(name = "分类ID")
    private Long categoryId;

    /** 分类名称翻译 */
    @Excel(name = "分类名称翻译")
    private String categoryName;

    /** 多语言标识 */
    @Excel(name = "多语言标识")
    private String languageCode;
}
