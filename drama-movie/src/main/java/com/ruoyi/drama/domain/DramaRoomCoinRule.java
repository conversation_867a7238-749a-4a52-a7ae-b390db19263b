package com.ruoyi.drama.domain;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 充值金币规则对象 drama_room_coin_rule
 *
 * <AUTHOR>
 * @date 2025-07-29
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class DramaRoomCoinRule extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    private Long id;

    /** 0.充值1. */
    @Excel(name = "0.充值1.")
    private Long type;

    /** 规则名称 */
    @Excel(name = "规则名称")
    private String name;

    /** 说明 */
    @Excel(name = "说明")
    private String content;

    /** 充值金额 */
    @Excel(name = "充值金额")
    private BigDecimal rechargeAmount;

    /** 金币数量 */
    @Excel(name = "金币数量")
    private Long Coins;

    /** 赠送金币数量 */
    @Excel(name = "赠送金币数量")
    private Long rewardCoins;

    /** 状态（1启用 0停用） */
    @Excel(name = "状态", readConverterExp = "1=启用,0=停用")
    private String status;

}
