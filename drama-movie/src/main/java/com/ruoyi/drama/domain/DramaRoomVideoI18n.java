package com.ruoyi.drama.domain;

import com.ruoyi.common.drama.vo.DramaRoomVideoI18nVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 剧集多语言对象 drama_room_video_i18n
 * 
 * <AUTHOR>
 * @date 2025-08-05
 */
@ApiModel("剧集多语言对象")
@EqualsAndHashCode(callSuper = true)
@Data
public class DramaRoomVideoI18n extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */

    private Long id;

    /** video id */
    @Excel(name = "video id")
    private Long videoId;

    /** 多语言标识 */
    @Excel(name = "多语言标识")
    private String languageCode;

    /** 字幕url */
    @Excel(name = "字幕url")
    private String subtitleUrl;

    /** 翻译状态：0未审核，1通过，2拒绝 */
    @Excel(name = "翻译状态：0未审核，1通过，2拒绝")
    private String status;

    public DramaRoomVideoI18nVO toVO(){
        DramaRoomVideoI18nVO dramaRoomVideoI18nVO = new DramaRoomVideoI18nVO();
        dramaRoomVideoI18nVO.setId(this.getId());
        dramaRoomVideoI18nVO.setVideoId(this.getVideoId());
        dramaRoomVideoI18nVO.setLanguageCode(this.getLanguageCode());
        dramaRoomVideoI18nVO.setSubtitleUrl(this.getSubtitleUrl());
        dramaRoomVideoI18nVO.setStatus(this.getStatus());
        return dramaRoomVideoI18nVO;
    }

    public static List<DramaRoomVideoI18nVO> toVOList(List<DramaRoomVideoI18n> list){
        return list.stream().map(DramaRoomVideoI18n::toVO).collect(Collectors.toList());
    }
}
