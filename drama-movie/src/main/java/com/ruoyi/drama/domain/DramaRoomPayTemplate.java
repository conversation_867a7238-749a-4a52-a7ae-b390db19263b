package com.ruoyi.drama.domain;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 付费模板对象 drama_room_pay_template
 *
 * <AUTHOR>
 * @date 2025-08-13
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("DramaRoomPayTemplate对象")
public class DramaRoomPayTemplate extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ApiModelProperty("主键ID")
    private Long id;

    /**
     * 充值模板名字
     */
    @ApiModelProperty("充值模板名字")
    @Excel(name = "充值模板名字")
    private String name;

    public void setId(Long id) {
        this.id = id;
    }

    public Long getId() {
        return id;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getName() {
        return name;
    }


}