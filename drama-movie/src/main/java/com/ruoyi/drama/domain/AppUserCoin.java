package com.ruoyi.drama.domain;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 用户金币对象 app_user_coin
 *
 * <AUTHOR>
 * @date 2025-07-29
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class AppUserCoin extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    private Long id;

    /** 用户id */
    @Excel(name = "用户id")
    private Long userId;

    /** 金币数量 */
    @Excel(name = "金币数量")
    private Long coins;

    /** 金币数量 */
    @Excel(name = "金币数量")
    private Long rewardCoins;
    /** 状态（1启用 0停用） */
    @Excel(name = "状态", readConverterExp = "1=启用,0=停用")
    private String status;

}
