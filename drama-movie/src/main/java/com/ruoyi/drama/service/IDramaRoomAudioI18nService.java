package com.ruoyi.drama.service;

import java.util.List;

import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.drama.dto.DramaRoomGeneralI18nDTO;
import com.ruoyi.drama.domain.DramaRoomAudioI18n;

/**
 * 音讯多语言Service接口
 * 
 * <AUTHOR>
 * @date 2025-08-05
 */
public interface IDramaRoomAudioI18nService 
{
    /**
     * 查询音讯多语言
     * 
     * @param id 音讯多语言主键
     * @return 音讯多语言
     */
    public DramaRoomAudioI18n selectDramaRoomAudioI18nById(Long id);

    /**
     * 查询音讯多语言列表
     * 
     * @param dramaRoomAudioI18n 音讯多语言
     * @return 音讯多语言集合
     */
    public List<DramaRoomAudioI18n> selectDramaRoomAudioI18nList(DramaRoomAudioI18n dramaRoomAudioI18n);

    /**
     * 新增音讯多语言
     * 
     * @param dramaRoomAudioI18n 音讯多语言
     * @return 结果
     */
    public int insertDramaRoomAudioI18n(DramaRoomAudioI18n dramaRoomAudioI18n);

    /**
     * 修改音讯多语言
     * 
     * @param dramaRoomAudioI18n 音讯多语言
     * @return 结果
     */
    public int updateDramaRoomAudioI18n(DramaRoomAudioI18n dramaRoomAudioI18n);

    /**
     * 批量删除音讯多语言
     * 
     * @param ids 需要删除的音讯多语言主键集合
     * @return 结果
     */
    public int deleteDramaRoomAudioI18nByIds(Long[] ids);

    /**
     * 删除音讯多语言信息
     * 
     * @param id 音讯多语言主键
     * @return 结果
     */
    public int deleteDramaRoomAudioI18nById(Long id);

    /**
     * 批量插入多语言
     * @param i18ns
     */
    void insertDramaRoomAudioI18nList(List<DramaRoomAudioI18n> i18ns);

    /**
     * 单语言翻译
     * @param dto
     */
    AjaxResult translate(DramaRoomGeneralI18nDTO dto);

    /**
     * 获取需要翻译的语种
     * @param id
     */
    AjaxResult listTranslate(Long id);
}
