package com.ruoyi.drama.service.impl;

import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.drama.dto.DramaRoomGeneralI18nDTO;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.Gemini25FlashUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.drama.domain.DramaRoomLanguageConfig;
import com.ruoyi.drama.domain.DramaRoomTag;
import com.ruoyi.drama.domain.DramaRoomTagI18n;
import com.ruoyi.drama.mapper.DramaRoomLanguageConfigMapper;
import com.ruoyi.drama.mapper.DramaRoomTagI18nMapper;
import com.ruoyi.drama.mapper.DramaRoomTagMapper;
import com.ruoyi.drama.service.IDramaRoomTagI18nService;
import io.jsonwebtoken.lang.Collections;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 标签多语言Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-08-05
 */
@Slf4j
@Service
public class DramaRoomTagI18nServiceImpl implements IDramaRoomTagI18nService {
    @Autowired
    private DramaRoomTagI18nMapper dramaRoomTagI18nMapper;

    @Autowired
    private DramaRoomTagMapper dramaRoomTagMapper;

    @Autowired
    private DramaRoomLanguageConfigMapper dramaRoomLanguageConfigMapper;

    /**
     * 查询标签多语言
     *
     * @param id 标签多语言主键
     * @return 标签多语言
     */
    @Override
    public DramaRoomTagI18n selectDramaRoomTagI18nById(Long id) {
        return dramaRoomTagI18nMapper.selectDramaRoomTagI18nById(id);
    }

    /**
     * 查询标签多语言列表
     *
     * @param dramaRoomTagI18n 标签多语言
     * @return 标签多语言
     */
    @Override
    public List<DramaRoomTagI18n> selectDramaRoomTagI18nList(DramaRoomTagI18n dramaRoomTagI18n) {
        return dramaRoomTagI18nMapper.selectDramaRoomTagI18nList(dramaRoomTagI18n);
    }

    /**
     * 新增标签多语言
     *
     * @param dramaRoomTagI18n 标签多语言
     * @return 结果
     */
    @Override
    public int insertDramaRoomTagI18n(DramaRoomTagI18n dramaRoomTagI18n) {
        dramaRoomTagI18n.setCreateTime(DateUtils.getNowDate());
        return dramaRoomTagI18nMapper.insertDramaRoomTagI18n(dramaRoomTagI18n);
    }

    /**
     * 修改标签多语言
     *
     * @param dramaRoomTagI18n 标签多语言
     * @return 结果
     */
    @Override
    public int updateDramaRoomTagI18n(DramaRoomTagI18n dramaRoomTagI18n) {
        dramaRoomTagI18n.setUpdateTime(DateUtils.getNowDate());
        return dramaRoomTagI18nMapper.updateDramaRoomTagI18n(dramaRoomTagI18n);
    }

    /**
     * 批量删除标签多语言
     *
     * @param ids 需要删除的标签多语言主键
     * @return 结果
     */
    @Override
    public int deleteDramaRoomTagI18nByIds(Long[] ids) {
        return dramaRoomTagI18nMapper.deleteDramaRoomTagI18nByIds(ids);
    }

    /**
     * 删除标签多语言信息
     *
     * @param id 标签多语言主键
     * @return 结果
     */
    @Override
    public int deleteDramaRoomTagI18nById(Long id) {
        return dramaRoomTagI18nMapper.deleteDramaRoomTagI18nById(id);
    }

    /**
     * 批量插入标签多语言
     *
     * @param i18ns 需要插入的标签多语言
     */
    @Override
    public void insertDramaRoomTagI18nList(List<DramaRoomTagI18n> i18ns) {
        dramaRoomTagI18nMapper.insertDramaRoomTagI18nList(i18ns);
    }

    /**
     * 单语种翻译
     *
     * @param dto 翻译参数
     * @return 结果
     */
    @Override
    public AjaxResult translate(DramaRoomGeneralI18nDTO dto) {
        Long id = dto.getId();
        Long loginId = dto.getUserId();
        String language = dto.getLanguage();
        String languageCode = dto.getLanguageCode();
        DramaRoomTag dramaRoomTag = dramaRoomTagMapper.selectDramaRoomTagByTagId(id);
        if (dramaRoomTag == null) {
            return AjaxResult.error("标签不存在");
        }
        try {
            String translate = Gemini25FlashUtils.translate(dramaRoomTag.getTagName(), language);
            if (StringUtils.isEmpty(translate)) {
                return AjaxResult.error("翻译异常");
            }
            DramaRoomTagI18n dramaRoomTagI18n = new DramaRoomTagI18n();
            dramaRoomTagI18n.setTagId(id);
            dramaRoomTagI18n.setLanguageCode(languageCode);
            dramaRoomTagI18n.setTagName(translate);
            dramaRoomTagI18n.setCreateTime(DateUtils.getNowDate());
            dramaRoomTagI18n.setCreateBy(String.valueOf(loginId));
            dramaRoomTagI18nMapper.insertDramaRoomTagI18n(dramaRoomTagI18n);
        } catch (IOException e) {
            log.error("【{}】翻译异常：{}", language, e.getMessage());
            return AjaxResult.error("翻译异常");
        }
        return AjaxResult.success("翻译成功");
    }

    @Override
    public AjaxResult listTranslate(Long id) {
        DramaRoomTag dramaRoomTag = dramaRoomTagMapper.selectDramaRoomTagByTagId(id);
        if (dramaRoomTag == null) {
            return AjaxResult.error("标签不存在");
        }
        DramaRoomTagI18n dramaRoomTagI18n = new DramaRoomTagI18n();
        dramaRoomTagI18n.setTagId(id);
        List<DramaRoomTagI18n> dramaRoomTagI18ns = dramaRoomTagI18nMapper.selectDramaRoomTagI18nList(dramaRoomTagI18n);
        List<DramaRoomLanguageConfig> dramaRoomLanguageConfigs = dramaRoomLanguageConfigMapper.selectDramaRoomLanguageConfigList(new DramaRoomLanguageConfig());
        if (Collections.isEmpty(dramaRoomTagI18ns)) {
            return AjaxResult.success(dramaRoomLanguageConfigs);
        }
        if (dramaRoomTagI18ns.size() == dramaRoomLanguageConfigs.size()) {
            return AjaxResult.success(dramaRoomTag.getTagName() + "标签已全部翻译，请刷新列表后检查翻译结果！");
        }
        List<String> existingLanguageCodes = dramaRoomTagI18ns.stream()
                .map(DramaRoomTagI18n::getLanguageCode)
                .collect(Collectors.toList());
        List<DramaRoomLanguageConfig> filteredConfigs = dramaRoomLanguageConfigs.stream()
                .filter(config -> !existingLanguageCodes.contains(config.getCode()))
                .collect(Collectors.toList());
        return AjaxResult.success(filteredConfigs);
    }
}
