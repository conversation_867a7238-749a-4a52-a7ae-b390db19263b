package com.ruoyi.drama.service.impl;

import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.drama.dto.DramaRoomGeneralI18nDTO;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.Gemini25FlashUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.drama.domain.DramaRoomAudio;
import com.ruoyi.drama.domain.DramaRoomAudioI18n;
import com.ruoyi.drama.domain.DramaRoomLanguageConfig;
import com.ruoyi.drama.mapper.DramaRoomAudioI18nMapper;
import com.ruoyi.drama.mapper.DramaRoomAudioMapper;
import com.ruoyi.drama.mapper.DramaRoomLanguageConfigMapper;
import com.ruoyi.drama.service.IDramaRoomAudioI18nService;
import io.jsonwebtoken.lang.Collections;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 音讯多语言Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-08-05
 */
@Slf4j
@Service
public class DramaRoomAudioI18nServiceImpl implements IDramaRoomAudioI18nService {
    @Autowired
    private DramaRoomAudioI18nMapper dramaRoomAudioI18nMapper;

    @Autowired
    private DramaRoomAudioMapper dramaRoomAudioMapper;

    @Autowired
    private DramaRoomLanguageConfigMapper dramaRoomLanguageConfigMapper;

    /**
     * 查询音讯多语言
     *
     * @param id 音讯多语言主键
     * @return 音讯多语言
     */
    @Override
    public DramaRoomAudioI18n selectDramaRoomAudioI18nById(Long id) {
        return dramaRoomAudioI18nMapper.selectDramaRoomAudioI18nById(id);
    }

    /**
     * 查询音讯多语言列表
     *
     * @param dramaRoomAudioI18n 音讯多语言
     * @return 音讯多语言
     */
    @Override
    public List<DramaRoomAudioI18n> selectDramaRoomAudioI18nList(DramaRoomAudioI18n dramaRoomAudioI18n) {
        return dramaRoomAudioI18nMapper.selectDramaRoomAudioI18nList(dramaRoomAudioI18n);
    }

    /**
     * 新增音讯多语言
     *
     * @param dramaRoomAudioI18n 音讯多语言
     * @return 结果
     */
    @Override
    public int insertDramaRoomAudioI18n(DramaRoomAudioI18n dramaRoomAudioI18n) {
        dramaRoomAudioI18n.setCreateTime(DateUtils.getNowDate());
        return dramaRoomAudioI18nMapper.insertDramaRoomAudioI18n(dramaRoomAudioI18n);
    }

    /**
     * 修改音讯多语言
     *
     * @param dramaRoomAudioI18n 音讯多语言
     * @return 结果
     */
    @Override
    public int updateDramaRoomAudioI18n(DramaRoomAudioI18n dramaRoomAudioI18n) {
        dramaRoomAudioI18n.setUpdateTime(DateUtils.getNowDate());
        return dramaRoomAudioI18nMapper.updateDramaRoomAudioI18n(dramaRoomAudioI18n);
    }

    /**
     * 批量删除音讯多语言
     *
     * @param ids 需要删除的音讯多语言主键
     * @return 结果
     */
    @Override
    public int deleteDramaRoomAudioI18nByIds(Long[] ids) {
        return dramaRoomAudioI18nMapper.deleteDramaRoomAudioI18nByIds(ids);
    }

    /**
     * 删除音讯多语言信息
     *
     * @param id 音讯多语言主键
     * @return 结果
     */
    @Override
    public int deleteDramaRoomAudioI18nById(Long id) {
        return dramaRoomAudioI18nMapper.deleteDramaRoomAudioI18nById(id);
    }

    /**
     * 批量新增音讯多语言
     *
     * @param i18ns 音讯多语言
     */
    @Override
    public void insertDramaRoomAudioI18nList(List<DramaRoomAudioI18n> i18ns) {
        dramaRoomAudioI18nMapper.insertDramaRoomAudioI18nList(i18ns);
    }

    /**
     * 单语种翻译
     *
     * @param dto
     * @return
     */
    @Override
    public AjaxResult translate(DramaRoomGeneralI18nDTO dto) {
        Long id = dto.getId();
        Long loginId = dto.getUserId();
        String language = dto.getLanguage();
        String languageCode = dto.getLanguageCode();
        DramaRoomAudio dramaRoomAudio = dramaRoomAudioMapper.selectDramaRoomAudioById(id);
        if (dramaRoomAudio == null) {
            return AjaxResult.error("音讯不存在");
        }
        try {
            String translate = Gemini25FlashUtils.translate(dramaRoomAudio.getAudio(), language);
            if (StringUtils.isEmpty(translate)) {
                return AjaxResult.error("翻译失败");
            }
            DramaRoomAudioI18n dramaRoomAudioI18n = new DramaRoomAudioI18n();
            dramaRoomAudioI18n.setAudioId(id);
            dramaRoomAudioI18n.setAudio(translate);
            dramaRoomAudioI18n.setLanguageCode(languageCode);
            dramaRoomAudioI18n.setCreateTime(DateUtils.getNowDate());
            dramaRoomAudioI18n.setCreateBy(String.valueOf(loginId));
            dramaRoomAudioI18nMapper.insertDramaRoomAudioI18n(dramaRoomAudioI18n);
        } catch (IOException e) {
            log.error("【{}】翻译异常：{}", language, e.getMessage());
            return AjaxResult.error("翻译异常");
        }
        return AjaxResult.success("翻译成功");
    }

    @Override
    public AjaxResult listTranslate(Long id) {
        DramaRoomAudio dramaRoomAudio = dramaRoomAudioMapper.selectDramaRoomAudioById(id);
        if (dramaRoomAudio == null) {
            return AjaxResult.error("音讯不存在");
        }
        DramaRoomAudioI18n dramaRoomAudioI18n = new DramaRoomAudioI18n();
        dramaRoomAudioI18n.setAudioId(id);
        List<DramaRoomAudioI18n> dramaRoomAudioI18ns = dramaRoomAudioI18nMapper.selectDramaRoomAudioI18nList(dramaRoomAudioI18n);
        List<DramaRoomLanguageConfig> dramaRoomLanguageConfigs = dramaRoomLanguageConfigMapper.selectDramaRoomLanguageConfigList(new DramaRoomLanguageConfig());
        if (Collections.isEmpty(dramaRoomAudioI18ns)) {
            return AjaxResult.success(dramaRoomLanguageConfigs);
        }
        if (dramaRoomAudioI18ns.size() == dramaRoomLanguageConfigs.size()) {
            return AjaxResult.success(dramaRoomAudio.getAudio() + "音讯已全部翻译，请刷新列表后检查翻译结果！");
        }
        List<String> existingLanguageCodes = dramaRoomAudioI18ns.stream()
                .map(DramaRoomAudioI18n::getLanguageCode)
                .collect(Collectors.toList());
        List<DramaRoomLanguageConfig> filteredConfigs = dramaRoomLanguageConfigs.stream()
                .filter(config -> !existingLanguageCodes.contains(config.getCode()))
                .collect(Collectors.toList());
        return AjaxResult.success(filteredConfigs);
    }
}
