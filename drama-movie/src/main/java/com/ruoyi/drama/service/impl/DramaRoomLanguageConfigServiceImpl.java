package com.ruoyi.drama.service.impl;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

import cn.hutool.core.collection.CollectionUtil;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.drama.domain.DramaRoomVideo;
import com.ruoyi.drama.domain.DramaRoomVideoI18n;
import com.ruoyi.drama.mapper.DramaRoomVideoI18nMapper;
import com.ruoyi.drama.mapper.DramaRoomVideoMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.drama.mapper.DramaRoomLanguageConfigMapper;
import com.ruoyi.drama.domain.DramaRoomLanguageConfig;
import com.ruoyi.drama.service.IDramaRoomLanguageConfigService;

/**
 * 大包多语言配置Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-08-05
 */
@Service
public class DramaRoomLanguageConfigServiceImpl implements IDramaRoomLanguageConfigService 
{
    @Autowired
    private DramaRoomLanguageConfigMapper dramaRoomLanguageConfigMapper;

    @Autowired
    private DramaRoomVideoI18nMapper dramaRoomVideoI18nMapper;

    @Autowired
    private DramaRoomVideoMapper dramaRoomVideoMapper;

    /**
     * 查询大包多语言配置
     * 
     * @param id 大包多语言配置主键
     * @return 大包多语言配置
     */
    @Override
    public DramaRoomLanguageConfig selectDramaRoomLanguageConfigById(Long id)
    {
        return dramaRoomLanguageConfigMapper.selectDramaRoomLanguageConfigById(id);
    }

    /**
     * 查询大包多语言配置列表
     * 
     * @param dramaRoomLanguageConfig 大包多语言配置
     * @return 大包多语言配置
     */
    @Override
    public List<DramaRoomLanguageConfig> selectDramaRoomLanguageConfigList(DramaRoomLanguageConfig dramaRoomLanguageConfig)
    {
        return dramaRoomLanguageConfigMapper.selectDramaRoomLanguageConfigList(dramaRoomLanguageConfig);
    }

    /**
     * 新增大包多语言配置
     * 
     * @param dramaRoomLanguageConfig 大包多语言配置
     * @return 结果
     */
    @Override
    public int insertDramaRoomLanguageConfig(DramaRoomLanguageConfig dramaRoomLanguageConfig)
    {
        dramaRoomLanguageConfig.setCreateTime(DateUtils.getNowDate());
        return dramaRoomLanguageConfigMapper.insertDramaRoomLanguageConfig(dramaRoomLanguageConfig);
    }

    /**
     * 修改大包多语言配置
     * 
     * @param dramaRoomLanguageConfig 大包多语言配置
     * @return 结果
     */
    @Override
    public int updateDramaRoomLanguageConfig(DramaRoomLanguageConfig dramaRoomLanguageConfig)
    {
        dramaRoomLanguageConfig.setUpdateTime(DateUtils.getNowDate());
        return dramaRoomLanguageConfigMapper.updateDramaRoomLanguageConfig(dramaRoomLanguageConfig);
    }

    /**
     * 批量删除大包多语言配置
     * 
     * @param ids 需要删除的大包多语言配置主键
     * @return 结果
     */
    @Override
    public int deleteDramaRoomLanguageConfigByIds(Long[] ids)
    {
        return dramaRoomLanguageConfigMapper.deleteDramaRoomLanguageConfigByIds(ids);
    }

    /**
     * 删除大包多语言配置信息
     * 
     * @param id 大包多语言配置主键
     * @return 结果
     */
    @Override
    public int deleteDramaRoomLanguageConfigById(Long id)
    {
        return dramaRoomLanguageConfigMapper.deleteDramaRoomLanguageConfigById(id);
    }

    @Override
    public List<DramaRoomLanguageConfig> getAccessLanguageConfig(Long movieId) {
        List<DramaRoomLanguageConfig> langList = dramaRoomLanguageConfigMapper.selectDramaRoomLanguageConfigList(new DramaRoomLanguageConfig());
        List<String> languageCodeCollect = langList.stream().map(DramaRoomLanguageConfig::getCode).collect(Collectors.toList());
        DramaRoomVideo shortVideoQuery = new DramaRoomVideo();
        shortVideoQuery.setMovieId(movieId);
        //原视频
        List<DramaRoomVideo> shortVideoList = dramaRoomVideoMapper.selectDramaRoomVideoList(shortVideoQuery);
        List<Long> videoIdList = shortVideoList.stream().map(DramaRoomVideo::getId).collect(Collectors.toList());
        //查询翻译后的视频字幕表
        List<DramaRoomVideoI18n> shortVideoI18nList = dramaRoomVideoI18nMapper.selectDramaRoomVideoI18nByVideoIds(videoIdList,languageCodeCollect);
        if(CollectionUtil.isNotEmpty(shortVideoI18nList)){
            List<String> collect = shortVideoI18nList.stream().map(DramaRoomVideoI18n::getLanguageCode).distinct().collect(Collectors.toList());
            return langList.stream().filter(item -> collect.contains(item.getCode())).collect(Collectors.toList());
        }
        return Collections.emptyList();
    }
}
