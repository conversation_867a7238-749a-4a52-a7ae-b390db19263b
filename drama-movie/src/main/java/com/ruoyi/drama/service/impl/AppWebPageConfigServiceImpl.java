package com.ruoyi.drama.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.drama.domain.AppWebPageConfig;
import com.ruoyi.drama.mapper.AppWebPageConfigMapper;
import com.ruoyi.drama.service.IAppWebPageConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


/**
 * App页面配置Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-08-05
 */
@Service
public class AppWebPageConfigServiceImpl implements IAppWebPageConfigService
{
    @Autowired
    private AppWebPageConfigMapper appWebPageConfigMapper;

    /**
     * 查询App页面配置
     * 
     * @param id App页面配置主键
     * @return App页面配置
     */
    @Override
    public AppWebPageConfig selectAppWebPageConfigById(Long id)
    {
        return appWebPageConfigMapper.selectAppWebPageConfigById(id);
    }

    /**
     * 查询App页面配置列表
     * 
     * @param appWebPageConfig App页面配置
     * @return App页面配置
     */
    @Override
    public List<AppWebPageConfig> selectAppWebPageConfigList(AppWebPageConfig appWebPageConfig)
    {
        return appWebPageConfigMapper.selectAppWebPageConfigList(appWebPageConfig);
    }

    /**
     * 新增App页面配置
     * 
     * @param appWebPageConfig App页面配置
     * @return 结果
     */
    @Override
    public int insertAppWebPageConfig(AppWebPageConfig appWebPageConfig)
    {
        appWebPageConfig.setCreateTime(DateUtils.getNowDate());
        return appWebPageConfigMapper.insertAppWebPageConfig(appWebPageConfig);
    }

    /**
     * 修改App页面配置
     * 
     * @param appWebPageConfig App页面配置
     * @return 结果
     */
    @Override
    public int updateAppWebPageConfig(AppWebPageConfig appWebPageConfig)
    {
        appWebPageConfig.setUpdateTime(DateUtils.getNowDate());
        return appWebPageConfigMapper.updateAppWebPageConfig(appWebPageConfig);
    }

    /**
     * 批量删除App页面配置
     * 
     * @param ids 需要删除的App页面配置主键
     * @return 结果
     */
    @Override
    public int deleteAppWebPageConfigByIds(Long[] ids)
    {
        return appWebPageConfigMapper.deleteAppWebPageConfigByIds(ids);
    }

    /**
     * 删除App页面配置信息
     * 
     * @param id App页面配置主键
     * @return 结果
     */
    @Override
    public int deleteAppWebPageConfigById(Long id)
    {
        return appWebPageConfigMapper.deleteAppWebPageConfigById(id);
    }
}
