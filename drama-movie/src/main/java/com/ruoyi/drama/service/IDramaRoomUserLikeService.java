package com.ruoyi.drama.service;

import com.ruoyi.drama.domain.DramaRoomUserLike;

import java.util.List;

/**
 * 用户点赞记录Service接口
 *
 * <AUTHOR>
 * @date 2025-07-08
 */
public interface IDramaRoomUserLikeService
{
    /**
     * 查询用户点赞记录
     *
     * @param id 用户点赞记录主键
     * @return 用户点赞记录
     */
    public DramaRoomUserLike selectDramaRoomUserLikeById(Long id);

    /**
     * 查询用户点赞记录列表
     *
     * @param dramaRoomUserLike 用户点赞记录
     * @return 用户点赞记录集合
     */
    public List<DramaRoomUserLike> selectDramaRoomUserLikeList(DramaRoomUserLike dramaRoomUserLike);

    /**
     * 新增用户点赞记录
     *
     * @param dramaRoomUserLike 用户点赞记录
     * @return 结果
     */
    public int insertDramaRoomUserLike(DramaRoomUserLike dramaRoomUserLike);

    /**
     * 修改用户点赞记录
     *
     * @param dramaRoomUserLike 用户点赞记录
     * @return 结果
     */
    public int updateDramaRoomUserLike(DramaRoomUserLike dramaRoomUserLike);

    /**
     * 批量删除用户点赞记录
     *
     * @param ids 需要删除的用户点赞记录主键集合
     * @return 结果
     */
    public int deleteDramaRoomUserLikeByIds(Long[] ids);

    /**
     * 删除用户点赞记录信息
     *
     * @param id 用户点赞记录主键
     * @return 结果
     */
    public int deleteDramaRoomUserLikeById(Long id);

    int likeVideo(Long videoId);
}
