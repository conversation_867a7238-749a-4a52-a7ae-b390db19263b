package com.ruoyi.drama.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.ruoyi.common.drama.dto.DramaRoomUserHistoryDTO;
import com.ruoyi.common.drama.vo.UserCollectVO;
import com.ruoyi.common.drama.vo.UserHistoryVO;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.mobile.AppSecurityUtils;
import com.ruoyi.drama.domain.DramaRoomMovieI18n;
import com.ruoyi.drama.domain.DramaRoomUserHistory;
import com.ruoyi.drama.domain.vo.DramaRoomUserHistoryVO;
import com.ruoyi.drama.mapper.DramaRoomMovieI18nMapper;
import com.ruoyi.drama.mapper.DramaRoomUserCollectMapper;
import com.ruoyi.drama.mapper.DramaRoomUserHistoryMapper;
import com.ruoyi.drama.service.IDramaRoomUserHistoryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 【请填写功能名称】Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-04
 */
@Service
public class DramaRoomUserHistoryServiceImpl implements IDramaRoomUserHistoryService
{
    @Autowired
    private DramaRoomUserHistoryMapper dramaRoomUserHistoryMapper;

    @Autowired
    private DramaRoomMovieI18nMapper dramaRoomMovieI18nMapper;

    @Autowired
    private DramaRoomUserCollectMapper dramaRoomUserCollectMapper;

    /**
     * 查询【请填写功能名称】
     *
     * @param id 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    @Override
    public DramaRoomUserHistory selectDramaRoomUserHistoryById(Long id)
    {
        return dramaRoomUserHistoryMapper.selectDramaRoomUserHistoryById(id);
    }

    /**
     * 查询【请填写功能名称】列表
     *
     * @param dramaRoomUserHistory 【请填写功能名称】
     * @return 【请填写功能名称】
     */
    @Override
    public List<DramaRoomUserHistory> selectDramaRoomUserHistoryList(DramaRoomUserHistory dramaRoomUserHistory)
    {
        return dramaRoomUserHistoryMapper.selectDramaRoomUserHistoryList(dramaRoomUserHistory);
    }

    /**
     * 新增【请填写功能名称】
     *
     * @param dramaRoomUserHistory 【请填写功能名称】
     * @return 结果
     */
    @Override
    public int insertDramaRoomUserHistory(DramaRoomUserHistory dramaRoomUserHistory)
    {
        dramaRoomUserHistory.setCreateTime(DateUtils.getNowDate());
        return dramaRoomUserHistoryMapper.insertDramaRoomUserHistory(dramaRoomUserHistory);
    }

    /**
     * 修改【请填写功能名称】
     *
     * @param dramaRoomUserHistory 【请填写功能名称】
     * @return 结果
     */
    @Override
    public int updateDramaRoomUserHistory(DramaRoomUserHistory dramaRoomUserHistory)
    {
        dramaRoomUserHistory.setUpdateTime(DateUtils.getNowDate());
        return dramaRoomUserHistoryMapper.updateDramaRoomUserHistory(dramaRoomUserHistory);
    }

    /**
     * 批量删除【请填写功能名称】
     *
     * @param ids 需要删除的【请填写功能名称】主键
     * @return 结果
     */
    @Override
    public int deleteDramaRoomUserHistoryByIds(Long[] ids)
    {
        return dramaRoomUserHistoryMapper.deleteDramaRoomUserHistoryByIds(ids);
    }

    /**
     * 删除【请填写功能名称】信息
     *
     * @param id 【请填写功能名称】主键
     * @return 结果
     */
    @Override
    public int deleteDramaRoomUserHistoryById(Long id)
    {
        return dramaRoomUserHistoryMapper.deleteDramaRoomUserHistoryById(id);
    }

    @Override
    public List<DramaRoomUserHistoryVO> queryUserHistory(Long userId) {
        return dramaRoomUserHistoryMapper.queryUserHistoryByUserId(userId);
    }

    @Override
    public int reportViewHistory(DramaRoomUserHistory dramaRoomUserHistory) {
        //查询历史记录书否存在 存在的话更新 不不存在的话 新增
        return 1;
    }

    @Override
    public int reportViewHistory(DramaRoomUserHistoryDTO dramaRoomUserHistory) {
        //查询用户这个剧是否有历史观看记录
        DramaRoomUserHistory query = new DramaRoomUserHistory();
        if(null == dramaRoomUserHistory.getUserId()){
            dramaRoomUserHistory.setUserId(AppSecurityUtils.getAppUserId());
        }
        query.setUserId(dramaRoomUserHistory.getUserId());
        query.setMovieId(dramaRoomUserHistory.getMovieId());
        List<DramaRoomUserHistory> dramaRoomUserHistories = dramaRoomUserHistoryMapper.selectDramaRoomUserHistoryList(query);
        //如果有则更新
        if(CollectionUtil.isNotEmpty(dramaRoomUserHistories)){
            DramaRoomUserHistory next = dramaRoomUserHistories.iterator().next();
            next.setProgress(dramaRoomUserHistory.getProgress());
            next.setVideoId(dramaRoomUserHistory.getVideoId());
            next.setViewDate(dramaRoomUserHistory.getViewDate());
            next.setUpdateTime(DateUtils.getNowDate());
            next.setVideoNum(dramaRoomUserHistory.getVideoNum());
            return dramaRoomUserHistoryMapper.updateDramaRoomUserHistory(next);
        }else {
            DramaRoomUserHistory data = new DramaRoomUserHistory();
            data.setUserId(dramaRoomUserHistory.getUserId());
            data.setMovieId(dramaRoomUserHistory.getMovieId());
            data.setVideoId(dramaRoomUserHistory.getVideoId());
            data.setProgress(dramaRoomUserHistory.getProgress());
            data.setViewDate(dramaRoomUserHistory.getViewDate());
            data.setCreateTime(DateUtils.getNowDate());
            data.setVideoNum(dramaRoomUserHistory.getVideoNum());
            return dramaRoomUserHistoryMapper.insertDramaRoomUserHistory(data);
        }
    }

    @Override
    public List<UserHistoryVO> getUserHistory(String languageCode) {
        Long appUserId = AppSecurityUtils.getAppUserId();
        List<UserHistoryVO> userHistoryVOList = dramaRoomUserHistoryMapper.selectDramaRoomUserHistoryByUserId(appUserId);
        if(CollectionUtil.isNotEmpty(userHistoryVOList)){
            // 是否收藏
            List<UserCollectVO> userCollectVOS = dramaRoomUserCollectMapper.selectDramaRoomUserCollectByUserId(appUserId);
            List<Long> movieIds = userCollectVOS.stream().map(UserCollectVO::getMovieId).collect(Collectors.toList());
            // 多语言
            List<Long> movieIdList = userHistoryVOList.stream().map(UserHistoryVO::getId).collect(Collectors.toList());
            List<DramaRoomMovieI18n> movieI18nList = dramaRoomMovieI18nMapper.selectDramaRoomMovieI18nByMovieIdListAndLanguageCode(movieIdList, languageCode);
            Map<Long, DramaRoomMovieI18n> movieI18nMap = movieI18nList.stream().collect(Collectors.toMap(DramaRoomMovieI18n::getMovieId, Function.identity()));
            for (UserHistoryVO userHistoryVO : userHistoryVOList) {
                userHistoryVO.setTitle(movieI18nMap.containsKey(userHistoryVO.getId()) ? movieI18nMap.get(userHistoryVO.getId()).getTitle() : userHistoryVO.getTitle());
                userHistoryVO.setCoverImage(movieI18nMap.containsKey(userHistoryVO.getId()) ? movieI18nMap.get(userHistoryVO.getId()).getCoverImage() : userHistoryVO.getCoverImage());
                userHistoryVO.setIsCollect(movieIds.contains(userHistoryVO.getMovieId()));
            }
        }
        return userHistoryVOList;
    }
}
