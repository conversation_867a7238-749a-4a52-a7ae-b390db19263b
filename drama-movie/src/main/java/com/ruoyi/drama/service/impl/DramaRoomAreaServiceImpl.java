package com.ruoyi.drama.service.impl;

import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.drama.domain.DramaRoomArea;
import com.ruoyi.drama.domain.DramaRoomAreaI18n;
import com.ruoyi.drama.manager.MovieAsyncManager;
import com.ruoyi.drama.manager.factory.I18nAsyncFactory;
import com.ruoyi.drama.mapper.DramaRoomAreaI18nMapper;
import com.ruoyi.drama.mapper.DramaRoomAreaMapper;
import com.ruoyi.drama.service.IDramaRoomAreaService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 短剧地区Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-28
 */
@Service
public class DramaRoomAreaServiceImpl implements IDramaRoomAreaService {
    @Autowired
    private DramaRoomAreaMapper dramaRoomAreaMapper;

    @Autowired
    private DramaRoomAreaI18nMapper dramaRoomAreaI18nMapper;

    /**
     * 查询短剧地区
     *
     * @param id 短剧地区主键
     * @return 短剧地区
     */
    @Override
    public DramaRoomArea selectDramaRoomAreaById(Long id) {
        return dramaRoomAreaMapper.selectDramaRoomAreaById(id);
    }

    /**
     * 查询短剧地区列表
     *
     * @param dramaRoomArea 短剧地区
     * @return 短剧地区
     */
    @Override
    public List<DramaRoomArea> selectDramaRoomAreaList(DramaRoomArea dramaRoomArea) {
        List<DramaRoomArea> dramaRoomAreas = dramaRoomAreaMapper.selectDramaRoomAreaList(dramaRoomArea);
        List<Long> areaIds = dramaRoomAreas.stream().map(DramaRoomArea::getId).collect(Collectors.toList());
        List<DramaRoomAreaI18n> i18ns = dramaRoomAreaI18nMapper.selectDramaRoomAreaI18nListByAreaIds(areaIds);
        if(CollectionUtils.isEmpty(i18ns)){
            return dramaRoomAreas;
        }
        Map<Long, List<DramaRoomAreaI18n>> i18nMap = i18ns.stream()
            .collect(Collectors.groupingBy(DramaRoomAreaI18n::getAreaId));
        dramaRoomAreas.forEach(area -> {
            if (i18nMap.containsKey(area.getId())) {
                area.setI18nList(i18nMap.get(area.getId()));
            }
        });
        return dramaRoomAreas;
    }

    /**
     * 新增短剧地区
     *
     * @param dramaRoomArea 短剧地区
     * @return 结果
     */
    @Override
    public int insertDramaRoomArea(DramaRoomArea dramaRoomArea) {
        dramaRoomArea.setCreateTime(DateUtils.getNowDate());
        dramaRoomArea.setCreateBy(String.valueOf(SecurityUtils.getUserId()));
        int i = dramaRoomAreaMapper.insertDramaRoomArea(dramaRoomArea);
        if (i > 0) {
            MovieAsyncManager.me().execute(I18nAsyncFactory.getAreaI18n(dramaRoomArea.getId(), dramaRoomArea.getArea(),String.valueOf(SecurityUtils.getUserId())));
        }
        return i;
    }

    /**
     * 修改短剧地区
     *
     * @param dramaRoomArea 短剧地区
     * @return 结果
     */
    @Override
    public int updateDramaRoomArea(DramaRoomArea dramaRoomArea) {
        dramaRoomArea.setUpdateTime(DateUtils.getNowDate());
        return dramaRoomAreaMapper.updateDramaRoomArea(dramaRoomArea);
    }

    /**
     * 批量删除短剧地区
     *
     * @param ids 需要删除的短剧地区主键
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteDramaRoomAreaByIds(Long[] ids) {
        // 先批量删除多语言表数据
        dramaRoomAreaI18nMapper.deleteDramaRoomAreaI18nByAreaIds(ids);
        return dramaRoomAreaMapper.deleteDramaRoomAreaByIds(ids);
    }

    /**
     * 删除短剧地区信息
     *
     * @param id 短剧地区主键
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteDramaRoomAreaById(Long id) {
        // 先删除多语言表数据
        dramaRoomAreaI18nMapper.deleteDramaRoomAreaI18nByAreaId(id);
        return dramaRoomAreaMapper.deleteDramaRoomAreaById(id);
    }
}
