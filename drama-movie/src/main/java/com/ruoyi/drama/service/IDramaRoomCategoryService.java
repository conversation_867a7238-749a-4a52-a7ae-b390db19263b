package com.ruoyi.drama.service;

import com.ruoyi.drama.domain.DramaRoomCategory;

import java.util.List;

/**
 * 分类Service接口
 *
 * <AUTHOR>
 * @date 2025-07-03
 */
public interface IDramaRoomCategoryService
{
    /**
     * 查询分类
     *
     * @param id 分类主键
     * @return 分类
     */
    public DramaRoomCategory selectDramaRoomCategoryById(Long id);

    /**
     * 查询分类列表
     *
     * @param dramaRoomCategory 分类
     * @return 分类集合
     */
    public List<DramaRoomCategory> selectDramaRoomCategoryList(DramaRoomCategory dramaRoomCategory);

    /**
     * 新增分类
     *
     * @param dramaRoomCategory 分类
     * @return 结果
     */
    public int insertDramaRoomCategory(DramaRoomCategory dramaRoomCategory);

    /**
     * 修改分类
     *
     * @param dramaRoomCategory 分类
     * @return 结果
     */
    public int updateDramaRoomCategory(DramaRoomCategory dramaRoomCategory);

    /**
     * 批量删除分类
     *
     * @param ids 需要删除的分类主键集合
     * @return 结果
     */
    public int deleteDramaRoomCategoryByIds(Long[] ids);

    /**
     * 删除分类信息
     *
     * @param id 分类主键
     * @return 结果
     */
    public int deleteDramaRoomCategoryById(Long id);
}
