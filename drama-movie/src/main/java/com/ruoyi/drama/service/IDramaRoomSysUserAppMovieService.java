package com.ruoyi.drama.service;

import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.drama.domain.DramaRoomSysUserAppMovie;

import java.util.List;

/**
 * 运营用户关联app，短剧Service接口
 *
 * <AUTHOR>
 * @date 2025-08-11
 */
public interface IDramaRoomSysUserAppMovieService
{
    /**
     * 查询运营用户关联app，短剧
     *
     * @param id 运营用户关联app，短剧主键
     * @return 运营用户关联app，短剧
     */
    public DramaRoomSysUserAppMovie selectDramaRoomSysUserAppMovieById(Long id);

    /**
     * 查询运营用户关联app，短剧列表
     *
     * @param dramaRoomSysUserAppMovie 运营用户关联app，短剧
     * @return 运营用户关联app，短剧集合
     */
    public List<DramaRoomSysUserAppMovie> selectDramaRoomSysUserAppMovieList(DramaRoomSysUserAppMovie dramaRoomSysUserAppMovie);

    /**
     * 新增运营用户关联app，短剧
     *
     * @param dramaRoomSysUserAppMovie 运营用户关联app，短剧
     * @return 结果
     */
    public int insertDramaRoomSysUserAppMovie(DramaRoomSysUserAppMovie dramaRoomSysUserAppMovie);

    /**
     * 修改运营用户关联app，短剧
     *
     * @param dramaRoomSysUserAppMovie 运营用户关联app，短剧
     * @return 结果
     */
    public int updateDramaRoomSysUserAppMovie(DramaRoomSysUserAppMovie dramaRoomSysUserAppMovie);

    /**
     * 批量删除运营用户关联app，短剧
     *
     * @param ids 需要删除的运营用户关联app，短剧主键集合
     * @return 结果
     */
    public int deleteDramaRoomSysUserAppMovieByIds(Long[] ids);

    /**
     * 删除运营用户关联app，短剧信息
     *
     * @param id 运营用户关联app，短剧主键
     * @return 结果
     */
    public int deleteDramaRoomSysUserAppMovieById(Long id);

    int addList(List<DramaRoomSysUserAppMovie> shortSysUserAppMovieList);

    List<SysUser> getChildUserList(String postId);

    int inherit(Long pId, Long cId);
}