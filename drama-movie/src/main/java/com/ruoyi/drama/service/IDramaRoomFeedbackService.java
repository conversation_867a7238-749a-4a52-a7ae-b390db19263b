package com.ruoyi.drama.service;

import com.ruoyi.drama.domain.DramaRoomFeedback;

import java.util.List;

/**
 * 用户反馈Service接口
 *
 * <AUTHOR>
 * @date 2025-07-28
 */
public interface IDramaRoomFeedbackService
{
    /**
     * 查询用户反馈
     *
     * @param id 用户反馈主键
     * @return 用户反馈
     */
    public DramaRoomFeedback selectDramaRoomFeedbackById(Long id);

    /**
     * 查询用户反馈列表
     *
     * @param dramaRoomFeedback 用户反馈
     * @return 用户反馈集合
     */
    public List<DramaRoomFeedback> selectDramaRoomFeedbackList(DramaRoomFeedback dramaRoomFeedback);

    /**
     * 新增用户反馈
     *
     * @param dramaRoomFeedback 用户反馈
     * @return 结果
     */
    public int insertDramaRoomFeedback(DramaRoomFeedback dramaRoomFeedback);

    /**
     * 修改用户反馈
     *
     * @param dramaRoomFeedback 用户反馈
     * @return 结果
     */
    public int updateDramaRoomFeedback(DramaRoomFeedback dramaRoomFeedback);

    /**
     * 批量删除用户反馈
     *
     * @param ids 需要删除的用户反馈主键集合
     * @return 结果
     */
    public int deleteDramaRoomFeedbackByIds(Long[] ids);

    /**
     * 删除用户反馈信息
     *
     * @param id 用户反馈主键
     * @return 结果
     */
    public int deleteDramaRoomFeedbackById(Long id);
}
