package com.ruoyi.drama.service;

import java.util.List;

import com.ruoyi.common.drama.vo.*;
import com.ruoyi.drama.domain.DramaRoomParamConfig;
import software.amazon.awssdk.services.s3.endpoints.internal.Value;

/**
 * 【请填写功能名称】Service接口
 *
 * <AUTHOR>
 * @date 2025-07-04
 */
public interface IDramaRoomParamConfigService
{
    /**
     * 查询【请填写功能名称】
     *
     * @param id 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    public DramaRoomParamConfig selectDramaRoomParamConfigById(Long id);

    /**
     * 查询【请填写功能名称】列表
     *
     * @param dramaRoomParamConfig 【请填写功能名称】
     * @return 【请填写功能名称】集合
     */
    public List<DramaRoomParamConfig> selectDramaRoomParamConfigList(DramaRoomParamConfig dramaRoomParamConfig);

    /**
     * 新增【请填写功能名称】
     *
     * @param dramaRoomParamConfig 【请填写功能名称】
     * @return 结果
     */
    public int insertDramaRoomParamConfig(DramaRoomParamConfig dramaRoomParamConfig);

    /**
     * 修改【请填写功能名称】
     *
     * @param dramaRoomParamConfig 【请填写功能名称】
     * @return 结果
     */
    public int updateDramaRoomParamConfig(DramaRoomParamConfig dramaRoomParamConfig);

    /**
     * 批量删除【请填写功能名称】
     *
     * @param ids 需要删除的【请填写功能名称】主键集合
     * @return 结果
     */
    public int deleteDramaRoomParamConfigByIds(Long[] ids);

    /**
     * 删除【请填写功能名称】信息
     *
     * @param id 【请填写功能名称】主键
     * @return 结果
     */
    public int deleteDramaRoomParamConfigById(Long id);

    /**
     * 查询首页配置
     * @param key
     * @return
     */
    List<MovieCategoryVO> selectRecommendConfigByKey(String key, String languageCode);

    /**
     * 查询筛选数据
     */
    MovieFilterVO selectFilterData(String languageCode);

    /**
     * 查询推荐短剧
     */
    List<PushMovieVideoVO> selectRecommendMovieConfigByKey(String appRecommendMovieConfig, String languageCode);
}
