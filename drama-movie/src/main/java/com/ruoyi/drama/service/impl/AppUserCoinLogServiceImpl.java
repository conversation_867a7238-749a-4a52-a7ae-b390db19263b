package com.ruoyi.drama.service.impl;

import com.ruoyi.common.drama.vo.UserCoinLogTradeVO;
import com.ruoyi.common.drama.vo.UserCoinLogVO;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.mobile.AppSecurityUtils;
import com.ruoyi.drama.domain.AppUserCoinLog;
import com.ruoyi.drama.mapper.AppUserCoinLogMapper;
import com.ruoyi.drama.service.IAppUserCoinLogService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 金币记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-29
 */
@Service
public class AppUserCoinLogServiceImpl implements IAppUserCoinLogService {
    @Autowired
    private AppUserCoinLogMapper appUserCoinLogMapper;

    /**
     * 查询金币记录
     *
     * @param id 金币记录主键
     * @return 金币记录
     */
    @Override
    public AppUserCoinLog selectAppUserCoinLogById(Long id) {
        return appUserCoinLogMapper.selectAppUserCoinLogById(id);
    }

    /**
     * 查询金币记录列表
     *
     * @param appUserCoinLog 金币记录
     * @return 金币记录
     */
    @Override
    public List<AppUserCoinLog> selectAppUserCoinLogList(AppUserCoinLog appUserCoinLog) {
        return appUserCoinLogMapper.selectAppUserCoinLogList(appUserCoinLog);
    }

    /**
     * 新增金币记录
     *
     * @param appUserCoinLog 金币记录
     * @return 结果
     */
    @Override
    public int insertAppUserCoinLog(AppUserCoinLog appUserCoinLog) {
        appUserCoinLog.setCreateTime(DateUtils.getNowDate());
        return appUserCoinLogMapper.insertAppUserCoinLog(appUserCoinLog);
    }

    /**
     * 修改金币记录
     *
     * @param appUserCoinLog 金币记录
     * @return 结果
     */
    @Override
    public int updateAppUserCoinLog(AppUserCoinLog appUserCoinLog) {
        appUserCoinLog.setUpdateTime(DateUtils.getNowDate());
        return appUserCoinLogMapper.updateAppUserCoinLog(appUserCoinLog);
    }

    /**
     * 批量删除金币记录
     *
     * @param ids 需要删除的金币记录主键
     * @return 结果
     */
    @Override
    public int deleteAppUserCoinLogByIds(Long[] ids) {
        return appUserCoinLogMapper.deleteAppUserCoinLogByIds(ids);
    }

    /**
     * 删除金币记录信息
     *
     * @param id 金币记录主键
     * @return 结果
     */
    @Override
    public int deleteAppUserCoinLogById(Long id) {
        return appUserCoinLogMapper.deleteAppUserCoinLogById(id);
    }

    @Override
    public List<UserCoinLogVO> getSignInByUserId() {
        Long appUserId = AppSecurityUtils.getAppUserId();
        return appUserCoinLogMapper.getSignInByUserId(appUserId);
    }

    @Override
    public List<UserCoinLogTradeVO> rechargeRecord() {
        Long appUserId = AppSecurityUtils.getAppUserId();
        List<AppUserCoinLog> appUserCoinLogs = appUserCoinLogMapper.selectUserCoinLogByUserIdAndTypeIncomeType(appUserId, 0);
        return appUserCoinLogs.stream().map(appUserCoinLog -> new UserCoinLogTradeVO(
                appUserCoinLog.getId(),
                appUserCoinLog.getType(),
                appUserCoinLog.getTradeType(),
                appUserCoinLog.getCoins(),
                appUserCoinLog.getBeforeCoins(),
                appUserCoinLog.getAfterCoins(),
                appUserCoinLog.getContent(),
                appUserCoinLog.getCreateTime()
        )).collect(Collectors.toList());
    }

    @Override
    public List<UserCoinLogTradeVO> rewardRecords() {
        Long appUserId = AppSecurityUtils.getAppUserId();
        List<AppUserCoinLog> appUserCoinLogs = appUserCoinLogMapper.selectUserCoinLogByUserIdAndTypeIncomeType(appUserId, 1);
        return appUserCoinLogs.stream().map(appUserCoinLog -> new UserCoinLogTradeVO(
                appUserCoinLog.getId(),
                appUserCoinLog.getType(),
                appUserCoinLog.getTradeType(),
                appUserCoinLog.getCoins(),
                appUserCoinLog.getBeforeCoins(),
                appUserCoinLog.getAfterCoins(),
                appUserCoinLog.getContent(),
                appUserCoinLog.getCreateTime()
        )).collect(Collectors.toList());
    }

    @Override
    public int selectByTypeAndUserIdAndSourceId(Long type, Long userId, Long signInRuleInfoId) {
        return appUserCoinLogMapper.selectByTypeAndUserIdAndSourceId(type,userId,signInRuleInfoId);
    }


}
