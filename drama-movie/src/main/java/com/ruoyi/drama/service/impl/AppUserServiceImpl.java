package com.ruoyi.drama.service.impl;


import com.ruoyi.common.constant.Constants;
import com.ruoyi.common.core.domain.entity.AppUser;
import com.ruoyi.common.core.domain.entity.AppUserDevice;
import com.ruoyi.common.core.domain.model.AppLoginInfo;
import com.ruoyi.common.core.domain.model.mobile.AppLoginUser;
import com.ruoyi.drama.domain.vo.SemAppUserVo;
import com.ruoyi.drama.domain.AppUserCoin;
import com.ruoyi.drama.mapper.AppUserCoinMapper;
import com.ruoyi.drama.mapper.AppUserDeviceMapper;
import com.ruoyi.drama.mapper.AppUserMapper;
import com.ruoyi.common.drama.dto.GuestLoginDto;
import com.ruoyi.common.drama.dto.RefreshTokenDto;
import com.ruoyi.common.drama.dto.ThirdPartyLoginDTO;
import com.ruoyi.common.drama.vo.UserInfoVO;
import com.ruoyi.common.utils.RequestUtils;
import com.ruoyi.common.utils.SocialLoginVerifier;
import com.ruoyi.common.utils.mobile.AppTokenService;
import com.ruoyi.common.utils.mobile.AppSecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.constant.DramaConfigConstants;
import com.ruoyi.common.utils.ip.IpUtils;
import com.ruoyi.common.utils.ServletUtils;
import com.ruoyi.drama.service.IAppUserService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.function.Consumer;
import java.util.stream.Collectors;

/**
 * App用户 服务层实现
 *
 * <AUTHOR>
 */
@Service
public class AppUserServiceImpl implements IAppUserService {
    private static final Logger log = LoggerFactory.getLogger(AppUserServiceImpl.class);

    @Autowired
    private AppUserMapper appUserMapper;

    @Autowired
    private AppUserDeviceMapper appUserDeviceMapper;

    @Autowired
    private AppTokenService appTokenService;

    @Autowired
    private AppUserCoinMapper appUserCoinMapper;

    /**
     * 允许账号最大绑定设备数
     */
    private int maxBindDeviceCount = 1;


    /**
     * 游客登录
     *
     * @param guestLoginDto 登录信息
     * @return 令牌信息
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public AppTokenService.TokenVo guestLogin(GuestLoginDto guestLoginDto) {
        String deviceId = guestLoginDto.getDeviceId().trim();


        AppUser appUser = appUserMapper.selectAppUserByDeviceId(deviceId);
        if (appUser == null) {
            AppLoginInfo appLoginInfo = guestLoginDto.initAppLoginInfo();
            appUser = handleAccountConflict(appLoginInfo, isTop -> {
            });
        } else {
            // 更新用户最后登录时间和IP
            appUser.setLastLoginDate(new Date());
            appUser.setLastLoginIp(IpUtils.getIpAddr(ServletUtils.getRequest()));
            appUserMapper.updateAppUser(appUser);
        }

        // 检查用户状态
        if (!appUser.hasEnabled()) {
            throw new ServiceException("用户已被禁用");
        }
        return createUserToken(appUser, deviceId, false);
    }


    /**
     * 刷新令牌
     *
     * @param refreshTokenDto 刷新信息
     * @return 新的令牌信息
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public AppTokenService.TokenVo refreshToken(RefreshTokenDto refreshTokenDto) {
        // 验证令牌类型
        String refreshToken = refreshTokenDto.getRefreshToken().trim();
        String tokenTypeFromToken = appTokenService.getTokenTypeFromToken(refreshToken);
        if (!tokenTypeFromToken.equals(Constants.REFRESH_TOKEN_KEY)) {
            throw new ServiceException("非法的refreshToken");
        }

        // 从令牌中获取设备ID
        String deviceId = appTokenService.getDeviceIdFromToken(refreshToken);
        AppUser appUser = appUserMapper.selectAppUserByDeviceId(deviceId);
        AtomicBoolean isTop = new AtomicBoolean(false);
        if (appUser == null) {
            // 用户不存在时处理账号冲突
            AppLoginInfo appLoginInfo = refreshTokenDto.initAppLoginInfo(deviceId);
            appUser = handleAccountConflict(appLoginInfo, isTop::set);
        }

        return createUserToken(appUser, deviceId, isTop.get());
    }

    /**
     * 第三方登录
     * 支持各种第三方平台的登录
     *
     * @param thirdPartyLoginDTO 第三方登录信息
     * @return 令牌信息
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public AppTokenService.TokenVo thirdPartyLogin(ThirdPartyLoginDTO thirdPartyLoginDTO) {
        try {
            SocialLoginVerifier.Provider provider = thirdPartyLoginDTO.getProvider();
            String accessToken = thirdPartyLoginDTO.getAccessToken().trim();
            // 获取当前登录用户信息
            AppLoginUser loginUser = appTokenService.getLoginUser(RequestUtils.getHttpServletRequest());
            String deviceId = loginUser.getDeviceId();
            Long userId = loginUser.getUserId();

            // 验证第三方令牌并获取用户信息
            SocialLoginVerifier.SocialUserInfo userInfo = SocialLoginVerifier.getUserInfo(provider, accessToken);
            if (!userInfo.isSuccess()) {
                throw new ServiceException("获取用户信息失败，token失效或已过期");
            }

            String platformOpenId = userInfo.getId();
            // 查询是否存在第三方账号
            AppUser existUser = appUserMapper.selectByPlatformOpenId(provider.toString(), platformOpenId);

            AppUser appUser = loginUser.getAppUser();
            if ((existUser == null || appUser.getId().equals(existUser.getId())) && (StringUtils.isEmpty(appUser.getAuthType()) || provider.name().equals(appUser.getAuthType()))) {
                // 没有三方账号，认证类型为空或与当前登录认证类型一致，更新用户信息
                updateThirdPartyLoginUser(provider, accessToken, userInfo, appUser);
            } else {
                if (!appUser.getId().equals(existUser.getId())) {
                    // 存在三方账号，三方账号ID与当前用户ID不一致则要判断用户上限绑定数
                    // 如果达到上限则删除最老的设备信息，再绑定新设备
                    AppUserDevice appUserDevice = appUserDeviceMapper.selectAppUserDeviceByUserIdAndDeviceId(userId, deviceId);
                    appUserDeviceMapper.deleteAppUserDeviceById(appUserDevice.getId());
                    appTokenService.delLoginUser(userId + "", deviceId);
                    appUser = existUser;
                    List<AppUserDevice> deviceList = appUserDeviceMapper.selectAppUserDeviceListByUserId(appUser.getId());
                    if (deviceList.size() >= maxBindDeviceCount) {
                        // 账号已绑定设备数达到上限，删除最老的设备信息，再绑定新设备
                        AppUserDevice oldAppUserDevice = deviceList.get(0);
                        appUserDeviceMapper.deleteAppUserDeviceById(oldAppUserDevice.getId());
                        AppUserDevice newAppUserDevice = new AppUserDevice();
                        thirdPartyLoginDTO.initDeviceInfo(newAppUserDevice, appUser.getId(), deviceId);
                        appUserDeviceMapper.insertAppUserDevice(newAppUserDevice);
                        appTokenService.delLoginUser(oldAppUserDevice.getUserId() + "", oldAppUserDevice.getDeviceId());
                    } else {
                        // 账号未达到上限，绑定设备ID
                        AppUserDevice newAppUserDevice = new AppUserDevice();
                        thirdPartyLoginDTO.initDeviceInfo(newAppUserDevice, appUser.getId(), deviceId);
                        appUserDeviceMapper.insertAppUserDevice(newAppUserDevice);
                    }
                    // 绑定设备ID
                    updateThirdPartyLoginUser(provider, accessToken, userInfo, appUser);
                }

                if (!provider.name().equals(appUser.getAuthType())) {
                    // 认证类型不同,去掉老账号绑定的设备ID，创建新用户绑定当前设备ID
                    AppUserDevice oldAppUserDevice = appUserDeviceMapper.selectAppUserDeviceByUserIdAndDeviceId(userId, deviceId);
                    appUserDeviceMapper.deleteAppUserDeviceById(oldAppUserDevice.getId());

                    // 创建新用户
                    appUser = new AppUser();
                    appUser.init(loginUser.getDeviceId(), DramaConfigConstants.UserConstants.ACCOUNT_TYPE_THIRD_PARTY);
                    AppUserDevice newAppUserDevice = new AppUserDevice();
                    thirdPartyLoginDTO.initDeviceInfo(newAppUserDevice, appUser.getId(), deviceId);
                    if (!appUser.hasValidDeviceId()) {
                        throw new ServiceException("设备ID非法");
                    }
                    // 设置第三方用户信息
                    appUser.setAuthType(provider.toString());
                    appUser.setEmail(userInfo.getEmail());
                    appUser.setNickname(userInfo.getName());
                    appUser.setPlatformOpenId(userInfo.getId());
                    appUserMapper.insertAppUser(appUser);
                    appUserDeviceMapper.insertAppUserDevice(newAppUserDevice);

                }
            }

            // 检查用户状态
            if (!appUser.hasEnabled()) {
                throw new ServiceException("用户已被禁用");
            }
            return createUserToken(appUser, deviceId, false);

        } catch (Exception e) {
            log.error("第三方登录失败: {}", e.getMessage());
            throw new ServiceException(e.getMessage());
        }
    }


    /**
     * 用户退出登录
     * 清除用户的登录状态和令牌信息
     */
    @Override
    public void logout() {
        try {
            String token = AppSecurityUtils.getAppToken();
            if (StringUtils.isNotEmpty(token)) {
                appTokenService.delLoginUser(token);
                log.info("用户退出登录成功");
            }
        } catch (Exception e) {
            log.warn("退出登录时发生异常: {}", e.getMessage());
            // 退出登录失败不抛异常，避免影响用户体验
        }
    }

    /**
     * 查询APP用户
     *
     * @param userId APP用户主键
     * @return APP用户
     */
    @Override
    public AppUser selectAppUserByUserId(Long userId) {
        if (userId == null) {
            return null;
        }
        return appUserMapper.selectAppUserById(userId);
    }

    /**
     * 查询APP用户列表
     *
     * @param appUser APP用户
     * @return APP用户列表
     */
    @Override
    public List<AppUser> selectAppUserList(AppUser appUser) {
        return appUserMapper.selectAppUserList(appUser);
    }

    /**
     * 获取登录用户信息
     *
     * @return 用户信息VO
     */
    @Override
    public UserInfoVO getLoginUserInfo() {
        // TODO 完善用户信息
        AppLoginUser loginUser = appTokenService.getLoginUser(RequestUtils.getHttpServletRequest());
        UserInfoVO userInfoVO = new UserInfoVO();
        AppUser appUser = appUserMapper.selectAppUserById(loginUser.getUserId());
        userInfoVO.setUserId(appUser.getUserTempId());
        userInfoVO.setAvatar(appUser.getAvatar());
        userInfoVO.setAccountType(appUser.getAccountType());

        return userInfoVO;
    }

    @Override
    public int insertAppUser(AppUser appUser) {
        return appUserMapper.insertAppUser(appUser);
    }

    @Override
    public int updateAppUser(AppUser appUser) {
        return appUserMapper.updateAppUser(appUser);
    }

    @Override
    public int deleteAppUserByUserIds(Long[] userIds) {
        return appUserMapper.deleteAppUserByUserIds(userIds);
    }

    @Override
    public int deleteAppUserByUserId(Long userId) {
        return appUserMapper.deleteAppUserByUserId(userId);
    }

    @Override
    public List<SemAppUserVo> selectSemAppUserList(AppUser appUser) {
        List<AppUser> appUsers = this.selectAppUserList(appUser);
        List<AppUserCoin> appUserCoins = appUserCoinMapper.selectAppUserCoinInUserIds(appUsers.stream().map(AppUser::getId).collect(Collectors.toList()));
        List<AppUserDevice> appUserDevices = appUserDeviceMapper.selectAppUserDeviceListInUserIds(appUsers.stream().map(AppUser::getId).collect(Collectors.toList()));
        // TODO 完善用户信息
        return appUsers.stream().map(item -> {
            SemAppUserVo vo = new SemAppUserVo();
            AppUserCoin appUserCoin = appUserCoins.stream().filter(coin -> coin.getUserId().equals(item.getId()))
                    .findFirst().orElse(new AppUserCoin());
            List<AppUserDevice> appUserDeviceList = appUserDevices.stream().filter(device -> device.getUserId().equals(item.getId())).collect(Collectors.toList());
            vo.init(item,appUserCoin,appUserDeviceList);
            return vo;
        }).collect(Collectors.toList());
    }


    /**
     * 创建用户Token
     *
     * @param appUser  用户对象
     * @param deviceId 设备ID
     * @param isTop    是否为优先级用户
     * @return 令牌信息
     */
    private AppTokenService.TokenVo createUserToken(AppUser appUser, String deviceId, boolean isTop) {
        AppLoginUser appLoginUser = new AppLoginUser(appUser);
        appLoginUser.setDeviceId(deviceId);

        return appTokenService.createToken(appLoginUser, isTop);
    }


    /**
     * 初始化APP用户
     * 创建新的游客用户并绑定设备
     *
     * @param appLoginInfo 设备ID
     * @return APP用户对象
     */
    private AppUser initAppUser(AppLoginInfo appLoginInfo) {
        AppUser appUser;
        String deviceId = appLoginInfo.getDeviceId();
        appUser = new AppUser();
        appUser.init(deviceId, DramaConfigConstants.UserConstants.ACCOUNT_TYPE_GUEST);
        AppUserDevice appUserDevice = new AppUserDevice();
        appLoginInfo.initDeviceInfo(appUserDevice, appUser.getId(), deviceId);
        if (!appUser.hasValidDeviceId()) {
            throw new ServiceException("设备ID非法");
        }
        // 插入用户和设备绑定信息
        appUserMapper.insertAppUser(appUser);
        appUserDeviceMapper.insertAppUserDevice(appUserDevice);
        return appUser;
    }


    /**
     * 更新第三方登录用户信息
     * 将第三方平台的用户信息同步到本地用户对象
     *
     * @param provider    三方登录提供商
     * @param accessToken 访问令牌
     * @param userInfo    用户信息
     * @param appUser     APP用户对象
     */
    private void updateThirdPartyLoginUser(SocialLoginVerifier.Provider provider, String accessToken, SocialLoginVerifier.SocialUserInfo userInfo, AppUser appUser) {
        appUser.setAuthType(provider.toString());
        appUser.setEmail(userInfo.getEmail());
        appUser.setAccountType(DramaConfigConstants.UserConstants.ACCOUNT_TYPE_THIRD_PARTY);
        appUser.setNickname(userInfo.getName());
        appUser.setPlatformOpenId(userInfo.getId());
        appUser.setCredential(accessToken);
        appUserMapper.updateAppUser(appUser);
    }


    /**
     * 处理账号冲突
     * 当设备ID对应的用户不存在或设备绑定达到上限时的处理逻辑
     *
     * @param appLoginInfo 登录信息
     * @param callback     回调函数，用于通知是否为优先级账号
     * @return AppUser 用户对象
     */
    private AppUser handleAccountConflict(AppLoginInfo appLoginInfo, Consumer<Boolean> callback) {
        AppUser appUser;
        String deviceId = appLoginInfo.getDeviceId();
        // 优先通过首次登录的设备ID查询用户
        AppUser firstAppUser = appUserMapper.selectAppUserByFirstDeviceId(deviceId);
        List<AppUserDevice> appUserDevices = null;
        if (firstAppUser != null) {
            // 存在首次登录的设备ID，查询该设备绑定的所有用户
            appUserDevices = appUserDeviceMapper.selectAppUserDeviceListByUserId(firstAppUser.getId());
        }

        if (firstAppUser == null) {
            // 如果没有查到，说明是新用户，创建新用户
            appUser = initAppUser(appLoginInfo);
        } else {
            if (appUserDevices == null || appUserDevices.isEmpty() || maxBindDeviceCount < appUserDevices.size()) {
                // 如果查到了用户，但是没有查到绑定设备｜绑定设备未达到上限，则绑定设备ID
                appUser = firstAppUser;
                // 账号未达到上限，绑定设备ID
                AppUserDevice newAppUserDevice = new AppUserDevice();
                appLoginInfo.initDeviceInfo(newAppUserDevice, appUser.getId(), deviceId);
                appUserDeviceMapper.insertAppUserDevice(newAppUserDevice);
            } else {
                // 账号已绑定设备数达到上限，创建新用户
                appUser = initAppUser(appLoginInfo);
            }
            // 通知回调函数这是一个优先级账号
            callback.accept(true);
        }
        return appUser;
    }
}