package com.ruoyi.drama.service;

import com.ruoyi.common.drama.dto.UserCoinDTO;
import com.ruoyi.common.drama.vo.SignInRuleVO;
import com.ruoyi.common.drama.vo.UserCoinLogVO;

import java.io.IOException;
import java.util.List;

/**
 * 充值金币相关接口
 *
 * <AUTHOR>
 * @date 2025-07-29
 */
public interface IUserCoinService
{

    int recharge(UserCoinDTO userCoinDTO);

    List<UserCoinLogVO> getSignInByUserId();

    int signIn(UserCoinDTO userCoinDTO);

    List<SignInRuleVO> getUserBenefits(Integer type);
}
