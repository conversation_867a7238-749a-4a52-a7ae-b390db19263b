package com.ruoyi.drama.service.impl;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import cn.hutool.core.collection.CollectionUtil;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.drama.domain.DramaRoomTagI18n;
import com.ruoyi.drama.manager.MovieAsyncManager;
import com.ruoyi.drama.manager.factory.I18nAsyncFactory;
import com.ruoyi.drama.mapper.DramaRoomTagI18nMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.drama.mapper.DramaRoomTagMapper;
import com.ruoyi.drama.domain.DramaRoomTag;
import com.ruoyi.drama.service.IDramaRoomTagService;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

/**
 * 标签Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-04
 */
@Service
public class DramaRoomTagServiceImpl implements IDramaRoomTagService {
    @Autowired
    private DramaRoomTagMapper dramaRoomTagMapper;

    @Autowired
    private DramaRoomTagI18nMapper dramaRoomTagI18nMapper;

    /**
     * 查询标签
     *
     * @param tagId 标签主键
     * @return 标签
     */
    @Override
    public DramaRoomTag selectDramaRoomTagByTagId(Long tagId) {
        return dramaRoomTagMapper.selectDramaRoomTagByTagId(tagId);
    }

    /**
     * 查询标签列表
     *
     * @param dramaRoomTag 标签
     * @return 标签
     */
    @Override
    public List<DramaRoomTag> selectDramaRoomTagList(DramaRoomTag dramaRoomTag) {
        List<DramaRoomTag> dramaRoomTags = dramaRoomTagMapper.selectDramaRoomTagList(dramaRoomTag);
        if(CollectionUtil.isEmpty(dramaRoomTags)){
            return dramaRoomTags;
        }
        List<Long> tagIds = dramaRoomTags.stream().map(DramaRoomTag::getTagId).collect(Collectors.toList());
        List<DramaRoomTagI18n> i18ns = dramaRoomTagI18nMapper.selectDramaRoomTagI18nByTagIds(tagIds);
        Map<Long, List<DramaRoomTagI18n>> i18nMap = i18ns.stream().collect(Collectors.groupingBy(DramaRoomTagI18n::getTagId));
        dramaRoomTags.forEach(tag -> {
            if (i18nMap.containsKey(tag.getTagId())) {
                tag.setI18nList(i18nMap.get(tag.getTagId()));
            }
        });
        return dramaRoomTags;
    }

    /**
     * 新增标签
     *
     * @param dramaRoomTag 标签
     * @return 结果
     */
    @Override
    public int insertDramaRoomTag(DramaRoomTag dramaRoomTag) {
        dramaRoomTag.setCreateTime(DateUtils.getNowDate());
        dramaRoomTag.setCreateBy(String.valueOf(SecurityUtils.getUserId()));
        int i = dramaRoomTagMapper.insertDramaRoomTag(dramaRoomTag);
        if (i > 0) {
            MovieAsyncManager.me().execute(I18nAsyncFactory.getTagI18n(dramaRoomTag.getTagId(), dramaRoomTag.getTagName(), String.valueOf(SecurityUtils.getUserId())));
        }
        return i;
    }

    /**
     * 修改标签
     *
     * @param dramaRoomTag 标签
     * @return 结果
     */
    @Override
    public int updateDramaRoomTag(DramaRoomTag dramaRoomTag) {
        dramaRoomTag.setUpdateTime(DateUtils.getNowDate());
        return dramaRoomTagMapper.updateDramaRoomTag(dramaRoomTag);
    }

    /**
     * 批量删除标签
     *
     * @param tagIds 需要删除的标签主键
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteDramaRoomTagByTagIds(Long[] tagIds) {
        dramaRoomTagI18nMapper.deleteDramaRoomTagI18nByTagIds(tagIds);
        return dramaRoomTagMapper.deleteDramaRoomTagByTagIds(tagIds);
    }

    /**
     * 删除标签信息
     *
     * @param tagId 标签主键
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteDramaRoomTagByTagId(Long tagId) {
        dramaRoomTagI18nMapper.deleteDramaRoomTagI18nByTagId(tagId);
        return dramaRoomTagMapper.deleteDramaRoomTagByTagId(tagId);
    }
}
