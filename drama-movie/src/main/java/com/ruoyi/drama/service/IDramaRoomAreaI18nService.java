package com.ruoyi.drama.service;

import java.util.ArrayList;
import java.util.List;

import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.drama.dto.DramaRoomGeneralI18nDTO;
import com.ruoyi.drama.domain.DramaRoomAreaI18n;
import org.apache.ibatis.annotations.Param;

/**
 * 短剧地区多语言Service接口
 * 
 * <AUTHOR>
 * @date 2025-08-05
 */
public interface IDramaRoomAreaI18nService 
{
    /**
     * 查询短剧地区多语言
     * 
     * @param id 短剧地区多语言主键
     * @return 短剧地区多语言
     */
    public DramaRoomAreaI18n selectDramaRoomAreaI18nById(Long id);

    /**
     * 查询短剧地区多语言列表
     * 
     * @param dramaRoomAreaI18n 短剧地区多语言
     * @return 短剧地区多语言集合
     */
    public List<DramaRoomAreaI18n> selectDramaRoomAreaI18nList(DramaRoomAreaI18n dramaRoomAreaI18n);

    /**
     * 新增短剧地区多语言
     * 
     * @param dramaRoomAreaI18n 短剧地区多语言
     * @return 结果
     */
    public int insertDramaRoomAreaI18n(DramaRoomAreaI18n dramaRoomAreaI18n);

    /**
     * 修改短剧地区多语言
     * 
     * @param dramaRoomAreaI18n 短剧地区多语言
     * @return 结果
     */
    public int updateDramaRoomAreaI18n(DramaRoomAreaI18n dramaRoomAreaI18n);

    /**
     * 批量删除短剧地区多语言
     * 
     * @param ids 需要删除的短剧地区多语言主键集合
     * @return 结果
     */
    public int deleteDramaRoomAreaI18nByIds(Long[] ids);

    /**
     * 删除短剧地区多语言信息
     * 
     * @param id 短剧地区多语言主键
     * @return 结果
     */
    public int deleteDramaRoomAreaI18nById(Long id);

    /**
     * 批量插入多语言
     * @param i18ns
     */
    void insertDramaRoomAreaI18nList(List<DramaRoomAreaI18n> i18ns);

    /**
     * 单语种翻译
     * @param dto
     */
    AjaxResult translate(DramaRoomGeneralI18nDTO dto);

    /**
     * 需要翻译的语种
     * @param id
     * @return
     */
    AjaxResult listTranslate(Long id);
}
