package com.ruoyi.drama.service;

import java.util.List;

import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.drama.dto.DramaRoomGeneralI18nDTO;
import com.ruoyi.drama.domain.DramaRoomTagI18n;

/**
 * 标签多语言Service接口
 * 
 * <AUTHOR>
 * @date 2025-08-05
 */
public interface IDramaRoomTagI18nService 
{
    /**
     * 查询标签多语言
     * 
     * @param id 标签多语言主键
     * @return 标签多语言
     */
    public DramaRoomTagI18n selectDramaRoomTagI18nById(Long id);

    /**
     * 查询标签多语言列表
     * 
     * @param dramaRoomTagI18n 标签多语言
     * @return 标签多语言集合
     */
    public List<DramaRoomTagI18n> selectDramaRoomTagI18nList(DramaRoomTagI18n dramaRoomTagI18n);

    /**
     * 新增标签多语言
     * 
     * @param dramaRoomTagI18n 标签多语言
     * @return 结果
     */
    public int insertDramaRoomTagI18n(DramaRoomTagI18n dramaRoomTagI18n);

    /**
     * 修改标签多语言
     * 
     * @param dramaRoomTagI18n 标签多语言
     * @return 结果
     */
    public int updateDramaRoomTagI18n(DramaRoomTagI18n dramaRoomTagI18n);

    /**
     * 批量删除标签多语言
     * 
     * @param ids 需要删除的标签多语言主键集合
     * @return 结果
     */
    public int deleteDramaRoomTagI18nByIds(Long[] ids);

    /**
     * 删除标签多语言信息
     * 
     * @param id 标签多语言主键
     * @return 结果
     */
    public int deleteDramaRoomTagI18nById(Long id);

    /**
     * 批量插入标签多语言
     * @param i18ns
     */
    void insertDramaRoomTagI18nList(List<DramaRoomTagI18n> i18ns);

    /**
     * 单语种翻译
     * @param dto
     * @return
     */
    AjaxResult translate(DramaRoomGeneralI18nDTO dto);

    /**
     * 多语种翻译
     * @param id
     * @return
     */
    AjaxResult listTranslate(Long id);
}
