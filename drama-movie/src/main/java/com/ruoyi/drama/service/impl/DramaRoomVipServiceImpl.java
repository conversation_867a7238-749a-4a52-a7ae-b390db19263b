package com.ruoyi.drama.service.impl;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import cn.hutool.core.collection.CollectionUtil;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.drama.mapper.DramaRoomVipMapper;
import com.ruoyi.drama.domain.DramaRoomVip;
import com.ruoyi.drama.service.IDramaRoomVipService;
import org.springframework.transaction.annotation.Transactional;

/**
 * vip管理Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-08-11
 */
@Service
public class DramaRoomVipServiceImpl implements IDramaRoomVipService
{
    @Autowired
    private DramaRoomVipMapper dramaRoomVipMapper;

    /**
     * 查询vip管理
     *
     * @param id vip管理主键
     * @return vip管理
     */
    @Override
    public DramaRoomVip selectDramaRoomVipById(Long id)
    {
        return dramaRoomVipMapper.selectDramaRoomVipById(id);
    }

    /**
     * 查询vip管理列表
     *
     * @param dramaRoomVip vip管理
     * @return vip管理
     */
    @Override
    public List<DramaRoomVip> selectDramaRoomVipList(DramaRoomVip dramaRoomVip)
    {
        return dramaRoomVipMapper.selectDramaRoomVipList(dramaRoomVip);
    }

    /**
     * 新增vip管理
     *
     * @param dramaRoomVip vip管理
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int insertDramaRoomVip(DramaRoomVip dramaRoomVip)
    {
        dramaRoomVip.setCreateTime(DateUtils.getNowDate());
        dramaRoomVip.setCreateBy(SecurityUtils.getUserId().toString());
        if(checkVipSort(dramaRoomVip)){
            throw new RuntimeException("当前模板下排序已存在,请重试");
        }
        //如果本条设置为了推荐支付模板
        if (Objects.equals(dramaRoomVip.getDefaultRecommend(), 1L)) {
            //查询当前模板下优先推荐的那条
            DramaRoomVip dramaRoomVipQuery = new DramaRoomVip();
            dramaRoomVipQuery.setPayTemplateId(dramaRoomVip.getPayTemplateId());
            dramaRoomVipQuery.setDefaultRecommend(1L);
            List<DramaRoomVip> list = dramaRoomVipMapper.selectDramaRoomVipList(dramaRoomVipQuery);
            if(CollectionUtil.isNotEmpty(list)){
                //执行更新 设置已推荐的记录设置为非推荐
                dramaRoomVipMapper.updateDefaultRecommendById(list.stream().map(DramaRoomVip::getId).collect(Collectors.toList()));
            }
        }
        //检验名称是否重复
        DramaRoomVip query = new DramaRoomVip();
        query.setName(dramaRoomVip.getName());
        List<DramaRoomVip> list = dramaRoomVipMapper.selectDramaRoomVipList(query);
        if(CollectionUtil.isNotEmpty(list)){
            throw new RuntimeException("付费计划名称已存在");
        }
        return dramaRoomVipMapper.insertDramaRoomVip(dramaRoomVip);
    }

    private Boolean checkVipSort(DramaRoomVip shortVip){
        Boolean flag = false;
        DramaRoomVip dramaRoomVipQuery = new DramaRoomVip();
        dramaRoomVipQuery.setPayTemplateId(shortVip.getPayTemplateId());
        List<DramaRoomVip> list = dramaRoomVipMapper.selectDramaRoomVipList(dramaRoomVipQuery);
        if(CollectionUtil.isEmpty(list)){
            return flag;
        }
        List<Long> collect = list.stream().map(DramaRoomVip::getSort).distinct().collect(Collectors.toList());
        return collect.contains(shortVip.getSort());
    }

    /**
     * 修改vip管理
     *
     * @param dramaRoomVip vip管理
     * @return 结果
     */
    @Override
    public int updateDramaRoomVip(DramaRoomVip dramaRoomVip)
    {
        dramaRoomVip.setUpdateTime(DateUtils.getNowDate());
        dramaRoomVip.setUpdateBy(SecurityUtils.getUserId().toString());
        DramaRoomVip oldData = dramaRoomVipMapper.selectDramaRoomVipById(dramaRoomVip.getId());
        if(StringUtils.isNull(oldData)) {
            throw new RuntimeException("要更新的VIP数据不存在");
        }
        if(!oldData.getSort().equals(dramaRoomVip.getSort()) && checkVipSort(dramaRoomVip)){
            throw new RuntimeException("当前模板下排序已存在,请重试");
        }
        //如果本条设置为了推荐支付模板
        if (Objects.equals(dramaRoomVip.getDefaultRecommend(), 1L)) {
            //查询当前模板下优先推荐的那条
            DramaRoomVip dramaRoomVipQuery = new DramaRoomVip();
            dramaRoomVipQuery.setPayTemplateId(dramaRoomVip.getPayTemplateId());
            dramaRoomVipQuery.setDefaultRecommend(1L);
            List<DramaRoomVip> list = dramaRoomVipMapper.selectDramaRoomVipList(dramaRoomVipQuery);
            if(CollectionUtil.isNotEmpty(list)){
                //执行更新 设置已推荐的记录设置为非推荐
                dramaRoomVipMapper.updateDefaultRecommendById(list.stream().map(DramaRoomVip::getId).collect(Collectors.toList()));
            }
        }
        DramaRoomVip query = new DramaRoomVip();
        query.setName(dramaRoomVip.getName());
        List<DramaRoomVip> list = dramaRoomVipMapper.selectDramaRoomVipList(query);
        if(CollectionUtil.isNotEmpty(list)){
            List<DramaRoomVip> collect = list.stream().filter(item -> !item.getId().equals(dramaRoomVip.getId())).collect(Collectors.toList());
            if(CollectionUtil.isNotEmpty(collect)){
                throw new RuntimeException("付费计划名称已存在");
            }
        }
        return dramaRoomVipMapper.updateDramaRoomVip(dramaRoomVip);
    }

    /**
     * 批量删除vip管理
     *
     * @param ids 需要删除的vip管理主键
     * @return 结果
     */
    @Override
    public int deleteDramaRoomVipByIds(Long[] ids)
    {
        return dramaRoomVipMapper.deleteDramaRoomVipByIds(ids);
    }

    /**
     * 删除vip管理信息
     *
     * @param id vip管理主键
     * @return 结果
     */
    @Override
    public int deleteDramaRoomVipById(Long id)
    {
        return dramaRoomVipMapper.deleteDramaRoomVipById(id);
    }

    @Override
    public List<DramaRoomVip> queryAllDramaRoomVip(DramaRoomVip dramaRoomVip) {
        return dramaRoomVipMapper.queryAllDramaRoomVip(dramaRoomVip);
    }
}
