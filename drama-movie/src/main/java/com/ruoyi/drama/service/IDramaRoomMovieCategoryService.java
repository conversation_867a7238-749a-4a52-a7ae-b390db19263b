package com.ruoyi.drama.service;

import java.util.List;

import com.ruoyi.drama.domain.DramaRoomMovie;
import com.ruoyi.drama.domain.DramaRoomMovieCategory;
import com.ruoyi.drama.domain.vo.DramaRoomMovieBindCategoryVO;

/**
 * 【请填写功能名称】Service接口
 *
 * <AUTHOR>
 * @date 2025-07-04
 */
public interface IDramaRoomMovieCategoryService
{
    /**
     * 查询【请填写功能名称】
     *
     * @param id 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    public DramaRoomMovieCategory selectDramaRoomMovieCategoryById(Long id);

    /**
     * 查询【请填写功能名称】列表
     *
     * @param dramaRoomMovieCategory 【请填写功能名称】
     * @return 【请填写功能名称】集合
     */
    public List<DramaRoomMovieCategory> selectDramaRoomMovieCategoryList(DramaRoomMovieCategory dramaRoomMovieCategory);

    /**
     * 新增【请填写功能名称】
     *
     * @param dramaRoomMovieCategory 【请填写功能名称】
     * @return 结果
     */
    public int insertDramaRoomMovieCategory(DramaRoomMovieCategory dramaRoomMovieCategory);

    /**
     * 修改【请填写功能名称】
     *
     * @param dramaRoomMovieCategory 【请填写功能名称】
     * @return 结果
     */
    public int updateDramaRoomMovieCategory(DramaRoomMovieCategory dramaRoomMovieCategory);

    /**
     * 批量删除【请填写功能名称】
     *
     * @param ids 需要删除的【请填写功能名称】主键集合
     * @return 结果
     */
    public int deleteDramaRoomMovieCategoryByIds(Long[] ids);

    /**
     * 删除【请填写功能名称】信息
     *
     * @param id 【请填写功能名称】主键
     * @return 结果
     */
    public int deleteDramaRoomMovieCategoryById(Long id);

    /**
     * 根据分类id查询分类下的所有电影
     * @param categoryId
     * @return
     */
    List<DramaRoomMovieBindCategoryVO> selectDramaRoomMovieCategoryListByCategoryId(Long categoryId);

    /**
     * 修改分类下的电影
     * @param categoryId
     * @param movieIds
     * @return
     */
    void updateBindMovies(Long categoryId, List<Long> movieIds);
}
