package com.ruoyi.drama.service;

import java.util.List;
import com.ruoyi.drama.domain.DramaRoomPayTemplateAmt;

/**
 * 金额模板Service接口
 * 
 * <AUTHOR>
 * @date 2025-08-13
 */
public interface IDramaRoomPayTemplateAmtService 
{
    /**
     * 查询金额模板
     * 
     * @param id 金额模板主键
     * @return 金额模板
     */
    public DramaRoomPayTemplateAmt selectDramaRoomPayTemplateAmtById(Long id);

    /**
     * 查询金额模板列表
     * 
     * @param dramaRoomPayTemplateAmt 金额模板
     * @return 金额模板集合
     */
    public List<DramaRoomPayTemplateAmt> selectDramaRoomPayTemplateAmtList(DramaRoomPayTemplateAmt dramaRoomPayTemplateAmt);

    /**
     * 新增金额模板
     * 
     * @param dramaRoomPayTemplateAmt 金额模板
     * @return 结果
     */
    public int insertDramaRoomPayTemplateAmt(DramaRoomPayTemplateAmt dramaRoomPayTemplateAmt);

    /**
     * 修改金额模板
     * 
     * @param dramaRoomPayTemplateAmt 金额模板
     * @return 结果
     */
    public int updateDramaRoomPayTemplateAmt(DramaRoomPayTemplateAmt dramaRoomPayTemplateAmt);

    /**
     * 批量删除金额模板
     * 
     * @param ids 需要删除的金额模板主键集合
     * @return 结果
     */
    public int deleteDramaRoomPayTemplateAmtByIds(Long[] ids);

    /**
     * 删除金额模板信息
     * 
     * @param id 金额模板主键
     * @return 结果
     */
    public int deleteDramaRoomPayTemplateAmtById(Long id);
}