package com.ruoyi.drama.service.impl;

import java.util.Collections;
import java.util.List;

import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.drama.domain.DramaRoomSysUserAppMovie;
import com.ruoyi.drama.mapper.DramaRoomSysUserAppMovieMapper;
import com.ruoyi.drama.service.IDramaRoomSysUserAppMovieService;
import com.ruoyi.system.mapper.SysUserPostMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 运营用户关联app，短剧Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-08-11
 */
@Service
public class DramaRoomSysUserAppMovieServiceImpl implements IDramaRoomSysUserAppMovieService
{
    @Autowired
    private DramaRoomSysUserAppMovieMapper dramaRoomSysUserAppMovieMapper;

    @Autowired
    private SysUserPostMapper sysUserPostMapper;


    /**
     * 查询运营用户关联app，短剧
     *
     * @param id 运营用户关联app，短剧主键
     * @return 运营用户关联app，短剧
     */
    @Override
    public DramaRoomSysUserAppMovie selectDramaRoomSysUserAppMovieById(Long id)
    {
        return dramaRoomSysUserAppMovieMapper.selectDramaRoomSysUserAppMovieById(id);
    }

    /**
     * 查询运营用户关联app，短剧列表
     *
     * @param dramaRoomSysUserAppMovie 运营用户关联app，短剧
     * @return 运营用户关联app，短剧
     */
    @Override
    public List<DramaRoomSysUserAppMovie> selectDramaRoomSysUserAppMovieList(DramaRoomSysUserAppMovie dramaRoomSysUserAppMovie)
    {
        return dramaRoomSysUserAppMovieMapper.selectDramaRoomSysUserAppMovieList(dramaRoomSysUserAppMovie);
    }

    /**
     * 新增运营用户关联app，短剧
     *
     * @param dramaRoomSysUserAppMovie 运营用户关联app，短剧
     * @return 结果
     */
    @Override
    public int insertDramaRoomSysUserAppMovie(DramaRoomSysUserAppMovie dramaRoomSysUserAppMovie)
    {
        dramaRoomSysUserAppMovie.setCreateTime(DateUtils.getNowDate());
        return dramaRoomSysUserAppMovieMapper.insertDramaRoomSysUserAppMovie(dramaRoomSysUserAppMovie);
    }

    /**
     * 修改运营用户关联app，短剧
     *
     * @param dramaRoomSysUserAppMovie 运营用户关联app，短剧
     * @return 结果
     */
    @Override
    public int updateDramaRoomSysUserAppMovie(DramaRoomSysUserAppMovie dramaRoomSysUserAppMovie)
    {
        dramaRoomSysUserAppMovie.setUpdateTime(DateUtils.getNowDate());
        return dramaRoomSysUserAppMovieMapper.updateDramaRoomSysUserAppMovie(dramaRoomSysUserAppMovie);
    }

    /**
     * 批量删除运营用户关联app，短剧
     *
     * @param ids 需要删除的运营用户关联app，短剧主键
     * @return 结果
     */
    @Override
    public int deleteDramaRoomSysUserAppMovieByIds(Long[] ids)
    {
        return dramaRoomSysUserAppMovieMapper.deleteDramaRoomSysUserAppMovieByIds(ids);
    }

    /**
     * 删除运营用户关联app，短剧信息
     *
     * @param id 运营用户关联app，短剧主键
     * @return 结果
     */
    @Override
    public int deleteDramaRoomSysUserAppMovieById(Long id)
    {
        return dramaRoomSysUserAppMovieMapper.deleteDramaRoomSysUserAppMovieById(id);
    }

    @Override
    public int addList(List<DramaRoomSysUserAppMovie> shortSysUserAppMovieList) {
        int res = 0;
        for (DramaRoomSysUserAppMovie sysUserAppMovie : shortSysUserAppMovieList) {
            sysUserAppMovie.setUpdateTime(DateUtils.getNowDate());
            res =dramaRoomSysUserAppMovieMapper.insertDramaRoomSysUserAppMovie(sysUserAppMovie);
        }
        return res;
    }

    @Override
    public List<SysUser> getChildUserList(String postId) {
        return dramaRoomSysUserAppMovieMapper.getChildUserList(postId);
    }

    @Override
    public int inherit(Long pId, Long cId) {
        sysUserPostMapper.updatePostIdByPIdAndCId(pId,cId);
        dramaRoomSysUserAppMovieMapper.updateSysUserIdByPIdAndCId(pId,cId);
        return  1/*shortSemLinkMapper.updateCreateByByPIdAndCId(pId,cId)*/;//修改link 创建人
    }

}