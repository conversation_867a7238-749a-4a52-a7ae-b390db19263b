package com.ruoyi.drama.service;

import java.util.List;
import com.ruoyi.drama.domain.DramaRoomMovieTag;
import com.ruoyi.drama.domain.vo.DramaRoomMovieBindTagVO;

/**
 * 短剧标签关联Service接口
 *
 * <AUTHOR>
 * @date 2025-07-04
 */
public interface IDramaRoomMovieTagService
{
    /**
     * 查询短剧标签关联
     *
     * @param id 短剧标签关联主键
     * @return 短剧标签关联
     */
    public DramaRoomMovieTag selectDramaRoomMovieTagById(Long id);

    /**
     * 查询短剧标签关联列表
     *
     * @param DramaRoomMovieTag 短剧标签关联
     * @return 短剧标签关联集合
     */
    public List<DramaRoomMovieTag> selectDramaRoomMovieTagList(DramaRoomMovieTag DramaRoomMovieTag);

    /**
     * 新增短剧标签关联
     *
     * @param DramaRoomMovieTag 短剧标签关联
     * @return 结果
     */
    public int insertDramaRoomMovieTag(DramaRoomMovieTag DramaRoomMovieTag);

    /**
     * 修改短剧标签关联
     *
     * @param DramaRoomMovieTag 短剧标签关联
     * @return 结果
     */
    public int updateDramaRoomMovieTag(DramaRoomMovieTag DramaRoomMovieTag);

    /**
     * 批量删除短剧标签关联
     *
     * @param ids 需要删除的短剧标签关联主键集合
     * @return 结果
     */
    public int deleteDramaRoomMovieTagByIds(Long[] ids);

    /**
     * 删除短剧标签关联信息
     *
     * @param id 短剧标签关联主键
     * @return 结果
     */
    public int deleteDramaRoomMovieTagById(Long id);

    /**
     * 根据标签id查询所有绑定的短剧
     *
     * @param tagId 标签id
     * @return 短剧列表
     */
    List<DramaRoomMovieBindTagVO> selectDramaRoomMovieTagListByCategoryId(Long tagId);

    /**
     * 根据标签id更新所有绑定的短剧
     *
     * @param tagId 标签id
     * @param movieIds 短剧id列表
     */
    void updateBindMovies(Long tagId, List<Long> movieIds);
}
