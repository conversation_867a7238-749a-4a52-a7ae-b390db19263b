package com.ruoyi.drama.service.impl;

import java.util.List;

import com.ruoyi.common.enums.AppUserActionEnum;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.mobile.AppSecurityUtils;
import com.ruoyi.drama.domain.DramaRoomVideo;
import com.ruoyi.drama.manager.MovieAsyncManager;
import com.ruoyi.drama.manager.factory.AppUserAsyncFactory;
import com.ruoyi.drama.mapper.DramaRoomVideoMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.drama.mapper.DramaRoomUserLikeMapper;
import com.ruoyi.drama.domain.DramaRoomUserLike;
import com.ruoyi.drama.service.IDramaRoomUserLikeService;
import org.springframework.util.CollectionUtils;

/**
 * 用户点赞记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-08
 */
@Service
public class DramaRoomUserLikeServiceImpl implements IDramaRoomUserLikeService
{
    @Autowired
    private DramaRoomUserLikeMapper dramaRoomUserLikeMapper;

    @Autowired
    private DramaRoomVideoMapper dramaRoomVideoMapper;

    /**
     * 查询用户点赞记录
     *
     * @param id 用户点赞记录主键
     * @return 用户点赞记录
     */
    @Override
    public DramaRoomUserLike selectDramaRoomUserLikeById(Long id)
    {
        return dramaRoomUserLikeMapper.selectDramaRoomUserLikeById(id);
    }

    /**
     * 查询用户点赞记录列表
     *
     * @param dramaRoomUserLike 用户点赞记录
     * @return 用户点赞记录
     */
    @Override
    public List<DramaRoomUserLike> selectDramaRoomUserLikeList(DramaRoomUserLike dramaRoomUserLike)
    {
        return dramaRoomUserLikeMapper.selectDramaRoomUserLikeList(dramaRoomUserLike);
    }

    /**
     * 新增用户点赞记录
     *
     * @param dramaRoomUserLike 用户点赞记录
     * @return 结果
     */
    @Override
    public int insertDramaRoomUserLike(DramaRoomUserLike dramaRoomUserLike)
    {
        dramaRoomUserLike.setCreateTime(DateUtils.getNowDate());
        return dramaRoomUserLikeMapper.insertDramaRoomUserLike(dramaRoomUserLike);
    }

    /**
     * 修改用户点赞记录
     *
     * @param dramaRoomUserLike 用户点赞记录
     * @return 结果
     */
    @Override
    public int updateDramaRoomUserLike(DramaRoomUserLike dramaRoomUserLike)
    {
        dramaRoomUserLike.setUpdateTime(DateUtils.getNowDate());
        return dramaRoomUserLikeMapper.updateDramaRoomUserLike(dramaRoomUserLike);
    }

    /**
     * 批量删除用户点赞记录
     *
     * @param ids 需要删除的用户点赞记录主键
     * @return 结果
     */
    @Override
    public int deleteDramaRoomUserLikeByIds(Long[] ids)
    {
        return dramaRoomUserLikeMapper.deleteDramaRoomUserLikeByIds(ids);
    }

    /**
     * 删除用户点赞记录信息
     *
     * @param id 用户点赞记录主键
     * @return 结果
     */
    @Override
    public int deleteDramaRoomUserLikeById(Long id)
    {
        return dramaRoomUserLikeMapper.deleteDramaRoomUserLikeById(id);
    }

    @Override
    public int likeVideo(Long videoId) {

        //查询用户是否已经点赞
        DramaRoomUserLike dramaRoomUserLikeQuery = new DramaRoomUserLike();
        dramaRoomUserLikeQuery.setVideoId(videoId);
        List<DramaRoomUserLike> dramaRoomUserLikeList = dramaRoomUserLikeMapper.selectDramaRoomUserLikeList(dramaRoomUserLikeQuery);
        if(CollectionUtils.isEmpty(dramaRoomUserLikeList)){
            //查询剧集是存在
            DramaRoomVideo video = dramaRoomVideoMapper.selectDramaRoomVideoById(videoId);
            if(null ==  video){
                return 0;
            }
            DramaRoomUserLike dramaRoomUserLike = new DramaRoomUserLike();
            dramaRoomUserLike.setVideoId(videoId);
            dramaRoomUserLike.setMovieId(video.getMovieId());
            dramaRoomUserLike.setUserId(AppSecurityUtils.getAppUserId());
            dramaRoomUserLike.setStatus(1L);
            dramaRoomUserLike.setCreateTime(DateUtils.getNowDate());
            MovieAsyncManager.me().execute(AppUserAsyncFactory.updateDramaActionCount(video.getMovieId(), 1L, AppUserActionEnum.LIKE));
            return dramaRoomUserLikeMapper.insertDramaRoomUserLike(dramaRoomUserLike);
        }else {
            //已经点赞过
            DramaRoomUserLike existRecord = dramaRoomUserLikeList.get(0);
            existRecord.setStatus(existRecord.getStatus() == 0L ? 1L : 0L);
            MovieAsyncManager.me().execute(AppUserAsyncFactory.updateDramaActionCount(existRecord.getMovieId(), existRecord.getStatus() == 0L ? 1L : -1L, AppUserActionEnum.LIKE));
            existRecord.setUpdateTime(DateUtils.getNowDate());
            return dramaRoomUserLikeMapper.updateDramaRoomUserLike(existRecord);
        }
    }
}
