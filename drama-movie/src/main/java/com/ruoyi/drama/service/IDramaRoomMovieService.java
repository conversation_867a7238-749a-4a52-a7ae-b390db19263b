package com.ruoyi.drama.service;

import com.ruoyi.common.drama.dto.DramaRoomMovieQueryDTO;
import com.ruoyi.common.drama.dto.DramaRoomMovieUnlockVideoDTO;
import com.ruoyi.common.drama.vo.MovieDetailVO;
import com.ruoyi.common.drama.vo.MovieVO;
import com.ruoyi.drama.domain.DramaRoomMovie;
import software.amazon.awssdk.services.s3.endpoints.internal.Value;

import javax.validation.Valid;
import java.util.List;

/**
 * 短剧Service接口
 *
 * <AUTHOR>
 * @date 2025-07-04
 */
public interface IDramaRoomMovieService
{
    /**
     * 查询短剧
     *
     * @param id 短剧主键
     * @return 短剧
     */
    public DramaRoomMovie selectDramaRoomMovieById(Long id);

    /**
     * 查询短剧列表
     *
     * @return 短剧集合
     */
    public List<DramaRoomMovie> selectDramaRoomMovieList(DramaRoomMovie dramaRoomMovie);

    /**
     * 新增短剧
     *
     * @param dramaRoomMovie 短剧
     * @return 结果
     */
    public int insertDramaRoomMovie(DramaRoomMovie dramaRoomMovie);

    /**
     * 修改短剧
     *
     * @param dramaRoomMovie 短剧
     * @return 结果
     */
    public int updateDramaRoomMovie(DramaRoomMovie dramaRoomMovie);

    /**
     * 批量删除短剧
     *
     * @param ids 需要删除的短剧主键集合
     * @return 结果
     */
    public int deleteDramaRoomMovieByIds(Long[] ids);

    /**
     * 删除短剧信息
     *
     * @param id 短剧主键
     * @return 结果
     */
    public int deleteDramaRoomMovieById(Long id);

    /**
     * 获取短剧详情
     * @param id
     * @return
     */
    MovieDetailVO getMovieDetail(Long id, String languageCode);

    /**
     * 推荐页条件查询短剧
     * @param queryDTO
     * @return
     */
    List<DramaRoomMovie> queryMovie(DramaRoomMovieQueryDTO queryDTO, String languageCode);

    /**
     * 获取热播榜
     * @return
     */
    List<MovieVO> getHotRecommend(String languageCode);

    /**
     * 获取新番
     * @return
     */
    List<MovieVO> getNewList(String languageCode);

    /**
     * 获取热搜榜
     * @return
     */
    List<MovieVO> getHotSearch(String languageCode);

    List<DramaRoomMovie> queryByMovieName(String name);
}
