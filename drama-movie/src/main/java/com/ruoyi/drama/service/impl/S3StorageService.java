package com.ruoyi.drama.service.impl;

import org.apache.tika.Tika;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import software.amazon.awssdk.core.sync.RequestBody;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.model.PutObjectRequest;
import software.amazon.awssdk.services.s3.model.PutObjectResponse;
import software.amazon.awssdk.services.s3.model.S3Exception;

import java.io.IOException;
import java.util.UUID;

@Service
public class S3StorageService {

    private static final Logger log = LoggerFactory.getLogger(S3StorageService.class);

    @Autowired
    private S3Client s3Client;

    @Value("${cloud.r2.bucket-name}")
    private String bucketName;

    @Value("${cloud.r2.file-prefix}")
    private String filePrefix;

    /**
     * 上传文件到 S3
     *
     * @param file MultipartFile 文件对象
     * @return 上传成功后的 ETag
     */
    public String uploadFile(MultipartFile file) throws Exception {
        //访问链接
        String responseUrl = "";
        //objectKey 对象键（文件在 S3 中的路径和名称）
        String objectKey = "uploads/" + UUID.randomUUID().toString() + "_" + file.getOriginalFilename();

        // 检查图片格式
        String imageType = detectImageType(file);
        if (imageType == null) {
            throw new Exception("仅支持JPG/PNG/JPEG/WEBP/GIF格式");
        }

        // 2. 处理图片
        byte[] processedImage = ImageProcessor.processImage(file.getBytes(), imageType);

        try {
            PutObjectRequest putObjectRequest = PutObjectRequest.builder()
                    .bucket(bucketName)
                    .key(objectKey)
                    .contentType(imageType)
                    .contentDisposition("inline")            // ✅ 指定为 inline，防止下载
                    .build();

            PutObjectResponse response = s3Client.putObject(
                    putObjectRequest,
                    RequestBody.fromBytes(processedImage)
            );
            //判断文件是否上传成功
            if (response.sdkHttpResponse().isSuccessful()) {
                log.debug("文件上传成功，构建访问链接");
                responseUrl = filePrefix + objectKey;
            }
            return responseUrl;
        } catch (S3Exception e) {
            throw new RuntimeException("Failed to read the file", e);
        }
    }

    public String uploadFileFilter(MultipartFile file) throws Exception {
        //访问链接
        String responseUrl = "";
        //objectKey 对象键（文件在 S3 中的路径和名称）
        String objectKey = "uploads/" + UUID.randomUUID().toString() + "_" + file.getOriginalFilename();

        // 检查图片格式
        String imageType = detectImageType(file);
        if (imageType == null) {
            return null;
        }

        // 2. 处理图片
        byte[] processedImage = ImageProcessor.processImage(file.getBytes(), imageType);

        try {
            PutObjectRequest putObjectRequest = PutObjectRequest.builder()
                    .bucket(bucketName)
                    .key(objectKey)
                    .contentType(imageType)
                    .contentDisposition("inline")            // ✅ 指定为 inline，防止下载
                    .build();

            PutObjectResponse response = s3Client.putObject(
                    putObjectRequest,
                    RequestBody.fromBytes(processedImage)
            );
            //判断文件是否上传成功
            if (response.sdkHttpResponse().isSuccessful()) {
                log.debug("文件上传成功，构建访问链接");
                responseUrl = filePrefix + objectKey;
            }
            return responseUrl;
        } catch (S3Exception e) {
            return null;
        }
    }

    public  String detectImageType(MultipartFile file) throws IOException {
        Tika tika = new Tika();
        String mimeType = tika.detect(file.getInputStream());

        switch (mimeType) {
            case "image/jpeg":
                return "jpg";
            case "image/png":
                return "png";
            case "image/gif":
                return "gif";
            case "image/webp":
                return "webp";
            default:
                return null;
        }
    }

}
