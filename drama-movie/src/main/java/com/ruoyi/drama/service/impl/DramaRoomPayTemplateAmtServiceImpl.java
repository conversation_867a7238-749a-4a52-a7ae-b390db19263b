package com.ruoyi.drama.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.drama.mapper.DramaRoomPayTemplateAmtMapper;
import com.ruoyi.drama.domain.DramaRoomPayTemplateAmt;
import com.ruoyi.drama.service.IDramaRoomPayTemplateAmtService;

/**
 * 金额模板Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-08-13
 */
@Service
public class DramaRoomPayTemplateAmtServiceImpl implements IDramaRoomPayTemplateAmtService 
{
    @Autowired
    private DramaRoomPayTemplateAmtMapper dramaRoomPayTemplateAmtMapper;

    /**
     * 查询金额模板
     * 
     * @param id 金额模板主键
     * @return 金额模板
     */
    @Override
    public DramaRoomPayTemplateAmt selectDramaRoomPayTemplateAmtById(Long id)
    {
        return dramaRoomPayTemplateAmtMapper.selectDramaRoomPayTemplateAmtById(id);
    }

    /**
     * 查询金额模板列表
     * 
     * @param dramaRoomPayTemplateAmt 金额模板
     * @return 金额模板
     */
    @Override
    public List<DramaRoomPayTemplateAmt> selectDramaRoomPayTemplateAmtList(DramaRoomPayTemplateAmt dramaRoomPayTemplateAmt)
    {
        return dramaRoomPayTemplateAmtMapper.selectDramaRoomPayTemplateAmtList(dramaRoomPayTemplateAmt);
    }

    /**
     * 新增金额模板
     * 
     * @param dramaRoomPayTemplateAmt 金额模板
     * @return 结果
     */
    @Override
    public int insertDramaRoomPayTemplateAmt(DramaRoomPayTemplateAmt dramaRoomPayTemplateAmt)
    {
        dramaRoomPayTemplateAmt.setCreateTime(DateUtils.getNowDate());
        return dramaRoomPayTemplateAmtMapper.insertDramaRoomPayTemplateAmt(dramaRoomPayTemplateAmt);
    }

    /**
     * 修改金额模板
     * 
     * @param dramaRoomPayTemplateAmt 金额模板
     * @return 结果
     */
    @Override
    public int updateDramaRoomPayTemplateAmt(DramaRoomPayTemplateAmt dramaRoomPayTemplateAmt)
    {
        dramaRoomPayTemplateAmt.setUpdateTime(DateUtils.getNowDate());
        return dramaRoomPayTemplateAmtMapper.updateDramaRoomPayTemplateAmt(dramaRoomPayTemplateAmt);
    }

    /**
     * 批量删除金额模板
     * 
     * @param ids 需要删除的金额模板主键
     * @return 结果
     */
    @Override
    public int deleteDramaRoomPayTemplateAmtByIds(Long[] ids)
    {
        return dramaRoomPayTemplateAmtMapper.deleteDramaRoomPayTemplateAmtByIds(ids);
    }

    /**
     * 删除金额模板信息
     * 
     * @param id 金额模板主键
     * @return 结果
     */
    @Override
    public int deleteDramaRoomPayTemplateAmtById(Long id)
    {
        return dramaRoomPayTemplateAmtMapper.deleteDramaRoomPayTemplateAmtById(id);
    }
}