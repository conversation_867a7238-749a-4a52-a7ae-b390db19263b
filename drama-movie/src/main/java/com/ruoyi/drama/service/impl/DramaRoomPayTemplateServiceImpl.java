package com.ruoyi.drama.service.impl;

import java.util.List;
import java.util.stream.Collectors;

import cn.hutool.core.collection.CollectionUtil;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.drama.mapper.DramaRoomPayTemplateMapper;
import com.ruoyi.drama.domain.DramaRoomPayTemplate;
import com.ruoyi.drama.service.IDramaRoomPayTemplateService;

/**
 * 付费模板Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-08-13
 */
@Service
public class DramaRoomPayTemplateServiceImpl implements IDramaRoomPayTemplateService 
{
    @Autowired
    private DramaRoomPayTemplateMapper dramaRoomPayTemplateMapper;

    /**
     * 查询付费模板
     * 
     * @param id 付费模板主键
     * @return 付费模板
     */
    @Override
    public DramaRoomPayTemplate selectDramaRoomPayTemplateById(Long id)
    {
        return dramaRoomPayTemplateMapper.selectDramaRoomPayTemplateById(id);
    }

    /**
     * 查询付费模板列表
     * 
     * @param dramaRoomPayTemplate 付费模板
     * @return 付费模板
     */
    @Override
    public List<DramaRoomPayTemplate> selectDramaRoomPayTemplateList(DramaRoomPayTemplate dramaRoomPayTemplate)
    {
        return dramaRoomPayTemplateMapper.selectDramaRoomPayTemplateList(dramaRoomPayTemplate);
    }

    /**
     * 新增付费模板
     * 
     * @param dramaRoomPayTemplate 付费模板
     * @return 结果
     */
    @Override
    public int insertDramaRoomPayTemplate(DramaRoomPayTemplate dramaRoomPayTemplate)
    {
        dramaRoomPayTemplate.setCreateTime(DateUtils.getNowDate());
        //查询付费模板名称是否已存在
        DramaRoomPayTemplate dramaRoomPayTemplateQuery = new DramaRoomPayTemplate();
        dramaRoomPayTemplateQuery.setName(dramaRoomPayTemplate.getName());
        List<DramaRoomPayTemplate> list = dramaRoomPayTemplateMapper.selectDramaRoomPayTemplateList(dramaRoomPayTemplateQuery);
        if(CollectionUtil.isNotEmpty(list)){
            throw new RuntimeException("付费模板名称已存在");
        }
        return dramaRoomPayTemplateMapper.insertDramaRoomPayTemplate(dramaRoomPayTemplate);
    }

    /**
     * 修改付费模板
     * 
     * @param dramaRoomPayTemplate 付费模板
     * @return 结果
     */
    @Override
    public int updateDramaRoomPayTemplate(DramaRoomPayTemplate dramaRoomPayTemplate)
    {
        dramaRoomPayTemplate.setUpdateTime(DateUtils.getNowDate());
        DramaRoomPayTemplate dramaRoomPayTemplateQuery = new DramaRoomPayTemplate();
        dramaRoomPayTemplateQuery.setName(dramaRoomPayTemplate.getName());
        List<DramaRoomPayTemplate> list = dramaRoomPayTemplateMapper.selectDramaRoomPayTemplateList(dramaRoomPayTemplateQuery);
        if(CollectionUtil.isNotEmpty(list)){
            List<DramaRoomPayTemplate> collect = list.stream().filter(item -> !item.getId().equals(dramaRoomPayTemplate.getId())).collect(Collectors.toList());
            if(CollectionUtil.isNotEmpty(collect)){
                throw new RuntimeException("付费模板名称已存在");
            }
        }
        return dramaRoomPayTemplateMapper.updateDramaRoomPayTemplate(dramaRoomPayTemplate);
    }

    /**
     * 批量删除付费模板
     * 
     * @param ids 需要删除的付费模板主键
     * @return 结果
     */
    @Override
    public int deleteDramaRoomPayTemplateByIds(Long[] ids)
    {
        return dramaRoomPayTemplateMapper.deleteDramaRoomPayTemplateByIds(ids);
    }

    /**
     * 删除付费模板信息
     * 
     * @param id 付费模板主键
     * @return 结果
     */
    @Override
    public int deleteDramaRoomPayTemplateById(Long id)
    {
        return dramaRoomPayTemplateMapper.deleteDramaRoomPayTemplateById(id);
    }
}