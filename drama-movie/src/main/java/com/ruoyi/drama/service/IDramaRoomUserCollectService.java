package com.ruoyi.drama.service;

import java.util.List;

import com.ruoyi.common.drama.dto.DramaRoomUserCollectDTO;
import com.ruoyi.common.drama.dto.DramaRoomUserHistoryDTO;
import com.ruoyi.common.drama.vo.UserCollectVO;
import com.ruoyi.common.drama.vo.UserHistoryVO;
import com.ruoyi.drama.domain.DramaRoomUserCollect;

/**
 * 【请填写功能名称】Service接口
 *
 * <AUTHOR>
 * @date 2025-07-04
 */
public interface IDramaRoomUserCollectService
{
    /**
     * 查询【请填写功能名称】
     *
     * @param id 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    public DramaRoomUserCollect selectDramaRoomUserCollectById(Long id);

    /**
     * 查询【请填写功能名称】列表
     *
     * @param dramaRoomUserFavorite 【请填写功能名称】
     * @return 【请填写功能名称】集合
     */
    public List<DramaRoomUserCollect> selectDramaRoomUserCollectList(DramaRoomUserCollect dramaRoomUserFavorite);

    /**
     * 新增【请填写功能名称】
     *
     * @param dramaRoomUserFavorite 【请填写功能名称】
     * @return 结果
     */
    public int insertDramaRoomUserCollect(DramaRoomUserCollect dramaRoomUserFavorite);

    /**
     * 修改【请填写功能名称】
     *
     * @param dramaRoomUserFavorite 【请填写功能名称】
     * @return 结果
     */
    public int updateDramaRoomUserCollect(DramaRoomUserCollect dramaRoomUserFavorite);

    /**
     * 批量删除【请填写功能名称】
     *
     * @param ids 需要删除的【请填写功能名称】主键集合
     * @return 结果
     */
    public int deleteDramaRoomUserCollectByIds(Long[] ids);

    /**
     * 删除【请填写功能名称】信息
     *
     * @param id 【请填写功能名称】主键
     * @return 结果
     */
    public int deleteDramaRoomUserCollectById(Long id);

    int collectMovie(DramaRoomUserCollectDTO dramaRoomUserFavoriteDTO);

    List<UserCollectVO> getUserCollect(String languageCode);

}
