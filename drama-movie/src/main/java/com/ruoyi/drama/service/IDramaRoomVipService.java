package com.ruoyi.drama.service;

import java.util.List;
import com.ruoyi.drama.domain.DramaRoomVip;

/**
 * vip管理Service接口
 * 
 * <AUTHOR>
 * @date 2025-08-11
 */
public interface IDramaRoomVipService 
{
    /**
     * 查询vip管理
     * 
     * @param id vip管理主键
     * @return vip管理
     */
    public DramaRoomVip selectDramaRoomVipById(Long id);

    /**
     * 查询vip管理列表
     * 
     * @param dramaRoomVip vip管理
     * @return vip管理集合
     */
    public List<DramaRoomVip> selectDramaRoomVipList(DramaRoomVip dramaRoomVip);

    /**
     * 新增vip管理
     * 
     * @param dramaRoomVip vip管理
     * @return 结果
     */
    public int insertDramaRoomVip(DramaRoomVip dramaRoomVip);

    /**
     * 修改vip管理
     * 
     * @param dramaRoomVip vip管理
     * @return 结果
     */
    public int updateDramaRoomVip(DramaRoomVip dramaRoomVip);

    /**
     * 批量删除vip管理
     * 
     * @param ids 需要删除的vip管理主键集合
     * @return 结果
     */
    public int deleteDramaRoomVipByIds(Long[] ids);

    /**
     * 删除vip管理信息
     * 
     * @param id vip管理主键
     * @return 结果
     */
    public int deleteDramaRoomVipById(Long id);

    List<DramaRoomVip> queryAllDramaRoomVip(DramaRoomVip dramaRoomVip);

}