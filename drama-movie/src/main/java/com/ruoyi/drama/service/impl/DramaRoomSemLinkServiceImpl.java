package com.ruoyi.drama.service.impl;

import java.util.List;
import java.util.UUID;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import cn.hutool.core.collection.CollectionUtil;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.drama.domain.DramaRoomPayTemplate;
import com.ruoyi.drama.domain.DramaRoomVip;
import com.ruoyi.drama.domain.vo.DramaRoomSemLinkVO;
import com.ruoyi.drama.mapper.DramaRoomPayTemplateMapper;
import com.ruoyi.drama.mapper.DramaRoomVipMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.drama.mapper.DramaRoomSemLinkMapper;
import com.ruoyi.drama.domain.DramaRoomSemLink;
import com.ruoyi.drama.service.IDramaRoomSemLinkService;

import static org.apache.commons.lang3.SystemUtils.getUserName;

/**
 * 推广链接Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-08-11
 */
@Service
public class DramaRoomSemLinkServiceImpl implements IDramaRoomSemLinkService 
{
    @Autowired
    private DramaRoomSemLinkMapper dramaRoomSemLinkMapper;

    @Autowired
    private DramaRoomPayTemplateMapper dramaRoomPayTemplateMapper;

    @Autowired
    private DramaRoomVipMapper dramaRoomVipMapper;

    /**
     * 查询推广链接
     * 
     * @param id 推广链接主键
     * @return 推广链接
     */
    @Override
    public DramaRoomSemLink selectDramaRoomSemLinkById(Long id)
    {
        return dramaRoomSemLinkMapper.selectDramaRoomSemLinkById(id);
    }

    /**
     * 查询推广链接列表
     * 
     * @param dramaRoomSemLink 推广链接
     * @return 推广链接
     */
    @Override
    public List<DramaRoomSemLink> selectDramaRoomSemLinkList(DramaRoomSemLink dramaRoomSemLink)
    {
        return dramaRoomSemLinkMapper.selectDramaRoomSemLinkList(dramaRoomSemLink);
    }

    /**
     * 新增推广链接
     * 
     * @param dramaRoomSemLink 推广链接
     * @return 结果
     */
    @Override
    public int insertDramaRoomSemLink(DramaRoomSemLink dramaRoomSemLink)
    {
        String appId = "1";
        dramaRoomSemLink.setCreateBy(SecurityUtils.getUserId().toString());
        dramaRoomSemLink.setCreateTime(DateUtils.getNowDate());
        String pdata = "?qd=" + dramaRoomSemLink.getPlatform() + "&mid=" + dramaRoomSemLink.getMovieId() + "&pid=" + dramaRoomSemLink.getPayTemplateId() + "&kid=" + dramaRoomSemLink.getVideoCoinPlanId();
        dramaRoomSemLink.setPdata(pdata);
        //先执行入库
        dramaRoomSemLinkMapper.insertDramaRoomSemLink(dramaRoomSemLink);
        Long linkId = dramaRoomSemLink.getId();
        Long facebookPixelId = dramaRoomSemLink.getPixelId();
        String language = dramaRoomSemLink.getLanguage();
        String link = "?p0=1mgdyduu&p1={{campaign.name}}&p2={{campaign.id}}&p3={{adset.name}}&p4={{adset.id}}&p5={{ad.name}}&p6={{ad.id}}&linkid=" + linkId + "&pixel_id=" + facebookPixelId + "&language=" + language;
        dramaRoomSemLink.setLink(link);
        // 生成十位随机uuid
        String uuid = UUID.randomUUID().toString().replace("-", "").substring(0, 10);
        dramaRoomSemLink.setUrl(uuid);
        //设置linkName
        String name = dramaRoomSemLink.getName();
        String linkName = "";
        String userName = getUserName();
        //根据userId 判断是否是组长 如果是组长取原名 如果是组员则取组长名
        if (StringUtils.isNotEmpty(name)) {
            linkName = appId + "-" + dramaRoomSemLink.getMovieId() + "-" + linkId + "-" + DateUtils.dateTime() + "-" + userName + "-" + name;
        } else {
            linkName = appId + "-" + dramaRoomSemLink.getMovieId() + "-" + linkId + "-" + DateUtils.dateTime() + "-" + userName;
        }
        dramaRoomSemLink.setName(linkName);
        //检测选中的付费模板 是否配置有付费计划
        Long payTemplateId = dramaRoomSemLink.getPayTemplateId();
        if (!checkPayTemplateAndVip(payTemplateId)) {
            throw new RuntimeException("付费模板未配置付费计划");
        }
        return dramaRoomSemLinkMapper.updateDramaRoomSemLink(dramaRoomSemLink);
    }

    private Boolean checkPayTemplateAndVip(Long payTemplateId) {
        DramaRoomPayTemplate dramaRoomPayTemplate = dramaRoomPayTemplateMapper.selectDramaRoomPayTemplateById(payTemplateId);
        if (null == dramaRoomPayTemplate) {
            return false;
        }
        DramaRoomVip query = new DramaRoomVip();
        query.setPayTemplateId(payTemplateId);
        List<DramaRoomVip> list = dramaRoomVipMapper.selectDramaRoomVipList(query);
        return CollectionUtil.isNotEmpty(list);
    }

    /**
     * 修改推广链接
     * 
     * @param dramaRoomSemLink 推广链接
     * @return 结果
     */
    @Override
    public int updateDramaRoomSemLink(DramaRoomSemLink dramaRoomSemLink)
    {
        String appId = "1";
        dramaRoomSemLink.setUpdateTime(DateUtils.getNowDate());
        dramaRoomSemLink.setUpdateBy(SecurityUtils.getUserId().toString());
        Long linkId = dramaRoomSemLink.getId();
        //可能会修改app平台 导致link链接不一样
        String pdata = "?qd=" + dramaRoomSemLink.getPlatform() + "&mid=" + dramaRoomSemLink.getMovieId() + "&pid=" + dramaRoomSemLink.getPayTemplateId() + "&kid=" + dramaRoomSemLink.getVideoCoinPlanId();
        dramaRoomSemLink.setPdata(pdata);
        //ShortFacebookPixel shortFacebookPixel = shortFacebookPixelMapper.selectShortFacebookPixelById(shortSemLink.getPixelId());
        String link = "?p0=1mgdyduu&p1={{campaign.name}}&p2={{campaign.id}}&p3={{adset.name}}&p4={{adset.id}}&p5={{ad.name}}&p6={{ad.id}}&linkid=" + dramaRoomSemLink.getId() + "&pixel_id=" + dramaRoomSemLink.getPixelId() + "&language=" + dramaRoomSemLink.getLanguage();
        dramaRoomSemLink.setLink(link);
        //设置linkName
        String name = dramaRoomSemLink.getName();
        String userName = getUserName();
        //正则匹配
        Pattern pattern = Pattern.compile("^(([^-\\s]+-){4,})");
        Matcher matcher = pattern.matcher(name);
        if (matcher.find()) {
            dramaRoomSemLink.setName(name);
        }else {
            String linkName = "";
            if (StringUtils.isNotEmpty(name)) {
                linkName = appId + "-" + dramaRoomSemLink.getMovieId() + "-" + linkId + "-" + DateUtils.dateTime() + "-" + userName + "-" + name;
            } else {
                linkName = appId + "-" + dramaRoomSemLink.getMovieId() + "-" + linkId + "-" + DateUtils.dateTime() + "-" + userName;
            }
            dramaRoomSemLink.setName(linkName);
        }
        //检测选中的付费模板 是否配置有付费计划
        Long payTemplateId = dramaRoomSemLink.getPayTemplateId();
        if (!checkPayTemplateAndVip(payTemplateId)) {
            throw new RuntimeException("付费模板未配置付费计划");
        }
        return dramaRoomSemLinkMapper.updateDramaRoomSemLink(dramaRoomSemLink);
    }

    /**
     * 批量删除推广链接
     * 
     * @param ids 需要删除的推广链接主键
     * @return 结果
     */
    @Override
    public int deleteDramaRoomSemLinkByIds(Long[] ids)
    {
        return dramaRoomSemLinkMapper.deleteDramaRoomSemLinkByIds(ids);
    }

    /**
     * 删除推广链接信息
     * 
     * @param id 推广链接主键
     * @return 结果
     */
    @Override
    public int deleteDramaRoomSemLinkById(Long id)
    {
        return dramaRoomSemLinkMapper.deleteDramaRoomSemLinkById(id);
    }

    @Override
    public List<DramaRoomSemLinkVO> queryDramaRoomSemLink(DramaRoomSemLink dramaRoomSemLink) {
        return dramaRoomSemLinkMapper.queryDramaRoomSemLink(dramaRoomSemLink);
    }
}