package com.ruoyi.drama.service;

import java.util.List;

import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.drama.dto.DramaRoomGeneralI18nDTO;
import com.ruoyi.drama.domain.DramaRoomCategoryI18n;

/**
 * 分类多语言Service接口
 * 
 * <AUTHOR>
 * @date 2025-08-05
 */
public interface IDramaRoomCategoryI18nService 
{
    /**
     * 查询分类多语言
     * 
     * @param id 分类多语言主键
     * @return 分类多语言
     */
    public DramaRoomCategoryI18n selectDramaRoomCategoryI18nById(Long id);

    /**
     * 查询分类多语言列表
     * 
     * @param dramaRoomCategoryI18n 分类多语言
     * @return 分类多语言集合
     */
    public List<DramaRoomCategoryI18n> selectDramaRoomCategoryI18nList(DramaRoomCategoryI18n dramaRoomCategoryI18n);

    /**
     * 新增分类多语言
     * 
     * @param dramaRoomCategoryI18n 分类多语言
     * @return 结果
     */
    public int insertDramaRoomCategoryI18n(DramaRoomCategoryI18n dramaRoomCategoryI18n);

    /**
     * 修改分类多语言
     * 
     * @param dramaRoomCategoryI18n 分类多语言
     * @return 结果
     */
    public int updateDramaRoomCategoryI18n(DramaRoomCategoryI18n dramaRoomCategoryI18n);

    /**
     * 批量删除分类多语言
     * 
     * @param ids 需要删除的分类多语言主键集合
     * @return 结果
     */
    public int deleteDramaRoomCategoryI18nByIds(Long[] ids);

    /**
     * 删除分类多语言信息
     * 
     * @param id 分类多语言主键
     * @return 结果
     */
    public int deleteDramaRoomCategoryI18nById(Long id);

    /**
     * 批量插入多语言
     * @param i18ns
     */
    void insertDramaRoomCategoryI18nList(List<DramaRoomCategoryI18n> i18ns);

    /**
     * 单语种翻译
     * @param dto
     * @return
     */
    AjaxResult translate(DramaRoomGeneralI18nDTO dto);

    /**
     * 需要翻译列表
     * @param id
     * @return
     */
    AjaxResult listTranslate(Long id);
}
