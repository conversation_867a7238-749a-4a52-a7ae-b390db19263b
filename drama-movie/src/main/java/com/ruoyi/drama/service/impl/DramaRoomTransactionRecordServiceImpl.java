package com.ruoyi.drama.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.drama.domain.DramaRoomTransactionRecord;
import com.ruoyi.drama.mapper.DramaRoomTransactionRecordMapper;
import com.ruoyi.drama.service.IDramaRoomTransactionRecordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 交易记录（包含支付和退款）Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-08-08
 */
@Service
public class DramaRoomTransactionRecordServiceImpl implements IDramaRoomTransactionRecordService
{
    @Autowired
    private DramaRoomTransactionRecordMapper dramaRoomTransactionRecordMapper;

    /**
     * 查询交易记录（包含支付和退款）
     * 
     * @param id 交易记录（包含支付和退款）主键
     * @return 交易记录（包含支付和退款）
     */
    @Override
    public DramaRoomTransactionRecord selectDramaRoomTransactionRecordById(Long id)
    {
        return dramaRoomTransactionRecordMapper.selectDramaRoomTransactionRecordById(id);
    }

    /**
     * 查询交易记录（包含支付和退款）列表
     * 
     * @param dramaRoomTransactionRecord 交易记录（包含支付和退款）
     * @return 交易记录（包含支付和退款）
     */
    @Override
    public List<DramaRoomTransactionRecord> selectDramaRoomTransactionRecordList(DramaRoomTransactionRecord dramaRoomTransactionRecord)
    {
        return dramaRoomTransactionRecordMapper.selectDramaRoomTransactionRecordList(dramaRoomTransactionRecord);
    }

    /**
     * 新增交易记录（包含支付和退款）
     * 
     * @param dramaRoomTransactionRecord 交易记录（包含支付和退款）
     * @return 结果
     */
    @Override
    public int insertDramaRoomTransactionRecord(DramaRoomTransactionRecord dramaRoomTransactionRecord)
    {
        dramaRoomTransactionRecord.setCreateTime(DateUtils.getNowDate());
        return dramaRoomTransactionRecordMapper.insertDramaRoomTransactionRecord(dramaRoomTransactionRecord);
    }

    /**
     * 修改交易记录（包含支付和退款）
     * 
     * @param dramaRoomTransactionRecord 交易记录（包含支付和退款）
     * @return 结果
     */
    @Override
    public int updateDramaRoomTransactionRecord(DramaRoomTransactionRecord dramaRoomTransactionRecord)
    {
        dramaRoomTransactionRecord.setUpdateTime(DateUtils.getNowDate());
        return dramaRoomTransactionRecordMapper.updateDramaRoomTransactionRecord(dramaRoomTransactionRecord);
    }

    /**
     * 批量删除交易记录（包含支付和退款）
     * 
     * @param ids 需要删除的交易记录（包含支付和退款）主键
     * @return 结果
     */
    @Override
    public int deleteDramaRoomTransactionRecordByIds(Long[] ids)
    {
        return dramaRoomTransactionRecordMapper.deleteDramaRoomTransactionRecordByIds(ids);
    }

    /**
     * 删除交易记录（包含支付和退款）信息
     * 
     * @param id 交易记录（包含支付和退款）主键
     * @return 结果
     */
    @Override
    public int deleteDramaRoomTransactionRecordById(Long id)
    {
        return dramaRoomTransactionRecordMapper.deleteDramaRoomTransactionRecordById(id);
    }
}