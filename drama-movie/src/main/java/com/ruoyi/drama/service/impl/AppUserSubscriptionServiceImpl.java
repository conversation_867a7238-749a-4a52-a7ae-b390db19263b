package com.ruoyi.drama.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.drama.mapper.AppUserSubscriptionMapper;
import com.ruoyi.drama.domain.AppUserSubscription;
import com.ruoyi.drama.service.IAppUserSubscriptionService;

/**
 * 用户订阅Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-09-01
 */
@Service
public class AppUserSubscriptionServiceImpl implements IAppUserSubscriptionService 
{
    @Autowired
    private AppUserSubscriptionMapper appUserSubscriptionMapper;

    /**
     * 查询用户订阅
     * 
     * @param id 用户订阅主键
     * @return 用户订阅
     */
    @Override
    public AppUserSubscription selectAppUserSubscriptionById(Long id)
    {
        return appUserSubscriptionMapper.selectAppUserSubscriptionById(id);
    }

    /**
     * 查询用户订阅列表
     * 
     * @param appUserSubscription 用户订阅
     * @return 用户订阅
     */
    @Override
    public List<AppUserSubscription> selectAppUserSubscriptionList(AppUserSubscription appUserSubscription)
    {
        return appUserSubscriptionMapper.selectAppUserSubscriptionList(appUserSubscription);
    }

    /**
     * 新增用户订阅
     * 
     * @param appUserSubscription 用户订阅
     * @return 结果
     */
    @Override
    public int insertAppUserSubscription(AppUserSubscription appUserSubscription)
    {
        appUserSubscription.setCreateTime(DateUtils.getNowDate());
        return appUserSubscriptionMapper.insertAppUserSubscription(appUserSubscription);
    }

    /**
     * 修改用户订阅
     * 
     * @param appUserSubscription 用户订阅
     * @return 结果
     */
    @Override
    public int updateAppUserSubscription(AppUserSubscription appUserSubscription)
    {
        appUserSubscription.setUpdateTime(DateUtils.getNowDate());
        return appUserSubscriptionMapper.updateAppUserSubscription(appUserSubscription);
    }

    /**
     * 批量删除用户订阅
     * 
     * @param ids 需要删除的用户订阅主键
     * @return 结果
     */
    @Override
    public int deleteAppUserSubscriptionByIds(Long[] ids)
    {
        return appUserSubscriptionMapper.deleteAppUserSubscriptionByIds(ids);
    }

    /**
     * 删除用户订阅信息
     * 
     * @param id 用户订阅主键
     * @return 结果
     */
    @Override
    public int deleteAppUserSubscriptionById(Long id)
    {
        return appUserSubscriptionMapper.deleteAppUserSubscriptionById(id);
    }
}