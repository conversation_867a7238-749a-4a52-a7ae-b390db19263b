package com.ruoyi.drama.service.impl;

import org.imgscalr.Scalr;

import javax.imageio.IIOImage;
import javax.imageio.ImageIO;
import javax.imageio.ImageWriteParam;
import javax.imageio.ImageWriter;
import javax.imageio.stream.ImageOutputStream;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.Iterator;

public class ImageProcessor {

    private static final int MAX_WIDTH = 750;
    private static final int MAX_HEIGHT = 1000;
    private static final float COMPRESSION_QUALITY = 0.8f;

//    /**
//     * 处理图片：检查尺寸、等比例缩放、压缩
//     * @param originalImage 原始图片字节数组
//     * @return 处理后的图片字节数组
//     */
//    public static byte[] processImage(byte[] originalImage) throws IOException {
//        return processImage(originalImage, "jpg");
//    }

    /**
     * 处理图片：检查尺寸、等比例缩放、压缩
     * @param originalImage 原始图片字节数组
     * @param outputFormat 输出图片格式 (jpg, png, gif等)
     * @return 处理后的图片字节数组
     */
    public static byte[] processImage(byte[] originalImage, String outputFormat) throws IOException {
//        // 读取原始图片
//        BufferedImage image = ImageIO.read(new ByteArrayInputStream(originalImage));
//        if (image == null) {
//            throw new IOException("无法读取图片");
//        }
//
//        final long SIZE_THRESHOLD = 1024 * 1024; // 1MB阈值
//        // 获取图片大小（字节数）
//        long imageSize = getImageSize(image); // 需要实现这个方法获取图片字节大小
//
//
//
//        // 获取原始尺寸
//        int originalWidth = image.getWidth();
//        int originalHeight = image.getHeight();
//
//        // 检查是否需要缩放
//        if (originalWidth > MAX_WIDTH || originalHeight > MAX_HEIGHT || imageSize > SIZE_THRESHOLD) {
//            // 计算等比例缩放后的尺寸
//            double widthRatio = (double) MAX_WIDTH / originalWidth;
//            double heightRatio = (double) MAX_HEIGHT / originalHeight;
//            double ratio = Math.min(widthRatio, heightRatio);
//
//            // 如果是由于大小超过阈值而非尺寸，可以设置一个最小压缩比例
//            if (imageSize > SIZE_THRESHOLD && ratio > 0.7) {
//                ratio = 0.7; // 强制缩小到70%
//            }
//
//            int newWidth = (int) (originalWidth * ratio);
//            int newHeight = (int) (originalHeight * ratio);
//
//            // 使用imgscalr进行高质量缩放
//            image = Scalr.resize(image, Scalr.Method.QUALITY, newWidth, newHeight);
//        }
//
//        // 压缩图片
//        return compressImage(image, outputFormat);
        BufferedImage image = ImageIO.read(new ByteArrayInputStream(originalImage));
        if (image == null) {
            throw new IOException("无法读取图片");
        }

        final int MAX_WIDTH = 750;
        final int MAX_HEIGHT = 600;
        final long SIZE_THRESHOLD = 1024 * 1024; // 1MB阈值

// 获取原始尺寸
        int originalWidth = image.getWidth();
        int originalHeight = image.getHeight();

        int newWidth = 0;
        int newHeight = 0;

        // 初始缩放比例计算
        double widthRatio = (double) MAX_WIDTH / originalWidth;
        double heightRatio = (double) MAX_HEIGHT / originalHeight;
        double ratio = Math.min(widthRatio, heightRatio);

        long imageSize = getImageSize(image,outputFormat); // 需要实现这个方法获取图片字节大小
        if (originalWidth > MAX_WIDTH || originalHeight > MAX_HEIGHT || imageSize > SIZE_THRESHOLD) {

// 初始缩放
            newWidth = (int) (originalWidth * ratio);
            newHeight = (int) (originalHeight * ratio);
            image = Scalr.resize(image, Scalr.Method.QUALITY, newWidth, newHeight);
        }

// 检查文件大小并循环压缩直到满足要求
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        long currentSize;
        float quality = 0.9f; // 初始质量参数

        do {
            baos.reset(); // 清空输出流

            // 使用JPEG或PNG压缩
            if (outputFormat.equalsIgnoreCase("jpg") || outputFormat.equalsIgnoreCase("jpeg")) {
                Iterator<ImageWriter> writers = ImageIO.getImageWritersByFormatName("jpeg");
                ImageWriter writer = writers.next();
                ImageWriteParam param = writer.getDefaultWriteParam();
                param.setCompressionMode(ImageWriteParam.MODE_EXPLICIT);
                param.setCompressionQuality(quality);

                try (ImageOutputStream ios = ImageIO.createImageOutputStream(baos)) {
                    writer.setOutput(ios);
                    writer.write(null, new IIOImage(image, null, null), param);
                }
                writer.dispose();
            } else {
                ImageIO.write(image, outputFormat, baos);
            }

            currentSize = baos.size();

            // 如果仍然太大，降低质量或进一步缩小尺寸
            if (currentSize > SIZE_THRESHOLD) {
                if (quality > 0.5f) {
                    quality -= 0.1f; // 降低质量
                } else {
                    // 如果质量已经很低但仍然太大，进一步缩小尺寸
                    ratio *= 0.9; // 缩小10%
                    newWidth = (int) (newWidth * ratio);
                    newHeight = (int) (newHeight * ratio);
                    image = Scalr.resize(image, Scalr.Method.QUALITY, newWidth, newHeight);
                    quality = 0.9f; // 重置质量参数
                }
            }
        } while (currentSize > SIZE_THRESHOLD);

        return baos.toByteArray();
    }

    // 获取图片字节大小的方法
    private static long getImageSize(BufferedImage image,String outputFormat) throws IOException {
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
//        ImageIO.write(image, "jpg", baos);
        ImageIO.write(image, outputFormat, baos);
        return baos.size();
    }

    /**
     * 压缩图片
     * @param image 需要压缩的图片
     * @param format 输出格式（jpg, png, gif等）
     * @return 处理后的图片字节数组
     */
    private static byte[] compressImage(BufferedImage image, String format) throws IOException {
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        
        // 如果输出格式是jpg/jpeg，且图像有透明通道，需要特殊处理
        if ((format.equalsIgnoreCase("jpg") || format.equalsIgnoreCase("jpeg")) && 
            (image.getType() == BufferedImage.TYPE_4BYTE_ABGR || 
             image.getType() == BufferedImage.TYPE_INT_ARGB || 
             image.getColorModel().hasAlpha())) {
            
            // 创建一个不带透明通道的RGB图像
            BufferedImage rgbImage = new BufferedImage(image.getWidth(), image.getHeight(), BufferedImage.TYPE_INT_RGB);


            // 绘制原图到白色背景上
            Graphics2D g2d = rgbImage.createGraphics();
            g2d.setColor(Color.WHITE);
            g2d.fillRect(0, 0, image.getWidth(), image.getHeight());
            g2d.drawImage(image, 0, 0, null);
            g2d.dispose();
            
            image = rgbImage;
        }

        // 使用带压缩质量设置的方式写入JPEG图片
        if (format.equalsIgnoreCase("jpg") || format.equalsIgnoreCase("jpeg")) {
            Iterator<ImageWriter> writers = ImageIO.getImageWritersByFormatName("jpg");
            if (writers.hasNext()) {
                ImageWriter writer = writers.next();
                ImageWriteParam param = writer.getDefaultWriteParam();
                param.setCompressionMode(ImageWriteParam.MODE_EXPLICIT);
                param.setCompressionQuality(COMPRESSION_QUALITY);

                try (ImageOutputStream ios = ImageIO.createImageOutputStream(baos)) {
                    writer.setOutput(ios);
                    writer.write(null, new IIOImage(image, null, null), param);
                }
                writer.dispose();
            } else {
                // 如果没有jpg写入器，则使用默认方式
                ImageIO.write(image, format, baos);
            }
        } else {
            // 非jpg格式使用默认方式写入
            ImageIO.write(image, format, baos);
        }

        return baos.toByteArray();
    }

//    /**
//     * 压缩图片为JPEG格式
//     */
//    private static byte[] compressImage(BufferedImage image) throws IOException {
//        return compressImage(image, "jpg");
//    }
}
