package com.ruoyi.drama.service.impl;

import java.io.IOException;
import java.util.List;
import java.util.stream.Collectors;

import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.drama.dto.DramaRoomGeneralI18nDTO;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.Gemini25FlashUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.drama.domain.DramaRoomArea;
import com.ruoyi.drama.domain.DramaRoomLanguageConfig;
import com.ruoyi.drama.mapper.DramaRoomAreaMapper;
import com.ruoyi.drama.mapper.DramaRoomLanguageConfigMapper;
import io.jsonwebtoken.lang.Collections;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.drama.mapper.DramaRoomAreaI18nMapper;
import com.ruoyi.drama.domain.DramaRoomAreaI18n;
import com.ruoyi.drama.service.IDramaRoomAreaI18nService;

/**
 * 短剧地区多语言Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-08-05
 */
@Slf4j
@Service
public class DramaRoomAreaI18nServiceImpl implements IDramaRoomAreaI18nService {
    @Autowired
    private DramaRoomAreaI18nMapper dramaRoomAreaI18nMapper;

    @Autowired
    private DramaRoomAreaMapper dramaRoomAreaMapper;

    @Autowired
    private DramaRoomLanguageConfigMapper dramaRoomLanguageConfigMapper;

    /**
     * 查询短剧地区多语言
     *
     * @param id 短剧地区多语言主键
     * @return 短剧地区多语言
     */
    @Override
    public DramaRoomAreaI18n selectDramaRoomAreaI18nById(Long id) {
        return dramaRoomAreaI18nMapper.selectDramaRoomAreaI18nById(id);
    }

    /**
     * 查询短剧地区多语言列表
     *
     * @param dramaRoomAreaI18n 短剧地区多语言
     * @return 短剧地区多语言
     */
    @Override
    public List<DramaRoomAreaI18n> selectDramaRoomAreaI18nList(DramaRoomAreaI18n dramaRoomAreaI18n) {
        return dramaRoomAreaI18nMapper.selectDramaRoomAreaI18nList(dramaRoomAreaI18n);
    }

    /**
     * 新增短剧地区多语言
     *
     * @param dramaRoomAreaI18n 短剧地区多语言
     * @return 结果
     */
    @Override
    public int insertDramaRoomAreaI18n(DramaRoomAreaI18n dramaRoomAreaI18n) {
        dramaRoomAreaI18n.setCreateTime(DateUtils.getNowDate());
        return dramaRoomAreaI18nMapper.insertDramaRoomAreaI18n(dramaRoomAreaI18n);
    }

    /**
     * 修改短剧地区多语言
     *
     * @param dramaRoomAreaI18n 短剧地区多语言
     * @return 结果
     */
    @Override
    public int updateDramaRoomAreaI18n(DramaRoomAreaI18n dramaRoomAreaI18n) {
        dramaRoomAreaI18n.setUpdateTime(DateUtils.getNowDate());
        return dramaRoomAreaI18nMapper.updateDramaRoomAreaI18n(dramaRoomAreaI18n);
    }

    /**
     * 批量删除短剧地区多语言
     *
     * @param ids 需要删除的短剧地区多语言主键
     * @return 结果
     */
    @Override
    public int deleteDramaRoomAreaI18nByIds(Long[] ids) {
        return dramaRoomAreaI18nMapper.deleteDramaRoomAreaI18nByIds(ids);
    }

    /**
     * 删除短剧地区多语言信息
     *
     * @param id 短剧地区多语言主键
     * @return 结果
     */
    @Override
    public int deleteDramaRoomAreaI18nById(Long id) {
        return dramaRoomAreaI18nMapper.deleteDramaRoomAreaI18nById(id);
    }

    @Override
    public void insertDramaRoomAreaI18nList(List<DramaRoomAreaI18n> i18ns) {
        dramaRoomAreaI18nMapper.insertDramaRoomAreaI18nList(i18ns);
    }

    @Override
    public AjaxResult translate(DramaRoomGeneralI18nDTO dto) {
        Long id = dto.getId();
        Long loginId = dto.getUserId();
        String language = dto.getLanguage();
        String languageCode = dto.getLanguageCode();
        DramaRoomArea dramaRoomArea = dramaRoomAreaMapper.selectDramaRoomAreaById(id);
        if (dramaRoomArea == null) {
            return AjaxResult.error("地区不存在");
        }
        try {
            String translate = Gemini25FlashUtils.translate(dramaRoomArea.getArea(), language);
            if (StringUtils.isEmpty(translate)) {
                return AjaxResult.error("翻译结果为空");
            }
            DramaRoomAreaI18n dramaRoomAreaI18n = new DramaRoomAreaI18n();
            dramaRoomAreaI18n.setArea(translate);
            dramaRoomAreaI18n.setAreaId(id);
            dramaRoomAreaI18n.setLanguageCode(languageCode);
            dramaRoomAreaI18n.setCreateTime(DateUtils.getNowDate());
            dramaRoomAreaI18n.setCreateBy(String.valueOf(loginId));
            dramaRoomAreaI18nMapper.insertDramaRoomAreaI18n(dramaRoomAreaI18n);
        } catch (IOException e) {
            log.error("【{}】翻译异常：{}", language, e.getMessage());
            return AjaxResult.error("翻译异常");
        }
        return AjaxResult.success("翻译成功");
    }

    @Override
    public AjaxResult listTranslate(Long id) {
        DramaRoomArea dramaRoomArea = dramaRoomAreaMapper.selectDramaRoomAreaById(id);
        if (dramaRoomArea == null) {
            return AjaxResult.error("地区不存在");
        }
        DramaRoomAreaI18n dramaRoomAreaI18n = new DramaRoomAreaI18n();
        dramaRoomAreaI18n.setAreaId(id);
        List<DramaRoomAreaI18n> dramaRoomAreaI18ns = dramaRoomAreaI18nMapper.selectDramaRoomAreaI18nList(dramaRoomAreaI18n);
        List<DramaRoomLanguageConfig> dramaRoomLanguageConfigs = dramaRoomLanguageConfigMapper.selectDramaRoomLanguageConfigList(new DramaRoomLanguageConfig());
        if (Collections.isEmpty(dramaRoomAreaI18ns)) {
            return AjaxResult.success(dramaRoomLanguageConfigs);
        }
        if (dramaRoomAreaI18ns.size() == dramaRoomLanguageConfigs.size()) {
            return AjaxResult.success(dramaRoomArea.getArea() + "地区已全部翻译，请刷新列表后检查翻译结果！");
        }
        List<String> existingLanguageCodes = dramaRoomAreaI18ns.stream()
                .map(DramaRoomAreaI18n::getLanguageCode)
                .collect(Collectors.toList());
        List<DramaRoomLanguageConfig> filteredConfigs = dramaRoomLanguageConfigs.stream()
                .filter(config -> !existingLanguageCodes.contains(config.getCode()))
                .collect(Collectors.toList());
        return AjaxResult.success(filteredConfigs);
    }
}
