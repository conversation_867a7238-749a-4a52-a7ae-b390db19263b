package com.ruoyi.drama.service;

import com.ruoyi.drama.domain.DramaRoomAudio;

import java.util.List;

/**
 * 音讯Service接口
 *
 * <AUTHOR>
 * @date 2025-07-28
 */
public interface IDramaRoomAudioService
{
    /**
     * 查询音讯
     *
     * @param id 音讯主键
     * @return 音讯
     */
    public DramaRoomAudio selectDramaRoomAudioById(Long id);

    /**
     * 查询音讯列表
     *
     * @param dramaRoomAudio 音讯
     * @return 音讯集合
     */
    public List<DramaRoomAudio> selectDramaRoomAudioList(DramaRoomAudio dramaRoomAudio);

    /**
     * 新增音讯
     *
     * @param dramaRoomAudio 音讯
     * @return 结果
     */
    public int insertDramaRoomAudio(DramaRoomAudio dramaRoomAudio);

    /**
     * 修改音讯
     *
     * @param dramaRoomAudio 音讯
     * @return 结果
     */
    public int updateDramaRoomAudio(DramaRoomAudio dramaRoomAudio);

    /**
     * 批量删除音讯
     *
     * @param ids 需要删除的音讯主键集合
     * @return 结果
     */
    public int deleteDramaRoomAudioByIds(Long[] ids);

    /**
     * 删除音讯信息
     *
     * @param id 音讯主键
     * @return 结果
     */
    public int deleteDramaRoomAudioById(Long id);
}
