package com.ruoyi.drama.service;

import com.ruoyi.common.drama.dto.DramaRoomMovieCoinRuleDTO;
import com.ruoyi.drama.domain.DramaRoomMovieCoinRule;

import java.util.List;
import java.util.Map;

/**
 * 短剧扣费规则Service接口
 *
 * <AUTHOR>
 * @date 2025-08-05
 */
public interface IDramaRoomMovieCoinRuleService
{
    /**
     * 查询短剧扣费规则
     *
     * @param id 短剧扣费规则主键
     * @return 短剧扣费规则
     */
    public DramaRoomMovieCoinRule selectDramaRoomMovieCoinRuleById(Long id);

    /**
     * 查询短剧扣费规则列表
     *
     * @param dramaRoomMovieCoinRule 短剧扣费规则
     * @return 短剧扣费规则集合
     */
    public List<DramaRoomMovieCoinRule> selectDramaRoomMovieCoinRuleList(DramaRoomMovieCoinRule dramaRoomMovieCoinRule);

    /**
     * 新增短剧扣费规则
     *
     * @param dramaRoomMovieCoinRule 短剧扣费规则
     * @return 结果
     */
    public int insertDramaRoomMovieCoinRule(DramaRoomMovieCoinRule dramaRoomMovieCoinRule);

    /**
     * 修改短剧扣费规则
     *
     * @param dramaRoomMovieCoinRule 短剧扣费规则
     * @return 结果
     */
    public int updateDramaRoomMovieCoinRule(DramaRoomMovieCoinRule dramaRoomMovieCoinRule);

    /**
     * 批量删除短剧扣费规则
     *
     * @param ids 需要删除的短剧扣费规则主键集合
     * @return 结果
     */
    public int deleteDramaRoomMovieCoinRuleByIds(Long[] ids);

    /**
     * 删除短剧扣费规则信息
     *
     * @param id 短剧扣费规则主键
     * @return 结果
     */
    public int deleteDramaRoomMovieCoinRuleById(Long id);

    /**
     * 新增或保存短剧扣费规则
     * @param dramaRoomMovieCoinRuleDTO
     * @return
     */
    int saveMovieCoinRule(DramaRoomMovieCoinRuleDTO dramaRoomMovieCoinRuleDTO);

    Map<String, Object> getDramaRoomMovieCoinRule(Long movieId);
}
