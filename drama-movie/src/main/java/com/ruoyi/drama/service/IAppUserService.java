package com.ruoyi.drama.service;

import com.ruoyi.common.core.domain.entity.AppUser;
import com.ruoyi.common.drama.dto.GuestLoginDto;
import com.ruoyi.common.drama.dto.RefreshTokenDto;
import com.ruoyi.common.drama.dto.ThirdPartyLoginDTO;
import com.ruoyi.drama.domain.vo.SemAppUserVo;
import com.ruoyi.common.drama.vo.UserInfoVO;
import com.ruoyi.common.utils.mobile.AppTokenService;

import java.util.List;

/**
 * App用户 服务层
 * 
 * <AUTHOR>
 */
public interface IAppUserService
{
    /**
     * 游客登录
     *
     * @param guestLoginDto 登录信息
     * @return token
     */
    AppTokenService.TokenVo  guestLogin(GuestLoginDto guestLoginDto);


    /**
     * 刷新token
     *
     * @param refreshTokenDto 刷新信息
     * @return token
     */
    AppTokenService.TokenVo refreshToken(RefreshTokenDto refreshTokenDto);
    /**
     * 第三方登录
     *
     * @param thirdPartyLoginDto 登录信息
     * @return token
     */
    AppTokenService.TokenVo thirdPartyLogin(ThirdPartyLoginDTO  thirdPartyLoginDto);

    /**
     * 退出登录
     */
    void logout();

    /**
     * 查询APP用户
     *
     * @param userId APP用户主键
     * @return APP用户
     */
    public AppUser selectAppUserByUserId(Long userId);

    /**
     * 查询APP用户列表
     *
     * @param appUser APP用户
     * @return APP用户集合
     */
    public List<AppUser> selectAppUserList(AppUser appUser);


    UserInfoVO getLoginUserInfo();

    /**
     * 新增APP用户
     */
    int insertAppUser(AppUser appUser);

    /**
     * 修改APP用户
     */
    int updateAppUser(AppUser appUser);

    /**
     * 批量删除APP用户
     */
    int deleteAppUserByUserIds(Long[] userIds);

    /**
     * 删除APP用户
     */
    int deleteAppUserByUserId(Long userId);

    List<SemAppUserVo> selectSemAppUserList(AppUser appUser);
}