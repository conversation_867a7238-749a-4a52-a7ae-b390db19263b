package com.ruoyi.drama.service;

import java.util.List;
import com.ruoyi.drama.domain.DramaRoomSemLink;
import com.ruoyi.drama.domain.vo.DramaRoomSemLinkVO;

/**
 * 推广链接Service接口
 * 
 * <AUTHOR>
 * @date 2025-08-11
 */
public interface IDramaRoomSemLinkService 
{
    /**
     * 查询推广链接
     * 
     * @param id 推广链接主键
     * @return 推广链接
     */
    public DramaRoomSemLink selectDramaRoomSemLinkById(Long id);

    /**
     * 查询推广链接列表
     * 
     * @param dramaRoomSemLink 推广链接
     * @return 推广链接集合
     */
    public List<DramaRoomSemLink> selectDramaRoomSemLinkList(DramaRoomSemLink dramaRoomSemLink);

    /**
     * 新增推广链接
     * 
     * @param dramaRoomSemLink 推广链接
     * @return 结果
     */
    public int insertDramaRoomSemLink(DramaRoomSemLink dramaRoomSemLink);

    /**
     * 修改推广链接
     * 
     * @param dramaRoomSemLink 推广链接
     * @return 结果
     */
    public int updateDramaRoomSemLink(DramaRoomSemLink dramaRoomSemLink);

    /**
     * 批量删除推广链接
     * 
     * @param ids 需要删除的推广链接主键集合
     * @return 结果
     */
    public int deleteDramaRoomSemLinkByIds(Long[] ids);

    /**
     * 删除推广链接信息
     * 
     * @param id 推广链接主键
     * @return 结果
     */
    public int deleteDramaRoomSemLinkById(Long id);

    List<DramaRoomSemLinkVO> queryDramaRoomSemLink(DramaRoomSemLink dramaRoomSemLink);

}