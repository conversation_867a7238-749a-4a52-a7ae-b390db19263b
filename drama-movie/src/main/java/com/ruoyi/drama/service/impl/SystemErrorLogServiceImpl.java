package com.ruoyi.drama.service.impl;

import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.drama.domain.SystemErrorLog;
import com.ruoyi.drama.mapper.SystemErrorLogMapper;
import com.ruoyi.drama.service.ISystemErrorLogService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 系统错误日志Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-08-06
 */
@Service
public class SystemErrorLogServiceImpl implements ISystemErrorLogService
{
    @Autowired
    private SystemErrorLogMapper systemErrorLogMapper;

    /**
     * 查询系统错误日志
     *
     * @param id 系统错误日志主键
     * @return 系统错误日志
     */
    @Override
    public SystemErrorLog selectSystemErrorLogById(Long id)
    {
        return systemErrorLogMapper.selectSystemErrorLogById(id);
    }

    /**
     * 查询系统错误日志列表
     *
     * @param systemErrorLog 系统错误日志
     * @return 系统错误日志
     */
    @Override
    public List<SystemErrorLog> selectSystemErrorLogList(SystemErrorLog systemErrorLog)
    {
        return systemErrorLogMapper.selectSystemErrorLogList(systemErrorLog);
    }

    /**
     * 新增系统错误日志
     *
     * @param systemErrorLog 系统错误日志
     * @return 结果
     */
    @Override
    public int insertSystemErrorLog(SystemErrorLog systemErrorLog)
    {
        systemErrorLog.setCreateTime(DateUtils.getNowDate());
        return systemErrorLogMapper.insertSystemErrorLog(systemErrorLog);
    }

    /**
     * 修改系统错误日志
     *
     * @param systemErrorLog 系统错误日志
     * @return 结果
     */
    @Override
    public int updateSystemErrorLog(SystemErrorLog systemErrorLog)
    {
        return systemErrorLogMapper.updateSystemErrorLog(systemErrorLog);
    }

    /**
     * 批量删除系统错误日志
     *
     * @param ids 需要删除的系统错误日志主键
     * @return 结果
     */
    @Override
    public int deleteSystemErrorLogByIds(Long[] ids)
    {
        return systemErrorLogMapper.deleteSystemErrorLogByIds(ids);
    }

    /**
     * 删除系统错误日志信息
     *
     * @param id 系统错误日志主键
     * @return 结果
     */
    @Override
    public int deleteSystemErrorLogById(Long id)
    {
        return systemErrorLogMapper.deleteSystemErrorLogById(id);
    }
}