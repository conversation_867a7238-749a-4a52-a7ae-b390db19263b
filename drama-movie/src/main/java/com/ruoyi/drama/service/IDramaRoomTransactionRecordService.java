package com.ruoyi.drama.service;

import com.ruoyi.drama.domain.DramaRoomTransactionRecord;

import java.util.List;

/**
 * 交易记录（包含支付和退款）Service接口
 * 
 * <AUTHOR>
 * @date 2025-08-08
 */
public interface IDramaRoomTransactionRecordService 
{
    /**
     * 查询交易记录（包含支付和退款）
     * 
     * @param id 交易记录（包含支付和退款）主键
     * @return 交易记录（包含支付和退款）
     */
    public DramaRoomTransactionRecord selectDramaRoomTransactionRecordById(Long id);

    /**
     * 查询交易记录（包含支付和退款）列表
     * 
     * @param dramaRoomTransactionRecord 交易记录（包含支付和退款）
     * @return 交易记录（包含支付和退款）集合
     */
    public List<DramaRoomTransactionRecord> selectDramaRoomTransactionRecordList(DramaRoomTransactionRecord dramaRoomTransactionRecord);

    /**
     * 新增交易记录（包含支付和退款）
     * 
     * @param dramaRoomTransactionRecord 交易记录（包含支付和退款）
     * @return 结果
     */
    public int insertDramaRoomTransactionRecord(DramaRoomTransactionRecord dramaRoomTransactionRecord);

    /**
     * 修改交易记录（包含支付和退款）
     * 
     * @param dramaRoomTransactionRecord 交易记录（包含支付和退款）
     * @return 结果
     */
    public int updateDramaRoomTransactionRecord(DramaRoomTransactionRecord dramaRoomTransactionRecord);

    /**
     * 批量删除交易记录（包含支付和退款）
     * 
     * @param ids 需要删除的交易记录（包含支付和退款）主键集合
     * @return 结果
     */
    public int deleteDramaRoomTransactionRecordByIds(Long[] ids);

    /**
     * 删除交易记录（包含支付和退款）信息
     * 
     * @param id 交易记录（包含支付和退款）主键
     * @return 结果
     */
    public int deleteDramaRoomTransactionRecordById(Long id);
}