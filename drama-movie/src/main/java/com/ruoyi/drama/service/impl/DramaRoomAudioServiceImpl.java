package com.ruoyi.drama.service.impl;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.drama.domain.DramaRoomAudio;
import com.ruoyi.drama.domain.DramaRoomAudioI18n;
import com.ruoyi.drama.manager.MovieAsyncManager;
import com.ruoyi.drama.manager.factory.I18nAsyncFactory;
import com.ruoyi.drama.mapper.DramaRoomAudioI18nMapper;
import com.ruoyi.drama.mapper.DramaRoomAudioMapper;
import com.ruoyi.drama.service.IDramaRoomAudioService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

/**
 * 音讯Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-28
 */
@Service
public class DramaRoomAudioServiceImpl implements IDramaRoomAudioService {
    @Autowired
    private DramaRoomAudioMapper dramaRoomAudioMapper;

    @Autowired
    private DramaRoomAudioI18nMapper dramaRoomAudioI18nMapper;

    /**
     * 查询音讯
     *
     * @param id 音讯主键
     * @return 音讯
     */
    @Override
    public DramaRoomAudio selectDramaRoomAudioById(Long id) {
        return dramaRoomAudioMapper.selectDramaRoomAudioById(id);
    }

    /**
     * 查询音讯列表
     *
     * @param dramaRoomAudio 音讯
     * @return 音讯
     */
    @Override
    public List<DramaRoomAudio> selectDramaRoomAudioList(DramaRoomAudio dramaRoomAudio) {
        List<DramaRoomAudio> dramaRoomAudios = dramaRoomAudioMapper.selectDramaRoomAudioList(dramaRoomAudio);
        List<Long> audioIds = dramaRoomAudios.stream().map(DramaRoomAudio::getId).collect(Collectors.toList());
        List<DramaRoomAudioI18n> i18ns = dramaRoomAudioI18nMapper.selectDramaRoomAudioI18nByAudioIds(audioIds);
        if (CollectionUtils.isEmpty(i18ns)) {
            return dramaRoomAudios;
        }
        Map<Long, List<DramaRoomAudioI18n>> i18nMap = i18ns.stream()
                .collect(Collectors.groupingBy(DramaRoomAudioI18n::getAudioId));
        dramaRoomAudios.forEach(audio -> {
            if (i18nMap.containsKey(audio.getId())) {
                audio.setI18nList(i18nMap.get(audio.getId()));
            }
        });
        return dramaRoomAudios;
    }

    /**
     * 新增音讯
     *
     * @param dramaRoomAudio 音讯
     * @return 结果
     */
    @Override
    public int insertDramaRoomAudio(DramaRoomAudio dramaRoomAudio) {
        dramaRoomAudio.setCreateTime(DateUtils.getNowDate());
        dramaRoomAudio.setCreateBy(String.valueOf(SecurityUtils.getUserId()));
        int i = dramaRoomAudioMapper.insertDramaRoomAudio(dramaRoomAudio);
        if (i > 0) {
            MovieAsyncManager.me().execute(I18nAsyncFactory.getAudioI18n(dramaRoomAudio.getId(), dramaRoomAudio.getAudio(), String.valueOf(SecurityUtils.getUserId())));
        }
        return i;
    }

    /**
     * 修改音讯
     *
     * @param dramaRoomAudio 音讯
     * @return 结果
     */
    @Override
    public int updateDramaRoomAudio(DramaRoomAudio dramaRoomAudio) {
        dramaRoomAudio.setUpdateTime(DateUtils.getNowDate());
        return dramaRoomAudioMapper.updateDramaRoomAudio(dramaRoomAudio);
    }

    /**
     * 批量删除音讯
     *
     * @param ids 需要删除的音讯主键
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteDramaRoomAudioByIds(Long[] ids) {
        dramaRoomAudioI18nMapper.deleteDramaRoomAudioI18nByAudioIds(ids);
        return dramaRoomAudioMapper.deleteDramaRoomAudioByIds(ids);
    }

    /**
     * 删除音讯信息
     *
     * @param id 音讯主键
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteDramaRoomAudioById(Long id) {
        dramaRoomAudioI18nMapper.deleteDramaRoomAudioI18nByAudioId(id);
        return dramaRoomAudioMapper.deleteDramaRoomAudioById(id);
    }
}
