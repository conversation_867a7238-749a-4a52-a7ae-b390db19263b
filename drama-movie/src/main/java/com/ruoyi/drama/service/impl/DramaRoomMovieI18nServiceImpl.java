package com.ruoyi.drama.service.impl;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CountDownLatch;
import java.util.stream.Collectors;

import cn.hutool.core.collection.CollectionUtil;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.drama.dto.DramaRoomMovieI18nDTO;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.Gemini25FlashUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.drama.domain.*;
import com.ruoyi.drama.mapper.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import com.ruoyi.drama.service.IDramaRoomMovieI18nService;

/**
 * 短剧翻译Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-08-05
 */
@Slf4j
@Service
public class DramaRoomMovieI18nServiceImpl implements IDramaRoomMovieI18nService {
    @Autowired
    private DramaRoomMovieI18nMapper dramaRoomMovieI18nMapper;

    @Autowired
    private DramaRoomLanguageConfigMapper dramaRoomLanguageConfigMapper;

    @Autowired
    private DramaRoomVideoMapper dramaRoomVideoMapper;

    @Autowired
    private DramaRoomVideoI18nMapper dramaRoomVideoI18nMapper;

    @Autowired
    private DramaRoomMovieMapper dramaRoomMovieMapper;

    @Autowired
    private ThreadPoolTaskExecutor threadPoolTaskExecutor;

    /**
     * 查询短剧翻译
     *
     * @param id 短剧翻译主键
     * @return 短剧翻译
     */
    @Override
    public DramaRoomMovieI18n selectDramaRoomMovieI18nById(Long id) {
        return dramaRoomMovieI18nMapper.selectDramaRoomMovieI18nById(id);
    }

    /**
     * 查询短剧翻译列表
     *
     * @param dramaRoomMovieI18n 短剧翻译
     * @return 短剧翻译
     */
    @Override
    public List<DramaRoomMovieI18n> selectDramaRoomMovieI18nList(DramaRoomMovieI18n dramaRoomMovieI18n) {
        return dramaRoomMovieI18nMapper.selectDramaRoomMovieI18nList(dramaRoomMovieI18n);
    }

    /**
     * 新增短剧翻译
     *
     * @param dramaRoomMovieI18n 短剧翻译
     * @return 结果
     */
    @Override
    public int insertDramaRoomMovieI18n(DramaRoomMovieI18n dramaRoomMovieI18n) {
        dramaRoomMovieI18n.setCreateTime(DateUtils.getNowDate());
        return dramaRoomMovieI18nMapper.insertDramaRoomMovieI18n(dramaRoomMovieI18n);
    }

    /**
     * 修改短剧翻译
     *
     * @param dramaRoomMovieI18n 短剧翻译
     * @return 结果
     */
    @Override
    public int updateDramaRoomMovieI18n(DramaRoomMovieI18n dramaRoomMovieI18n) {
        dramaRoomMovieI18n.setUpdateTime(DateUtils.getNowDate());
        return dramaRoomMovieI18nMapper.updateDramaRoomMovieI18n(dramaRoomMovieI18n);
    }

    /**
     * 批量删除短剧翻译
     *
     * @param ids 需要删除的短剧翻译主键
     * @return 结果
     */
    @Override
    public int deleteDramaRoomMovieI18nByIds(Long[] ids) {
        return dramaRoomMovieI18nMapper.deleteDramaRoomMovieI18nByIds(ids);
    }

    /**
     * 删除短剧翻译信息
     *
     * @param id 短剧翻译主键
     * @return 结果
     */
    @Override
    public int deleteDramaRoomMovieI18nById(Long id) {
        return dramaRoomMovieI18nMapper.deleteDramaRoomMovieI18nById(id);
    }

    @Override
    public List<DramaRoomMovieI18n> getMovieI18nList(Long movieId, String languageCode) {
        DramaRoomMovieI18n query = new DramaRoomMovieI18n();
        query.setMovieId(movieId);
        if (StringUtils.isNotEmpty(languageCode)) {
            query.setLanguageCode(languageCode);
        }
        return dramaRoomMovieI18nMapper.selectDramaRoomMovieI18nList(query);
    }

    @Override
    public int updateDramaRoomMovieI18nState(DramaRoomMovieI18n dramaRoomMovieI18n) {
        //只有是通过状态才校验  查询短剧下这个语种的视频字母是否翻译完成
        if (dramaRoomMovieI18n.getStatus().equals("1")) {
            Long movieId = dramaRoomMovieI18n.getMovieId();
            DramaRoomMovieI18n dramaRoomMovieI18nQuery = new DramaRoomMovieI18n();
            dramaRoomMovieI18nQuery.setMovieId(movieId);
            dramaRoomMovieI18nQuery.setLanguageCode(dramaRoomMovieI18n.getLanguageCode());
            List<DramaRoomMovieI18n> shortMovieI18nList = dramaRoomMovieI18nMapper.selectDramaRoomMovieI18nList(dramaRoomMovieI18nQuery);
            List<DramaRoomLanguageConfig> langList = dramaRoomLanguageConfigMapper.selectDramaRoomLanguageConfigList(new DramaRoomLanguageConfig());
            Map<String, String> languageCodeMap = langList.stream().collect(Collectors.toMap(DramaRoomLanguageConfig::getCode, DramaRoomLanguageConfig::getZhName));
            if (CollectionUtil.isNotEmpty(shortMovieI18nList)) {
                for (DramaRoomMovieI18n movieI18n : shortMovieI18nList) {
                    if (StringUtils.isEmpty(movieI18n.getCoverImage())) {
                        throw new RuntimeException("短剧" + languageCodeMap.get(movieI18n.getLanguageCode()) + "版本封面未上传");
                    }
                }
            }
            List<String> languageCodeCollect = shortMovieI18nList.stream().map(DramaRoomMovieI18n::getLanguageCode).collect(Collectors.toList());
            DramaRoomVideo shortVideoQuery = new DramaRoomVideo();
            shortVideoQuery.setMovieId(movieId);
            //原视频
            List<DramaRoomVideo> dramaRoomVideoList = dramaRoomVideoMapper.selectDramaRoomVideoList(shortVideoQuery);
            List<Long> videoIdList = dramaRoomVideoList.stream().map(DramaRoomVideo::getId).collect(Collectors.toList());
            //查询翻译后的视频字幕表
            List<DramaRoomVideoI18n> dramaRoomVideoI18nList = dramaRoomVideoI18nMapper.selectDramaRoomVideoI18nByVideoIds(videoIdList, languageCodeCollect);
            int num = videoIdList.size();
            if (CollectionUtil.isEmpty(dramaRoomVideoI18nList)) {
                throw new RuntimeException("短剧" + languageCodeMap.get(shortMovieI18nList.iterator().next().getLanguageCode()) + "版本字幕未上传");
            }
            //判断某个语言的数量和原视频不一致则抛异常
            if (num != dramaRoomVideoI18nList.size()) {
                List<Long> videoNumList = new ArrayList<>();
                Map<Long, Long> videoNumMap = dramaRoomVideoList.stream().collect(Collectors.toMap(DramaRoomVideo::getId, DramaRoomVideo::getVideoNum));
                List<Long> videoI18nVideoIdList = dramaRoomVideoI18nList.stream().map(DramaRoomVideoI18n::getVideoId).collect(Collectors.toList());
                videoNumMap.forEach((videoId, videoNum) -> {
                    if (!videoI18nVideoIdList.contains(videoId)) {
                        videoNumList.add(videoNumMap.get(videoId));
                    }
                });
                //将 videoNumList 转为字符串用 , 拼接
                String videoNumStr = videoNumList.stream().map(String::valueOf).collect(Collectors.joining(","));
                throw new RuntimeException("短剧的字幕" + languageCodeMap.get(shortMovieI18nList.iterator().next().getLanguageCode()) + "版本翻译的第" + videoNumStr + "集未完成");
            }
        }
        //根据videoId查询 已翻译的短句字幕
        DramaRoomMovieI18n update = new DramaRoomMovieI18n();
        update.setId(dramaRoomMovieI18n.getId());
        update.setLanguageCode(dramaRoomMovieI18n.getLanguageCode());
        update.setStatus(dramaRoomMovieI18n.getStatus());
        update.setUpdateBy(String.valueOf(SecurityUtils.getUserId()));
        update.setUpdateTime(DateUtils.getNowDate());
        return dramaRoomMovieI18nMapper.updateDramaRoomMovieI18n(update);
    }

    @Override
    public AjaxResult translateMovieI18n(DramaRoomMovieI18nDTO dto) {
        String language = dto.getLanguage();
        Long movieId = dto.getMovieId();
        String languageCode = dto.getLanguageCode();
        log.info("开始翻译短剧【{}】语言【{}】", movieId, language);
        // 查询这部剧的语言是否翻译
        DramaRoomMovieI18n dramaRoomMovieI18n = new DramaRoomMovieI18n();
        dramaRoomMovieI18n.setMovieId(movieId);
        dramaRoomMovieI18n.setLanguageCode(languageCode);
        List<DramaRoomMovieI18n> movieI18ns = dramaRoomMovieI18nMapper.selectDramaRoomMovieI18nList(dramaRoomMovieI18n);
        if (CollectionUtil.isNotEmpty(movieI18ns)) {
            log.error("短剧【{}】语言【{}】已翻译", movieId, language);
            return AjaxResult.error("短剧" + language + "语言已翻译");
        }
        // 开始翻译短剧
        try {
            DramaRoomMovie dramaRoomMovie = dramaRoomMovieMapper.selectDramaRoomMovieById(movieId);
            if (dramaRoomMovie == null) {
                log.error("【{}】翻译异常：{}短剧不存在", language, movieId);
                return AjaxResult.error("短剧不存在");
            }
            String[] keys = new String[]{"title", "description", "director", "actors"};
            CountDownLatch countDownLatch = new CountDownLatch(keys.length);
            for (String key : keys) {
                threadPoolTaskExecutor.execute(() -> {
                    log.info(Thread.currentThread().getName() + "线程：开始翻译：{}", key);
                    try {
                        String translate = getI18N(dramaRoomMovie.get(key), language);
                        dramaRoomMovieI18n.set(key, translate);
                    } catch (Exception e) {
                        e.printStackTrace();
                    } finally {
                        countDownLatch.countDown();
                    }
                });
            }
            countDownLatch.await();
            dramaRoomMovieI18n.setCreateTime(DateUtils.getNowDate());
            dramaRoomMovieI18n.setUpdateTime(DateUtils.getNowDate());
            dramaRoomMovieI18n.setCreateBy(String.valueOf(dto.getUserId()));
            int i = dramaRoomMovieI18nMapper.insertDramaRoomMovieI18n(dramaRoomMovieI18n);
            if (i > 0) {
                log.info("短剧【{}】语言【{}】翻译成功", movieId, language);
                // todo 发送消息 通知前端翻译成功
            }

        } catch (Exception e) {
            log.error("【{}】翻译异常：{}", language, e.getMessage());
            return AjaxResult.error("翻译异常");
        }
        return AjaxResult.success("翻译中...");
    }

    @Override
    public int deleteMovieI18n(Long movieId, String languageCode) {
        return dramaRoomMovieI18nMapper.deleteMovieI18n(movieId, languageCode);
    }

    private static String getI18N(String text, String language) throws IOException {
        return StringUtils.isNotEmpty(text) ? Gemini25FlashUtils.translate(text, language) : text;
    }
}
