package com.ruoyi.drama.service;

import java.util.List;
import com.ruoyi.drama.domain.DramaRoomPayTemplate;

/**
 * 付费模板Service接口
 * 
 * <AUTHOR>
 * @date 2025-08-13
 */
public interface IDramaRoomPayTemplateService 
{
    /**
     * 查询付费模板
     * 
     * @param id 付费模板主键
     * @return 付费模板
     */
    public DramaRoomPayTemplate selectDramaRoomPayTemplateById(Long id);

    /**
     * 查询付费模板列表
     * 
     * @param dramaRoomPayTemplate 付费模板
     * @return 付费模板集合
     */
    public List<DramaRoomPayTemplate> selectDramaRoomPayTemplateList(DramaRoomPayTemplate dramaRoomPayTemplate);

    /**
     * 新增付费模板
     * 
     * @param dramaRoomPayTemplate 付费模板
     * @return 结果
     */
    public int insertDramaRoomPayTemplate(DramaRoomPayTemplate dramaRoomPayTemplate);

    /**
     * 修改付费模板
     * 
     * @param dramaRoomPayTemplate 付费模板
     * @return 结果
     */
    public int updateDramaRoomPayTemplate(DramaRoomPayTemplate dramaRoomPayTemplate);

    /**
     * 批量删除付费模板
     * 
     * @param ids 需要删除的付费模板主键集合
     * @return 结果
     */
    public int deleteDramaRoomPayTemplateByIds(Long[] ids);

    /**
     * 删除付费模板信息
     * 
     * @param id 付费模板主键
     * @return 结果
     */
    public int deleteDramaRoomPayTemplateById(Long id);
}