package com.ruoyi.drama.service.impl;

import com.ruoyi.common.drama.vo.UserCoinVO;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.mobile.AppSecurityUtils;
import com.ruoyi.drama.domain.AppUserCoin;
import com.ruoyi.drama.mapper.AppUserCoinMapper;
import com.ruoyi.drama.service.IAppUserCoinService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 用户金币Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-29
 */
@Service
public class AppUserCoinServiceImpl implements IAppUserCoinService
{
    @Autowired
    private AppUserCoinMapper appUserCoinMapper;

    /**
     * 查询用户金币
     *
     * @param id 用户金币主键
     * @return 用户金币
     */
    @Override
    public AppUserCoin selectAppUserCoinById(Long id)
    {
        return appUserCoinMapper.selectAppUserCoinById(id);
    }

    /**
     * 查询用户金币列表
     *
     * @param appUserCoin 用户金币
     * @return 用户金币
     */
    @Override
    public List<AppUserCoin> selectAppUserCoinList(AppUserCoin appUserCoin)
    {
        return appUserCoinMapper.selectAppUserCoinList(appUserCoin);
    }

    /**
     * 新增用户金币
     *
     * @param appUserCoin 用户金币
     * @return 结果
     */
    @Override
    public int insertAppUserCoin(AppUserCoin appUserCoin)
    {
        appUserCoin.setCreateTime(DateUtils.getNowDate());
        return appUserCoinMapper.insertAppUserCoin(appUserCoin);
    }

    /**
     * 修改用户金币
     *
     * @param appUserCoin 用户金币
     * @return 结果
     */
    @Override
    public int updateAppUserCoin(AppUserCoin appUserCoin)
    {
        appUserCoin.setUpdateTime(DateUtils.getNowDate());
        return appUserCoinMapper.updateAppUserCoin(appUserCoin);
    }

    /**
     * 批量删除用户金币
     *
     * @param ids 需要删除的用户金币主键
     * @return 结果
     */
    @Override
    public int deleteAppUserCoinByIds(Long[] ids)
    {
        return appUserCoinMapper.deleteAppUserCoinByIds(ids);
    }

    /**
     * 删除用户金币信息
     *
     * @param id 用户金币主键
     * @return 结果
     */
    @Override
    public int deleteAppUserCoinById(Long id)
    {
        return appUserCoinMapper.deleteAppUserCoinById(id);
    }

    @Override
    public UserCoinVO getUserCoinByUserId(Long appUserId) {
        if(null == appUserId)
            appUserId = AppSecurityUtils.getAppUserId();
        return appUserCoinMapper.getUserCoinByUserId(appUserId);
    }


}
