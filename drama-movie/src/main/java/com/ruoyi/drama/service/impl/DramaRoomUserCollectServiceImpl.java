package com.ruoyi.drama.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.ruoyi.common.drama.dto.DramaRoomUserCollectDTO;
import com.ruoyi.common.drama.vo.UserCollectVO;
import com.ruoyi.common.enums.AppUserActionEnum;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.mobile.AppSecurityUtils;
import com.ruoyi.drama.domain.DramaRoomMovieI18n;
import com.ruoyi.drama.domain.DramaRoomUserCollect;
import com.ruoyi.drama.manager.MovieAsyncManager;
import com.ruoyi.drama.manager.factory.AppUserAsyncFactory;
import com.ruoyi.drama.mapper.DramaRoomMovieI18nMapper;
import com.ruoyi.drama.mapper.DramaRoomUserCollectMapper;
import com.ruoyi.drama.mapper.DramaRoomUserHistoryMapper;
import com.ruoyi.drama.service.IDramaRoomUserCollectService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 【请填写功能名称】Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-04
 */
@Service
public class DramaRoomUserCollectServiceImpl implements IDramaRoomUserCollectService {
    @Autowired
    private DramaRoomUserCollectMapper dramaRoomUserFavoriteMapper;

    @Autowired
    private DramaRoomUserHistoryMapper dramaRoomUserHistoryMapper;

    @Autowired
    private DramaRoomMovieI18nMapper dramaRoomMovieI18nMapper;

    /**
     * 查询【请填写功能名称】
     *
     * @param id 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    @Override
    public DramaRoomUserCollect selectDramaRoomUserCollectById(Long id) {
        return dramaRoomUserFavoriteMapper.selectDramaRoomUserCollectById(id);
    }

    /**
     * 查询【请填写功能名称】列表
     *
     * @param dramaRoomUserFavorite 【请填写功能名称】
     * @return 【请填写功能名称】
     */
    @Override
    public List<DramaRoomUserCollect> selectDramaRoomUserCollectList(DramaRoomUserCollect dramaRoomUserFavorite) {
        return dramaRoomUserFavoriteMapper.selectDramaRoomUserCollectList(dramaRoomUserFavorite);
    }

    /**
     * 新增【请填写功能名称】
     *
     * @param dramaRoomUserFavorite 【请填写功能名称】
     * @return 结果
     */
    @Override
    public int insertDramaRoomUserCollect(DramaRoomUserCollect dramaRoomUserFavorite) {
        dramaRoomUserFavorite.setCreateTime(DateUtils.getNowDate());
        return dramaRoomUserFavoriteMapper.insertDramaRoomUserCollect(dramaRoomUserFavorite);
    }

    /**
     * 修改【请填写功能名称】
     *
     * @param dramaRoomUserFavorite 【请填写功能名称】
     * @return 结果
     */
    @Override
    public int updateDramaRoomUserCollect(DramaRoomUserCollect dramaRoomUserFavorite) {
        dramaRoomUserFavorite.setUpdateTime(DateUtils.getNowDate());
        return dramaRoomUserFavoriteMapper.updateDramaRoomUserCollect(dramaRoomUserFavorite);
    }

    /**
     * 批量删除【请填写功能名称】
     *
     * @param ids 需要删除的【请填写功能名称】主键
     * @return 结果
     */
    @Override
    public int deleteDramaRoomUserCollectByIds(Long[] ids) {
        return dramaRoomUserFavoriteMapper.deleteDramaRoomUserCollectByIds(ids);
    }

    /**
     * 删除【请填写功能名称】信息
     *
     * @param id 【请填写功能名称】主键
     * @return 结果
     */
    @Override
    public int deleteDramaRoomUserCollectById(Long id) {
        return dramaRoomUserFavoriteMapper.deleteDramaRoomUserCollectById(id);
    }

    @Override
    public int collectMovie(DramaRoomUserCollectDTO dramaRoomUserFavoriteDTO) {
        //现根据userId和movieId查询收藏记录
        Long appUserId = null == AppSecurityUtils.getAppUserId() ? dramaRoomUserFavoriteDTO.getUserId() : AppSecurityUtils.getAppUserId();
        DramaRoomUserCollect query = new DramaRoomUserCollect();
        query.setUserId(appUserId);
        query.setMovieId(dramaRoomUserFavoriteDTO.getMovieId());
        List<DramaRoomUserCollect> list = selectDramaRoomUserCollectList(query);
        //
        //没有收藏过 新增一条收藏记录
        if (CollectionUtils.isEmpty(list)) {
            DramaRoomUserCollect dramaRoomUserFavorite = new DramaRoomUserCollect();
            BeanUtils.copyProperties(dramaRoomUserFavoriteDTO, dramaRoomUserFavorite);
            dramaRoomUserFavorite.setStatus(1L);
            dramaRoomUserFavorite.setCreateTime(DateUtils.getNowDate());
            MovieAsyncManager.me().execute(AppUserAsyncFactory.updateDramaActionCount(appUserId, 1L, AppUserActionEnum.COLLECT));
            return insertDramaRoomUserCollect(dramaRoomUserFavorite);
        } else {
            //收藏或者取消收藏
            DramaRoomUserCollect existRecord = list.get(0);
            existRecord.setStatus(existRecord.getStatus() == 0L ? 1L : 0L);
            existRecord.setUpdateTime(DateUtils.getNowDate());
            MovieAsyncManager.me().execute(AppUserAsyncFactory.updateDramaActionCount(appUserId, existRecord.getStatus() == 0L ? -1L : 1L, AppUserActionEnum.COLLECT));
            return updateDramaRoomUserCollect(existRecord);
        }
    }

    @Override
    public List<UserCollectVO> getUserCollect(String languageCode) {
        Long appUserId = AppSecurityUtils.getAppUserId();
        List<UserCollectVO> userCollectVOList = dramaRoomUserFavoriteMapper.selectDramaRoomUserCollectByUserId(appUserId);
        if (CollectionUtil.isNotEmpty(userCollectVOList)) {
            List<Long> movieIdList = userCollectVOList.stream().map(UserCollectVO::getId).collect(Collectors.toList());
            List<DramaRoomMovieI18n> movieI18nList = dramaRoomMovieI18nMapper.selectDramaRoomMovieI18nByMovieIdListAndLanguageCode(movieIdList, languageCode);
            Map<Long, DramaRoomMovieI18n> movieI18nMap = movieI18nList.stream().collect(Collectors.toMap(DramaRoomMovieI18n::getMovieId, Function.identity()));
            for (UserCollectVO userCollectVO : userCollectVOList) {
                userCollectVO.setTitle(movieI18nMap.containsKey(userCollectVO.getId()) ? movieI18nMap.get(userCollectVO.getId()).getTitle() : userCollectVO.getTitle());
                userCollectVO.setCoverImage(movieI18nMap.containsKey(userCollectVO.getId()) ? movieI18nMap.get(userCollectVO.getId()).getCoverImage() : userCollectVO.getCoverImage());
            }
        }
        return userCollectVOList;
    }
}
