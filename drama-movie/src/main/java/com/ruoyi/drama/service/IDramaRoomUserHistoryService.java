package com.ruoyi.drama.service;

import java.util.List;

import com.ruoyi.common.drama.dto.DramaRoomUserHistoryDTO;
import com.ruoyi.common.drama.vo.UserHistoryVO;
import com.ruoyi.drama.domain.DramaRoomUserHistory;
import com.ruoyi.drama.domain.vo.DramaRoomUserHistoryVO;

/**
 * 【请填写功能名称】Service接口
 *
 * <AUTHOR>
 * @date 2025-07-04
 */
public interface IDramaRoomUserHistoryService
{
    /**
     * 查询【请填写功能名称】
     *
     * @param id 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    public DramaRoomUserHistory selectDramaRoomUserHistoryById(Long id);

    /**
     * 查询【请填写功能名称】列表
     *
     * @param dramaRoomUserHistory 【请填写功能名称】
     * @return 【请填写功能名称】集合
     */
    public List<DramaRoomUserHistory> selectDramaRoomUserHistoryList(DramaRoomUserHistory dramaRoomUserHistory);

    /**
     * 新增【请填写功能名称】
     *
     * @param dramaRoomUserHistory 【请填写功能名称】
     * @return 结果
     */
    public int insertDramaRoomUserHistory(DramaRoomUserHistory dramaRoomUserHistory);

    /**
     * 修改【请填写功能名称】
     *
     * @param dramaRoomUserHistory 【请填写功能名称】
     * @return 结果
     */
    public int updateDramaRoomUserHistory(DramaRoomUserHistory dramaRoomUserHistory);

    /**
     * 批量删除【请填写功能名称】
     *
     * @param ids 需要删除的【请填写功能名称】主键集合
     * @return 结果
     */
    public int deleteDramaRoomUserHistoryByIds(Long[] ids);

    /**
     * 删除【请填写功能名称】信息
     *
     * @param id 【请填写功能名称】主键
     * @return 结果
     */
    public int deleteDramaRoomUserHistoryById(Long id);

    List<DramaRoomUserHistoryVO> queryUserHistory(Long userId);

    int reportViewHistory(DramaRoomUserHistory dramaRoomUserHistory);

    int reportViewHistory(DramaRoomUserHistoryDTO dramaRoomUserHistory);


    List<UserHistoryVO> getUserHistory(String languageCode);

}
