package com.ruoyi.drama.service;

import com.ruoyi.drama.domain.DramaRoomOrder;

import java.util.List;

/**
 * 订单Service接口
 * 
 * <AUTHOR>
 * @date 2025-08-08
 */
public interface IDramaRoomOrderService 
{
    /**
     * 查询订单
     * 
     * @param id 订单主键
     * @return 订单
     */
    public DramaRoomOrder selectDramaRoomOrderById(Long id);

    /**
     * 查询订单列表
     * 
     * @param dramaRoomOrder 订单
     * @return 订单集合
     */
    public List<DramaRoomOrder> selectDramaRoomOrderList(DramaRoomOrder dramaRoomOrder);

    /**
     * 新增订单
     * 
     * @param dramaRoomOrder 订单
     * @return 结果
     */
    public int insertDramaRoomOrder(DramaRoomOrder dramaRoomOrder);

    /**
     * 修改订单
     * 
     * @param dramaRoomOrder 订单
     * @return 结果
     */
    public int updateDramaRoomOrder(DramaRoomOrder dramaRoomOrder);

    /**
     * 批量删除订单
     * 
     * @param ids 需要删除的订单主键集合
     * @return 结果
     */
    public int deleteDramaRoomOrderByIds(Long[] ids);

    /**
     * 删除订单信息
     * 
     * @param id 订单主键
     * @return 结果
     */
    public int deleteDramaRoomOrderById(Long id);
}