package com.ruoyi.drama.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.drama.mapper.DramaRoomVideoChannelCoinMapper;
import com.ruoyi.drama.domain.DramaRoomVideoChannelCoin;
import com.ruoyi.drama.service.IDramaRoomVideoChannelCoinService;

/**
 * 视频渠道扣金币配置Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-08-05
 */
@Service
public class DramaRoomVideoChannelCoinServiceImpl implements IDramaRoomVideoChannelCoinService 
{
    @Autowired
    private DramaRoomVideoChannelCoinMapper dramaRoomVideoChannelCoinMapper;

    /**
     * 查询视频渠道扣金币配置
     * 
     * @param id 视频渠道扣金币配置主键
     * @return 视频渠道扣金币配置
     */
    @Override
    public DramaRoomVideoChannelCoin selectDramaRoomVideoChannelCoinById(Long id)
    {
        return dramaRoomVideoChannelCoinMapper.selectDramaRoomVideoChannelCoinById(id);
    }

    /**
     * 查询视频渠道扣金币配置列表
     * 
     * @param dramaRoomVideoChannelCoin 视频渠道扣金币配置
     * @return 视频渠道扣金币配置
     */
    @Override
    public List<DramaRoomVideoChannelCoin> selectDramaRoomVideoChannelCoinList(DramaRoomVideoChannelCoin dramaRoomVideoChannelCoin)
    {
        return dramaRoomVideoChannelCoinMapper.selectDramaRoomVideoChannelCoinList(dramaRoomVideoChannelCoin);
    }

    /**
     * 新增视频渠道扣金币配置
     * 
     * @param dramaRoomVideoChannelCoin 视频渠道扣金币配置
     * @return 结果
     */
    @Override
    public int insertDramaRoomVideoChannelCoin(DramaRoomVideoChannelCoin dramaRoomVideoChannelCoin)
    {
        dramaRoomVideoChannelCoin.setCreateTime(DateUtils.getNowDate());
        return dramaRoomVideoChannelCoinMapper.insertDramaRoomVideoChannelCoin(dramaRoomVideoChannelCoin);
    }

    /**
     * 修改视频渠道扣金币配置
     * 
     * @param dramaRoomVideoChannelCoin 视频渠道扣金币配置
     * @return 结果
     */
    @Override
    public int updateDramaRoomVideoChannelCoin(DramaRoomVideoChannelCoin dramaRoomVideoChannelCoin)
    {
        dramaRoomVideoChannelCoin.setUpdateTime(DateUtils.getNowDate());
        return dramaRoomVideoChannelCoinMapper.updateDramaRoomVideoChannelCoin(dramaRoomVideoChannelCoin);
    }

    /**
     * 批量删除视频渠道扣金币配置
     * 
     * @param ids 需要删除的视频渠道扣金币配置主键
     * @return 结果
     */
    @Override
    public int deleteDramaRoomVideoChannelCoinByIds(Long[] ids)
    {
        return dramaRoomVideoChannelCoinMapper.deleteDramaRoomVideoChannelCoinByIds(ids);
    }

    /**
     * 删除视频渠道扣金币配置信息
     * 
     * @param id 视频渠道扣金币配置主键
     * @return 结果
     */
    @Override
    public int deleteDramaRoomVideoChannelCoinById(Long id)
    {
        return dramaRoomVideoChannelCoinMapper.deleteDramaRoomVideoChannelCoinById(id);
    }
}
