package com.ruoyi.drama.service.impl;

import java.util.Collections;
import java.util.List;

import com.ruoyi.common.drama.vo.CoinRuleVO;
import com.ruoyi.common.drama.vo.UserCoinVO;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.drama.domain.DramaRoomCoinRule;
import com.ruoyi.drama.mapper.DramaRoomCoinRuleMapper;
import com.ruoyi.drama.service.IDramaRoomCoinRuleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 充值金币规则Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-29
 */
@Service
public class DramaRoomCoinRuleServiceImpl implements IDramaRoomCoinRuleService
{
    @Autowired
    private DramaRoomCoinRuleMapper dramaRoomCoinRuleMapper;

    /**
     * 查询充值金币规则
     *
     * @param id 充值金币规则主键
     * @return 充值金币规则
     */
    @Override
    public DramaRoomCoinRule selectDramaRoomCoinRuleById(Long id)
    {
        return dramaRoomCoinRuleMapper.selectDramaRoomCoinRuleById(id);
    }

    /**
     * 查询充值金币规则列表
     *
     * @param dramaRoomCoinRule 充值金币规则
     * @return 充值金币规则
     */
    @Override
    public List<DramaRoomCoinRule> selectDramaRoomCoinRuleList(DramaRoomCoinRule dramaRoomCoinRule)
    {
        return dramaRoomCoinRuleMapper.selectDramaRoomCoinRuleList(dramaRoomCoinRule);
    }

    /**
     * 新增充值金币规则
     *
     * @param dramaRoomCoinRule 充值金币规则
     * @return 结果
     */
    @Override
    public int insertDramaRoomCoinRule(DramaRoomCoinRule dramaRoomCoinRule)
    {
        dramaRoomCoinRule.setCreateTime(DateUtils.getNowDate());
        return dramaRoomCoinRuleMapper.insertDramaRoomCoinRule(dramaRoomCoinRule);
    }

    /**
     * 修改充值金币规则
     *
     * @param dramaRoomCoinRule 充值金币规则
     * @return 结果
     */
    @Override
    public int updateDramaRoomCoinRule(DramaRoomCoinRule dramaRoomCoinRule)
    {
        dramaRoomCoinRule.setUpdateTime(DateUtils.getNowDate());
        return dramaRoomCoinRuleMapper.updateDramaRoomCoinRule(dramaRoomCoinRule);
    }

    /**
     * 批量删除充值金币规则
     *
     * @param ids 需要删除的充值金币规则主键
     * @return 结果
     */
    @Override
    public int deleteDramaRoomCoinRuleByIds(Long[] ids)
    {
        return dramaRoomCoinRuleMapper.deleteDramaRoomCoinRuleByIds(ids);
    }

    /**
     * 删除充值金币规则信息
     *
     * @param id 充值金币规则主键
     * @return 结果
     */
    @Override
    public int deleteDramaRoomCoinRuleById(Long id)
    {
        return dramaRoomCoinRuleMapper.deleteDramaRoomCoinRuleById(id);
    }

    @Override
    public List<CoinRuleVO> getCoinRuleList(Integer type) {
        return dramaRoomCoinRuleMapper.getCoinRuleList(type);
    }

}
