package com.ruoyi.drama.service;

import com.ruoyi.common.drama.dto.DramaRoomMovieUnlockVideoDTO;
import com.ruoyi.common.drama.vo.UnlockEpisodeRecordsVO;

import javax.validation.Valid;
import java.util.List;

/**
 * 短剧Service接口 
 *
 * <AUTHOR>
 * @date 2025-07-04
 */
public interface IDramaRoomUserUnlockVideoService {
    void unlockVideo(@Valid DramaRoomMovieUnlockVideoDTO dto);

    List<UnlockEpisodeRecordsVO> unlockEpisodeRecords();
}
