package com.ruoyi.drama.service.impl;

import com.ruoyi.common.drama.vo.*;
import com.ruoyi.common.enums.DramaRoomParamConfigEnum;
import com.ruoyi.common.enums.SortType;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.drama.domain.*;
import com.ruoyi.drama.mapper.*;
import com.ruoyi.drama.service.IDramaRoomParamConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 【请填写功能名称】Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-04
 */
@Service
public class DramaRoomParamConfigServiceImpl implements IDramaRoomParamConfigService {
    @Autowired
    private DramaRoomParamConfigMapper dramaRoomParamConfigMapper;

    @Autowired
    private DramaRoomMovieMapper dramaRoomMovieMapper;

    @Autowired
    private DramaRoomVideoMapper dramaRoomVideoMapper;

    @Autowired
    private DramaRoomCategoryMapper dramaRoomCategoryMapper;

    @Autowired
    private DramaRoomAreaMapper dramaRoomAreaMapper;

    @Autowired
    private DramaRoomAudioMapper dramaRoomAudioMapper;

    @Autowired
    private DramaRoomCategoryI18nMapper dramaRoomCategoryI18nMapper;

    @Autowired
    private DramaRoomMovieCategoryMapper dramaRoomMovieCategoryMapper;

    @Autowired
    private DramaRoomMovieI18nMapper dramaRoomMovieI18nMapper;

    @Autowired
    private DramaRoomAreaI18nMapper dramaRoomAreaI18nMapper;

    @Autowired
    private DramaRoomAudioI18nMapper dramaRoomAudioI18nMapper;

    @Autowired
    private DramaRoomVideoI18nMapper dramaRoomVideoI18nMapper;

    /**
     * 查询【请填写功能名称】
     *
     * @param id 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    @Override
    public DramaRoomParamConfig selectDramaRoomParamConfigById(Long id) {
        return dramaRoomParamConfigMapper.selectDramaRoomParamConfigById(id);
    }

    /**
     * 查询【请填写功能名称】列表
     *
     * @param dramaRoomParamConfig 【请填写功能名称】
     * @return 【请填写功能名称】
     */
    @Override
    public List<DramaRoomParamConfig> selectDramaRoomParamConfigList(DramaRoomParamConfig dramaRoomParamConfig) {
        return dramaRoomParamConfigMapper.selectDramaRoomParamConfigList(dramaRoomParamConfig);
    }

    /**
     * 新增【请填写功能名称】
     *
     * @param dramaRoomParamConfig 【请填写功能名称】
     * @return 结果
     */
    @Override
    public int insertDramaRoomParamConfig(DramaRoomParamConfig dramaRoomParamConfig) {
        dramaRoomParamConfig.setCreateTime(DateUtils.getNowDate());
        return dramaRoomParamConfigMapper.insertDramaRoomParamConfig(dramaRoomParamConfig);
    }

    /**
     * 修改【请填写功能名称】
     *
     * @param dramaRoomParamConfig 【请填写功能名称】
     * @return 结果
     */
    @Override
    public int updateDramaRoomParamConfig(DramaRoomParamConfig dramaRoomParamConfig) {
        dramaRoomParamConfig.setUpdateTime(DateUtils.getNowDate());
        return dramaRoomParamConfigMapper.updateDramaRoomParamConfig(dramaRoomParamConfig);
    }

    /**
     * 批量删除【请填写功能名称】
     *
     * @param ids 需要删除的【请填写功能名称】主键
     * @return 结果
     */
    @Override
    public int deleteDramaRoomParamConfigByIds(Long[] ids) {
        return dramaRoomParamConfigMapper.deleteDramaRoomParamConfigByIds(ids);
    }

    /**
     * 删除【请填写功能名称】信息
     *
     * @param id 【请填写功能名称】主键
     * @return 结果
     */
    @Override
    public int deleteDramaRoomParamConfigById(Long id) {
        return dramaRoomParamConfigMapper.deleteDramaRoomParamConfigById(id);
    }

    @Override
    public List<MovieCategoryVO> selectRecommendConfigByKey(String key, String languageCode) {
        List<MovieCategoryVO> movieCategoryVOS = new ArrayList<>();
        String value = dramaRoomParamConfigMapper.selectDramaRoomParamConfigByKey(key);
        Long[] categoryIds = null;
        if (value != null && !value.isEmpty()) {
            categoryIds = Arrays.stream(value.split(","))
                    .map(String::trim)
                    .map(Long::valueOf)
                    .toArray(Long[]::new);
        }
        // 根据分类ID 批量查询翻译后的分类名称
        List<DramaRoomCategoryI18n> categoryI18nList = dramaRoomCategoryI18nMapper.selectByCategoryIdListAndLanguageCode(Arrays.asList(categoryIds), languageCode);
        Map<Long, String> categoryI18nMap = categoryI18nList.stream().collect(Collectors.toMap(DramaRoomCategoryI18n::getCategoryId, DramaRoomCategoryI18n::getCategoryName));
        // 根据分类ID 批量查询分类下的剧集
        List<DramaRoomMovieCategory> movieCategoryList = dramaRoomMovieCategoryMapper.selectByCategoryIdList(Arrays.asList(categoryIds));
        List<Long> movieIdList = movieCategoryList.stream().map(DramaRoomMovieCategory::getMovieId).distinct().collect(Collectors.toList());
        //查询翻译后的剧集
        List<DramaRoomMovieI18n> movieI18nList = dramaRoomMovieI18nMapper.selectDramaRoomMovieI18nByMovieIdListAndLanguageCode(movieIdList, languageCode);
        Map<Long, DramaRoomMovieI18n> movieI18nMap = movieI18nList.stream().collect(Collectors.toMap(DramaRoomMovieI18n::getMovieId, Function.identity()));
        for (Long categoryId : categoryIds) {
            MovieCategoryVO movieCategoryVO = new MovieCategoryVO();
            DramaRoomCategory dramaRoomCategory = dramaRoomCategoryMapper.selectDramaRoomCategoryById(categoryId);
            movieCategoryVO.setId(dramaRoomCategory.getId());
            movieCategoryVO.setName(categoryI18nMap.containsKey(categoryId) ? categoryI18nMap.get(categoryId) : dramaRoomCategory.getCategoryName());
            movieCategoryVO.setSort(dramaRoomCategory.getSort());
            List<MovieVO> voList = DramaRoomMovie.toVOList(dramaRoomMovieMapper.selectDramaRoomMovieByCategoryId(categoryId));
            //字段翻译
            for (MovieVO movieVO : voList) {
                movieVO.setTitle(movieI18nMap.containsKey(movieVO.getId()) ? movieI18nMap.get(movieVO.getId()).getTitle() : movieVO.getTitle());
                movieVO.setCoverImage(movieI18nMap.containsKey(movieVO.getId()) ? movieI18nMap.get(movieVO.getId()).getCoverImage() : movieVO.getCoverImage());
            }
            movieCategoryVO.setMovies(voList);
            movieCategoryVOS.add(movieCategoryVO);
        }
        return movieCategoryVOS;
    }

    @Override
    public MovieFilterVO selectFilterData(String languageCode) {
        MovieFilterVO movieFilterVO = new MovieFilterVO();
        List<MovieAreasVO> movieAreasVOList = DramaRoomArea.toVOList(dramaRoomAreaMapper.selectDramaRoomAreaList(new DramaRoomArea()));
        //查询与之对应的地区翻译
        DramaRoomAreaI18n dramaRoomAreaI18nQuery = new DramaRoomAreaI18n();
        dramaRoomAreaI18nQuery.setLanguageCode(languageCode);
        List<DramaRoomAreaI18n> dramaRoomAreaI18nList = dramaRoomAreaI18nMapper.selectDramaRoomAreaI18nList(dramaRoomAreaI18nQuery);
        Map<Long, String> areaI18nMap = dramaRoomAreaI18nList.stream().collect(Collectors.toMap(DramaRoomAreaI18n::getAreaId, DramaRoomAreaI18n::getArea));
        movieAreasVOList.forEach(movieAreasVO -> movieAreasVO.setName(areaI18nMap.containsKey(movieAreasVO.getId()) ? areaI18nMap.get(movieAreasVO.getId()) : movieAreasVO.getName()));
        movieFilterVO.setAreaId(movieAreasVOList);
        //处理音频
        DramaRoomAudioI18n dramaRoomAudioI18nQuery = new DramaRoomAudioI18n();
        dramaRoomAudioI18nQuery.setLanguageCode(languageCode);
        List<DramaRoomAudioI18n> dramaRoomAudioI18nList = dramaRoomAudioI18nMapper.selectDramaRoomAudioI18nList(dramaRoomAudioI18nQuery);
        Map<Long, String> audioI18nMap = dramaRoomAudioI18nList.stream().collect(Collectors.toMap(DramaRoomAudioI18n::getAudioId, DramaRoomAudioI18n::getAudio));
        List<MovieAudioVO> movieAudioVOList = DramaRoomAudio.toVOList(dramaRoomAudioMapper.selectDramaRoomAudioList(new DramaRoomAudio()));
        movieAudioVOList.forEach(movieAudioVO -> movieAudioVO.setName(audioI18nMap.containsKey(movieAudioVO.getId()) ? audioI18nMap.get(movieAudioVO.getId()) : movieAudioVO.getName()));
        movieFilterVO.setAudioId(movieAudioVOList);

        String value = dramaRoomParamConfigMapper.selectDramaRoomParamConfigByKey(DramaRoomParamConfigEnum.APP_CATEGORY_CONFIG.getCode());
        Long[] categoryIds = null;
        if (value != null && !value.isEmpty()) {
            categoryIds = Arrays.stream(value.split(","))
                    .map(String::trim)
                    .map(Long::valueOf)
                    .toArray(Long[]::new);
        }
        List<DramaRoomCategory> categories = dramaRoomCategoryMapper.selectDramaRoomCategoryByIds(categoryIds);
        // 根据分类ID 批量查询翻译后的分类名称
        List<DramaRoomCategoryI18n> categoryI18nList = dramaRoomCategoryI18nMapper.selectByCategoryIdListAndLanguageCode(Arrays.asList(categoryIds), languageCode);
        Map<Long, String> categoryI18nMap = categoryI18nList.stream().collect(Collectors.toMap(DramaRoomCategoryI18n::getCategoryId, DramaRoomCategoryI18n::getCategoryName));
        List<MovieCategoryVO> voList = DramaRoomCategory.toVOList(categories);
        voList.forEach(movieCategoryVO -> movieCategoryVO.setName(categoryI18nMap.containsKey(movieCategoryVO.getId()) ? categoryI18nMap.get(movieCategoryVO.getId()) : movieCategoryVO.getName()));
        movieFilterVO.setCategoryId(voList);

        //需要定义各种语言与之对应的枚举 todo
        MovieSortVO vo1 = new MovieSortVO(SortType.NEW.getKey());
        MovieSortVO vo2 = new MovieSortVO(SortType.HOT.getKey());
        movieFilterVO.setSort(Arrays.asList(vo1, vo2));

        return movieFilterVO;
    }

    @Override
    public List<PushMovieVideoVO> selectRecommendMovieConfigByKey(String key, String languageCode) {
        String categoryId = dramaRoomParamConfigMapper.selectDramaRoomParamConfigByKey(key);
        List<DramaRoomMovieCategory> movieCategoryList = dramaRoomMovieCategoryMapper.selectDramaRoomMovieCategoryByCategoryId(Long.valueOf(categoryId));
        Long[] movieIds = movieCategoryList.stream()
                .map(DramaRoomMovieCategory::getMovieId)
                .toArray(Long[]::new);
        // 获取短剧数据
        List<DramaRoomMovie> movies = dramaRoomMovieMapper.selectDramaRoomMovieByIds(movieIds);
        List<PushMovieVideoVO> pushList = DramaRoomMovie.toPushList(movies);
        //查询短剧多语言
        List<DramaRoomMovieI18n> movieI18nList = dramaRoomMovieI18nMapper.selectDramaRoomMovieI18nByMovieIdListAndLanguageCode(Arrays.asList(movieIds), languageCode);
        Map<Long, DramaRoomMovieI18n> movieI18nMap = movieI18nList.stream().collect(Collectors.toMap(DramaRoomMovieI18n::getMovieId, Function.identity()));

        // 根据ids获取短剧第一集
        for (PushMovieVideoVO pushMovieVideoVO : pushList) {
            pushMovieVideoVO.setTitle(movieI18nMap.containsKey(pushMovieVideoVO.getId()) ? movieI18nMap.get(pushMovieVideoVO.getId()).getTitle() : pushMovieVideoVO.getTitle());
            pushMovieVideoVO.setDescription(movieI18nMap.containsKey(pushMovieVideoVO.getId()) ? movieI18nMap.get(pushMovieVideoVO.getId()).getDescription() : pushMovieVideoVO.getDescription());
            List<DramaRoomVideo> dramaRoomVideo = dramaRoomVideoMapper.selectDramaRoomVideoByMovieId(pushMovieVideoVO.getId());
            List<VideoVO> videoVOList = DramaRoomVideo.toVideoVOList(dramaRoomVideo);
            VideoVO videoVO = videoVOList.get(0);
            //查询多语言字幕
            DramaRoomVideoI18n dramaRoomVideoI18nQuery = new DramaRoomVideoI18n();
            dramaRoomVideoI18nQuery.setVideoId(videoVO.getId());
            List<DramaRoomVideoI18n> dramaRoomVideoI18nList = dramaRoomVideoI18nMapper.selectDramaRoomVideoI18nList(dramaRoomVideoI18nQuery);
            List<DramaRoomVideoI18nVO> videoI18nVOList = DramaRoomVideoI18n.toVOList(dramaRoomVideoI18nList);
            videoVO.setSubtitleUrlList(dramaRoomVideoI18nList.isEmpty() ? null : videoI18nVOList);
            pushMovieVideoVO.setVideo(videoVO);
        }
        return pushList;
    }

}
