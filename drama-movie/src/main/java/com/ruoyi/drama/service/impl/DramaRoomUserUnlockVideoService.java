package com.ruoyi.drama.service.impl;

import com.ruoyi.common.drama.dto.DramaRoomMovieUnlockVideoDTO;
import com.ruoyi.common.drama.vo.UnlockEpisodeRecordsVO;
import com.ruoyi.common.drama.vo.UserCoinVO;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.mobile.AppSecurityUtils;
import com.ruoyi.common.utils.uuid.IdUtils;
import com.ruoyi.drama.domain.*;
import com.ruoyi.drama.mapper.*;
import com.ruoyi.drama.service.IDramaRoomUserUnlockVideoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

/**
 * 短剧Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-04
 */
@Service
public class DramaRoomUserUnlockVideoService  implements IDramaRoomUserUnlockVideoService {


    @Autowired
    private DramaRoomUserUnlockVideoMapper dramaRoomUserUnlockVideoMapper;

    @Autowired
    private DramaRoomVideoMapper dramaRoomVideoMapper;

    @Autowired
    private DramaRoomVideoChannelCoinMapper dramaRoomVideoChannelCoinMapper;

    @Autowired
    private AppUserCoinMapper appUserCoinMapper;

    @Autowired
    private AppUserCoinLogMapper appUserCoinLogMapper;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void unlockVideo(DramaRoomMovieUnlockVideoDTO dto) {
        Long movieId = dto.getMovieId();
        Long videoId = dto.getVideoId();
        Long appUserId = AppSecurityUtils.getAppUserId();
        AppUserCoin appUserCoin = appUserCoinMapper.selectUserCoinByUserId(appUserId);

        // 查询渠道视频所需金币进行扣费解锁，TODO 等待sem的深链逻辑完成后，才可以获取金币扣费规则ID
        DramaRoomVideoChannelCoin dramaRoomVideoChannelCoin = dramaRoomVideoChannelCoinMapper
                .selectDramaRoomVideoChannelCoinByCoinRuleIdAndMovieIdAndVideoId(0L, movieId, videoId);
        Integer coinRequired = 0;
        if (dramaRoomVideoChannelCoin != null){
            coinRequired = dramaRoomVideoChannelCoin.getCoin();
        }else{
            DramaRoomVideo dramaRoomVideo = dramaRoomVideoMapper.selectDramaRoomVideoById(videoId);
            if (dramaRoomVideo != null){
                coinRequired = dramaRoomVideo.getCoin();
            }
        }

        if (coinRequired == 0){
            return;
        }

        Long coins = appUserCoin.getCoins();
        Long rewardCoins = appUserCoin.getRewardCoins();
        // TODO 优先扣费奖励币还是充值币需确认

        long totalCoins = coins + rewardCoins;
        if (coinRequired > totalCoins) {
            throw new ServiceException("当前用户金币不足，请充值后再试");
        }

        appUserCoin.setCoins(coins - coinRequired);
        appUserCoinMapper.updateAppUserCoin(appUserCoin);

        AppUserCoinLog appUserCoinLogReward = new AppUserCoinLog();
        appUserCoinLogReward.setType(3L);
        appUserCoinLogReward.setTradeType(1);
        appUserCoinLogReward.setUserId(appUserId);
        appUserCoinLogReward.setCoins(Long.valueOf(coinRequired));
        appUserCoinLogReward.setBeforeCoins(coins);
        appUserCoinLogReward.setAfterCoins(Optional.ofNullable(appUserCoin.getCoins()).orElse(0L) + Optional.of(appUserCoin.getRewardCoins()).orElse(0L));
        appUserCoinLogReward.setSourceId(videoId);
        appUserCoinLogMapper.insertAppUserCoinLog(appUserCoinLogReward);

        DramaRoomUserUnlockVideo dramaRoomUserUnlockVideo = dramaRoomUserUnlockVideoMapper.selectByUserIdAndMovieIdAndVideoId(appUserId,movieId, videoId);
        if (dramaRoomUserUnlockVideo == null) {
            DramaRoomUserUnlockVideo userUnlockVideo = new DramaRoomUserUnlockVideo();
            userUnlockVideo.setId(IdUtils.snowflakeId());
            userUnlockVideo.setUserId(AppSecurityUtils.getAppUserId());
            userUnlockVideo.setMovieId(movieId);
            userUnlockVideo.setVideoId(videoId);
            userUnlockVideo.setUnlockType(0);
            userUnlockVideo.setCostCoin(coinRequired);
            dramaRoomUserUnlockVideoMapper.insertDramaRoomUserUnlockVideo(userUnlockVideo);
        }

    }

    @Override
    public List<UnlockEpisodeRecordsVO> unlockEpisodeRecords() {
        Long appUserId = AppSecurityUtils.getAppUserId();
        return dramaRoomUserUnlockVideoMapper.unlockEpisodeRecords(appUserId);
    }
}
