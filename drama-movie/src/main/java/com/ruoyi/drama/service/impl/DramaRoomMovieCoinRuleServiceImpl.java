package com.ruoyi.drama.service.impl;

import java.util.*;
import java.util.stream.Collectors;

import cn.hutool.core.collection.CollectionUtil;
import com.ruoyi.common.drama.dto.DramaRoomMovieCoinRuleDTO;
import com.ruoyi.common.drama.dto.DramaRoomVideoChannelCoinDTO;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.drama.domain.DramaRoomMovie;
import com.ruoyi.drama.domain.DramaRoomVideo;
import com.ruoyi.drama.domain.DramaRoomVideoChannelCoin;
import com.ruoyi.drama.mapper.DramaRoomMovieMapper;
import com.ruoyi.drama.mapper.DramaRoomVideoChannelCoinMapper;
import com.ruoyi.drama.mapper.DramaRoomVideoMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.drama.mapper.DramaRoomMovieCoinRuleMapper;
import com.ruoyi.drama.domain.DramaRoomMovieCoinRule;
import com.ruoyi.drama.service.IDramaRoomMovieCoinRuleService;
import org.springframework.transaction.annotation.Transactional;

/**
 * 短剧扣费规则Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-08-05
 */
@Slf4j
@Service
public class DramaRoomMovieCoinRuleServiceImpl implements IDramaRoomMovieCoinRuleService
{
    @Autowired
    private DramaRoomMovieCoinRuleMapper dramaRoomMovieCoinRuleMapper;

    @Autowired
    private DramaRoomVideoChannelCoinMapper dramaRoomVideoChannelCoinMapper;

    @Autowired
    private DramaRoomMovieMapper dramaRoomMovieMapper;

    @Autowired
    private DramaRoomVideoMapper dramaRoomVideoMapper;

    /**
     * 查询短剧扣费规则
     *
     * @param id 短剧扣费规则主键
     * @return 短剧扣费规则
     */
    @Override
    public DramaRoomMovieCoinRule selectDramaRoomMovieCoinRuleById(Long id)
    {
        return dramaRoomMovieCoinRuleMapper.selectDramaRoomMovieCoinRuleById(id);
    }

    /**
     * 查询短剧扣费规则列表
     *
     * @param dramaRoomMovieCoinRule 短剧扣费规则
     * @return 短剧扣费规则
     */
    @Override
    public List<DramaRoomMovieCoinRule> selectDramaRoomMovieCoinRuleList(DramaRoomMovieCoinRule dramaRoomMovieCoinRule)
    {
        return dramaRoomMovieCoinRuleMapper.selectDramaRoomMovieCoinRuleList(dramaRoomMovieCoinRule);
    }

    /**
     * 新增短剧扣费规则
     *
     * @param dramaRoomMovieCoinRule 短剧扣费规则
     * @return 结果
     */
    @Override
    public int insertDramaRoomMovieCoinRule(DramaRoomMovieCoinRule dramaRoomMovieCoinRule)
    {
        dramaRoomMovieCoinRule.setCreateTime(DateUtils.getNowDate());
        return dramaRoomMovieCoinRuleMapper.insertDramaRoomMovieCoinRule(dramaRoomMovieCoinRule);
    }

    /**
     * 修改短剧扣费规则
     *
     * @param dramaRoomMovieCoinRule 短剧扣费规则
     * @return 结果
     */
    @Override
    public int updateDramaRoomMovieCoinRule(DramaRoomMovieCoinRule dramaRoomMovieCoinRule)
    {
        dramaRoomMovieCoinRule.setUpdateTime(DateUtils.getNowDate());
        return dramaRoomMovieCoinRuleMapper.updateDramaRoomMovieCoinRule(dramaRoomMovieCoinRule);
    }

    /**
     * 批量删除短剧扣费规则
     *
     * @param ids 需要删除的短剧扣费规则主键
     * @return 结果
     */
    @Override
    public int deleteDramaRoomMovieCoinRuleByIds(Long[] ids)
    {
        return dramaRoomMovieCoinRuleMapper.deleteDramaRoomMovieCoinRuleByIds(ids);
    }

    /**
     * 删除短剧扣费规则信息
     *
     * @param id 短剧扣费规则主键
     * @return 结果
     */
    @Override
    public int deleteDramaRoomMovieCoinRuleById(Long id)
    {
        return dramaRoomMovieCoinRuleMapper.deleteDramaRoomMovieCoinRuleById(id);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public int saveMovieCoinRule(DramaRoomMovieCoinRuleDTO dramaRoomMovieCoinRuleDTO) {
        Long id = dramaRoomMovieCoinRuleDTO.getId();
        DramaRoomMovieCoinRule dramaRoomMovieCoinRule = dramaRoomMovieCoinRuleMapper.selectDramaRoomMovieCoinRuleById(id);
        //新增
        if(null == dramaRoomMovieCoinRule){
            DramaRoomMovieCoinRule newDramaRoomMovieCoinRule = new DramaRoomMovieCoinRule();
            newDramaRoomMovieCoinRule.setMovieId(dramaRoomMovieCoinRuleDTO.getMovieId());
            newDramaRoomMovieCoinRule.setName(dramaRoomMovieCoinRuleDTO.getName());
            newDramaRoomMovieCoinRule.setCreateTime(DateUtils.getNowDate());
            newDramaRoomMovieCoinRule.setCreateBy(SecurityUtils.getUserId().toString());
            dramaRoomMovieCoinRuleMapper.insertDramaRoomMovieCoinRule(newDramaRoomMovieCoinRule);
            id = newDramaRoomMovieCoinRule.getId();
        }else {
            //修改
            dramaRoomMovieCoinRule.setMovieId(dramaRoomMovieCoinRuleDTO.getMovieId());
            dramaRoomMovieCoinRule.setName(dramaRoomMovieCoinRuleDTO.getName());
            dramaRoomMovieCoinRule.setUpdateTime(DateUtils.getNowDate());
            dramaRoomMovieCoinRule.setUpdateBy(SecurityUtils.getUserId().toString());
            dramaRoomMovieCoinRuleMapper.updateDramaRoomMovieCoinRule(dramaRoomMovieCoinRule);
        }
        //查询所关联的剧
        DramaRoomMovie dramaRoomMovie = dramaRoomMovieMapper.selectDramaRoomMovieById(dramaRoomMovieCoinRuleDTO.getMovieId());
        if(null == dramaRoomMovie){
            throw new RuntimeException("所关联的剧不存在");
        }
        //查询短剧下的所有视频
        DramaRoomVideo dramaRoomVideoQuery = new DramaRoomVideo();
        dramaRoomVideoQuery.setMovieId(dramaRoomMovieCoinRuleDTO.getMovieId());
        List<DramaRoomVideo> dramaRoomVideoList = dramaRoomVideoMapper.selectDramaRoomVideoList(dramaRoomVideoQuery);
        if(CollectionUtil.isEmpty(dramaRoomVideoList)){
            throw new RuntimeException("当前电影尚未配置视频资源");
        }
        //查询该规则下配置数据
        DramaRoomVideoChannelCoin dramaRoomVideoChannelCoinQuery = new DramaRoomVideoChannelCoin();
        dramaRoomVideoChannelCoinQuery.setCoinRuleId(id);
        List<DramaRoomVideoChannelCoin> dramaRoomVideoChannelCoinList = dramaRoomVideoChannelCoinMapper.selectDramaRoomVideoChannelCoinList(dramaRoomVideoChannelCoinQuery);
        List<DramaRoomVideoChannelCoinDTO> videoChannelCoinList = dramaRoomMovieCoinRuleDTO.getVideoChannelCoinList();
        if(CollectionUtil.isEmpty(dramaRoomVideoChannelCoinList)){
            //构建映射关系
            Map<String, DramaRoomVideo> videoMap = new HashMap<>();
            if(CollectionUtil.isNotEmpty(dramaRoomVideoList)){
                for (DramaRoomVideo video : dramaRoomVideoList) {
                    videoMap.put(video.getMovieId()+"-"+ video.getVideoNum(), video);
                }
            }
            //新增
            List<DramaRoomVideoChannelCoin> inertList = new ArrayList<>();
            for (DramaRoomVideoChannelCoinDTO dramaRoomVideoChannelCoinDTO : videoChannelCoinList) {
                Integer startNum = dramaRoomVideoChannelCoinDTO.getStartNum();
                Integer endNum = dramaRoomVideoChannelCoinDTO.getEndNum();
                for (int i = startNum; i <= endNum; i++) {
                    DramaRoomVideoChannelCoin dramaRoomVideoChannelCoin = new DramaRoomVideoChannelCoin();
                    dramaRoomVideoChannelCoin.setCoinRuleId(id);
                    dramaRoomVideoChannelCoin.setMovieId(dramaRoomMovieCoinRuleDTO.getMovieId());
                    dramaRoomVideoChannelCoin.setCoin(dramaRoomVideoChannelCoinDTO.getCoin());
                    dramaRoomVideoChannelCoin.setNum(i);
                    dramaRoomVideoChannelCoin.setCreateTime(DateUtils.getNowDate());
                    dramaRoomVideoChannelCoin.setCreateBy(SecurityUtils.getUserId().toString());
                    if(videoMap.containsKey(dramaRoomMovie.getId() + "-" + i)){
                        DramaRoomVideo dramaRoomVideo = videoMap.get(dramaRoomMovie.getId() + "-" + i);
                        dramaRoomVideoChannelCoin.setVideoId(dramaRoomVideo.getId());
                        dramaRoomVideoChannelCoin.setState(dramaRoomVideo.getStatus());
                    }
                    inertList.add(dramaRoomVideoChannelCoin);
                }
            }
            return dramaRoomVideoChannelCoinMapper.insertDramaRoomVideoChannelCoinBatch(inertList);
        }else {
            //修改
            Map<String, Integer> coinMap = new HashMap<>();
            for (DramaRoomVideoChannelCoinDTO dramaRoomVideoChannelCoinDTO : videoChannelCoinList) {
                Integer startNum = dramaRoomVideoChannelCoinDTO.getStartNum();
                Integer endNum = dramaRoomVideoChannelCoinDTO.getEndNum();
                for (int i = startNum; i <= endNum; i++){
                    String key = dramaRoomMovie.getId() + "-" + i;
                    coinMap.put(key, dramaRoomVideoChannelCoinDTO.getCoin());
                }
            }
            //填充属性
            for (DramaRoomVideoChannelCoin dramaRoomVideoChannelCoin : dramaRoomVideoChannelCoinList) {
                String key = dramaRoomVideoChannelCoin.getMovieId() + "-" + dramaRoomVideoChannelCoin.getNum();
                if(coinMap.containsKey(key)){
                    dramaRoomVideoChannelCoin.setCoin(coinMap.get(key));
                    dramaRoomVideoChannelCoin.setUpdateBy(SecurityUtils.getUserId().toString());
                    dramaRoomVideoChannelCoin.setUpdateTime(DateUtils.getNowDate());
                }
            }
            return dramaRoomVideoChannelCoinMapper.updateDramaRoomVideoChannelCoinBatch(dramaRoomVideoChannelCoinList);
        }
    }

    @Override
    public Map<String, Object> getDramaRoomMovieCoinRule(Long movieId) {
        Map<String, Object> result = new HashMap<>();
        result.put("movieId", movieId);
        // 获取电影硬币规则
        DramaRoomMovieCoinRule dramaRoomMovieCoinRule = dramaRoomMovieCoinRuleMapper.selectDramaRoomMovieCoinRuleByMoiveId(movieId);
        if (dramaRoomMovieCoinRule != null) {
            result.put("name", dramaRoomMovieCoinRule.getName());
            result.put("id",dramaRoomMovieCoinRule.getId());
        } else {
            // 如果规则不存在，可以设置默认值或空值
            result.put("name", null); // 或者设置为空字符串 "" 或默认名称
        }
        List<Map<String, Object>> videoChannelCoinList = new ArrayList<Map<String, Object>>();
        //筛选分组
        Map<Integer, List<DramaRoomVideoChannelCoin>> groupMap = new HashMap<>();
        // 获取视频频道硬币列表
        List<DramaRoomVideoChannelCoin> dramaRoomVideoChannelCoins = dramaRoomVideoChannelCoinMapper.selectDramaRoomVideoChannelCoinByMovieId(movieId);
        for (DramaRoomVideoChannelCoin videoChannelCoin : dramaRoomVideoChannelCoins) {
            groupMap
                    .computeIfAbsent(videoChannelCoin.getCoin(), k -> new ArrayList<>())
                    .add(videoChannelCoin);
        }
        groupMap.keySet().stream()
                .sorted(Comparator.naturalOrder()) // 正序
                .forEach(key -> {
                    List<DramaRoomVideoChannelCoin> values = groupMap.get(key);
                    Map<String, Object> videoChannelCoinItem = new HashMap<>();
                    videoChannelCoinItem.put("coin", key);
                    videoChannelCoinItem.put("startNum", values.get(0).getNum());
                    videoChannelCoinItem.put("endNum", values.get(values.size() - 1).getNum());
                    videoChannelCoinList.add(videoChannelCoinItem);
                });
        result.put("videoChannelCoinList", videoChannelCoinList);
        return result;
    }

}
