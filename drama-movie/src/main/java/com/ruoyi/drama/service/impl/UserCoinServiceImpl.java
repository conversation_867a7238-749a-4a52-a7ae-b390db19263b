package com.ruoyi.drama.service.impl;

import com.ruoyi.common.drama.dto.UserCoinDTO;
import com.ruoyi.common.drama.vo.SignInRuleVO;
import com.ruoyi.common.drama.vo.UserCoinLogVO;
import com.ruoyi.common.drama.vo.UserCoinVO;
import com.ruoyi.common.utils.mobile.AppSecurityUtils;
import com.ruoyi.drama.domain.*;
import com.ruoyi.drama.service.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

/**
 * 充值金币相关接口Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-29
 */
@Service
public class UserCoinServiceImpl implements IUserCoinService
{

    @Autowired
    private IDramaRoomCoinRuleService dramaRoomCoinRuleService;

    @Autowired
    private IAppUserCoinService appUserCoinService;

    @Autowired
    private IAppUserCoinLogService appUserCoinLogService;

    @Autowired
    private IDramaRoomSignInRuleInfoService dramaRoomSignInRuleInfoService;

    @Autowired
    private IDramaRoomSignInRuleService signInRuleService;



    @Override
    @Transactional(rollbackFor = Exception.class)
    public int recharge(UserCoinDTO userCoinDTO) {
        int result = 0;
        DramaRoomCoinRule dramaRoomCoinRule = dramaRoomCoinRuleService.selectDramaRoomCoinRuleById(userCoinDTO.getCoinRuleId());
        if (dramaRoomCoinRule != null) {

            Long appUserId = AppSecurityUtils.getAppUserId();
            UserCoinVO userCoinVO = appUserCoinService.getUserCoinByUserId(appUserId);
            AppUserCoin appUserCoin = new AppUserCoin();
            if(userCoinVO != null) {
                appUserCoin.setId(userCoinVO.getId());
                if(dramaRoomCoinRule.getCoins() != null)
                    appUserCoin.setCoins(Optional.ofNullable(userCoinVO.getCoins()).orElse(0L) + Optional.of(dramaRoomCoinRule.getCoins()).orElse(0L));
                if(dramaRoomCoinRule.getRewardCoins() != null)
                    appUserCoin.setRewardCoins(Optional.ofNullable(userCoinVO.getRewardCoins()).orElse(0L) + Optional.of(dramaRoomCoinRule.getRewardCoins()).orElse(0L));
                result = appUserCoinService.updateAppUserCoin(appUserCoin);

                if(dramaRoomCoinRule.getCoins() != null){
                    AppUserCoinLog appUserCoinLogRecharge = new AppUserCoinLog();
                    appUserCoinLogRecharge.setType(0L);
                    appUserCoinLogRecharge.setTradeType(0);
                    appUserCoinLogRecharge.setIncomeType(0);
                    appUserCoinLogRecharge.setUserId(appUserId);
                    appUserCoinLogRecharge.setCoins(dramaRoomCoinRule.getCoins());
                    appUserCoinLogRecharge.setBeforeCoins(userCoinVO.getCoins());
                    appUserCoinLogRecharge.setAfterCoins(Optional.ofNullable(userCoinVO.getCoins()).orElse(0L) + Optional.of(dramaRoomCoinRule.getCoins()).orElse(0L));
                    appUserCoinLogRecharge.setSourceId(dramaRoomCoinRule.getId());
                    result = appUserCoinLogService.insertAppUserCoinLog(appUserCoinLogRecharge);
                }

                if(dramaRoomCoinRule.getRewardCoins() != null) {
                    AppUserCoinLog appUserCoinLogReward = new AppUserCoinLog();
                    appUserCoinLogReward.setType(1L);
                    appUserCoinLogReward.setTradeType(0);
                    appUserCoinLogReward.setIncomeType(0);
                    appUserCoinLogReward.setUserId(appUserId);
                    appUserCoinLogReward.setCoins(dramaRoomCoinRule.getRewardCoins());
                    appUserCoinLogReward.setBeforeCoins(userCoinVO.getRewardCoins());
                    appUserCoinLogReward.setAfterCoins(Optional.ofNullable(userCoinVO.getRewardCoins()).orElse(0L) + Optional.of(dramaRoomCoinRule.getRewardCoins()).orElse(0L));
                    appUserCoinLogReward.setSourceId(dramaRoomCoinRule.getId());
                    result = appUserCoinLogService.insertAppUserCoinLog(appUserCoinLogReward);
                }
            }else{
                appUserCoin.setUserId(appUserId);
                appUserCoin.setCoins(dramaRoomCoinRule.getCoins());
                appUserCoin.setRewardCoins(dramaRoomCoinRule.getRewardCoins());
                result = appUserCoinService.insertAppUserCoin(appUserCoin);

                if(dramaRoomCoinRule.getCoins() != null){
                    AppUserCoinLog appUserCoinLogRecharge = new AppUserCoinLog();
                    appUserCoinLogRecharge.setType(0L);
                    appUserCoinLogRecharge.setTradeType(0);
                    appUserCoinLogRecharge.setIncomeType(0);
                    appUserCoinLogRecharge.setUserId(appUserId);
                    appUserCoinLogRecharge.setCoins(dramaRoomCoinRule.getCoins());
                    appUserCoinLogRecharge.setBeforeCoins(0L);
                    appUserCoinLogRecharge.setAfterCoins(dramaRoomCoinRule.getCoins());
                    appUserCoinLogRecharge.setSourceId(dramaRoomCoinRule.getId());
                    result = appUserCoinLogService.insertAppUserCoinLog(appUserCoinLogRecharge);
                }

                if(dramaRoomCoinRule.getRewardCoins() != null) {
                    AppUserCoinLog appUserCoinLogReward = new AppUserCoinLog();
                    appUserCoinLogReward.setType(1L);
                    appUserCoinLogReward.setTradeType(0);
                    appUserCoinLogReward.setIncomeType(0);
                    appUserCoinLogReward.setUserId(appUserId);
                    appUserCoinLogReward.setCoins(dramaRoomCoinRule.getRewardCoins());
                    appUserCoinLogReward.setBeforeCoins(0L);
                    appUserCoinLogReward.setAfterCoins(dramaRoomCoinRule.getRewardCoins());
                    appUserCoinLogReward.setSourceId(dramaRoomCoinRule.getId());
                    result = appUserCoinLogService.insertAppUserCoinLog(appUserCoinLogReward);
                }
            }

        }
        return result;
    }

    @Override
    public List<UserCoinLogVO> getSignInByUserId() {
        List<UserCoinLogVO> userCoinLogVOList =appUserCoinLogService.getSignInByUserId();
        if(userCoinLogVOList != null && !userCoinLogVOList.isEmpty()){
            UserCoinVO userCoinVO = appUserCoinService.getUserCoinByUserId(null);
            if(null != userCoinVO)
                userCoinLogVOList.get(0).setCoinsBalance(userCoinVO.getCoins());
        }
        return userCoinLogVOList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int signIn(UserCoinDTO userCoinDTO){
        int result = 0;
        DramaRoomSignInRuleInfo dramaRoomSignInRuleInfo = dramaRoomSignInRuleInfoService.selectDramaRoomSignInRuleInfoById(userCoinDTO.getSignInRuleInfoId());
        if (dramaRoomSignInRuleInfo != null) {

            Long appUserId = null;
            if(null == userCoinDTO.getUserId())
                appUserId = AppSecurityUtils.getAppUserId();
            else
                appUserId = userCoinDTO.getUserId();
            UserCoinVO userCoinVO = appUserCoinService.getUserCoinByUserId(appUserId);
            AppUserCoin appUserCoin = new AppUserCoin();
            if(userCoinVO != null) {
                //验证是否签到
                DramaRoomSignInRule signInRule = signInRuleService.selectDramaRoomSignInRuleById(dramaRoomSignInRuleInfo.getSignInRuleId());
                int count = appUserCoinLogService.selectByTypeAndUserIdAndSourceId(signInRule.getType(),userCoinVO.getUserId(),dramaRoomSignInRuleInfo.getId());
                if(count >0)
                    return 0;

                appUserCoin.setId(userCoinVO.getId());
                if(dramaRoomSignInRuleInfo.getNum() != null)
                    appUserCoin.setRewardCoins(Optional.ofNullable(userCoinVO.getRewardCoins()).orElse(0L) + Optional.of(dramaRoomSignInRuleInfo.getNum()).orElse(0L));
                result = appUserCoinService.updateAppUserCoin(appUserCoin);

                if(dramaRoomSignInRuleInfo.getNum() != null) {
                    AppUserCoinLog appUserCoinLogReward = new AppUserCoinLog();
                    appUserCoinLogReward.setType(2L);
                    appUserCoinLogReward.setTradeType(0);
                    appUserCoinLogReward.setIncomeType(1);
                    appUserCoinLogReward.setUserId(appUserId);
                    appUserCoinLogReward.setCoins(dramaRoomSignInRuleInfo.getNum());
                    appUserCoinLogReward.setBeforeCoins(userCoinVO.getRewardCoins());
                    appUserCoinLogReward.setAfterCoins(Optional.ofNullable(userCoinVO.getRewardCoins()).orElse(0L) + Optional.of(dramaRoomSignInRuleInfo.getNum()).orElse(0L));
                    appUserCoinLogReward.setSourceId(dramaRoomSignInRuleInfo.getId());
                    result = appUserCoinLogService.insertAppUserCoinLog(appUserCoinLogReward);
                }
            }else{
                appUserCoin.setUserId(appUserId);
                appUserCoin.setCoins(0L);
                appUserCoin.setRewardCoins(dramaRoomSignInRuleInfo.getNum());
                result = appUserCoinService.insertAppUserCoin(appUserCoin);

                if(dramaRoomSignInRuleInfo.getNum() != null){
                    AppUserCoinLog appUserCoinLogReward = new AppUserCoinLog();
                    appUserCoinLogReward.setType(2L);
                    appUserCoinLogReward.setTradeType(0);
                    appUserCoinLogReward.setIncomeType(1);
                    appUserCoinLogReward.setUserId(appUserId);
                    appUserCoinLogReward.setCoins(dramaRoomSignInRuleInfo.getNum());
                    appUserCoinLogReward.setBeforeCoins(0L);
                    appUserCoinLogReward.setAfterCoins(dramaRoomSignInRuleInfo.getNum());
                    appUserCoinLogReward.setSourceId(dramaRoomSignInRuleInfo.getId());
                    result = appUserCoinLogService.insertAppUserCoinLog(appUserCoinLogReward);
                }
            }

        }


        return result;
    }

    @Override
    public List<SignInRuleVO> getUserBenefits(Integer type) {
        return dramaRoomSignInRuleInfoService.getUserBenefits(type);
    }

}
