package com.ruoyi.drama.service.impl;

import com.ruoyi.common.core.domain.entity.AppUser;
import com.ruoyi.common.drama.vo.DramaRoomPayTemplateAmtVO;
import com.ruoyi.common.drama.vo.DramaRoomPayTemplateSubscriptionVO;
import com.ruoyi.common.drama.vo.IapGoodsVO;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.mobile.AppSecurityUtils;
import com.ruoyi.drama.domain.DramaRoomSemLink;
import com.ruoyi.drama.mapper.IapMapper;
import com.ruoyi.drama.service.IAppUserService;
import com.ruoyi.drama.service.IDramaRoomSemLinkService;
import com.ruoyi.drama.service.IapService;
import org.apache.poi.util.StringUtil;
import org.checkerframework.checker.units.qual.A;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class IapServiceImpl implements IapService {

    @Autowired
    private IAppUserService appUserService;

    @Autowired
    private IDramaRoomSemLinkService semLinkService;

    @Autowired
    private IapMapper iapMapper;

    @Override
    public IapGoodsVO getIapGoods() {
        //1.获取当前用户
        Long appUserId = AppSecurityUtils.getAppUserId();
        AppUser appUser = appUserService.selectAppUserByUserId(appUserId);
        if (appUser == null) {
            return null;
        }
        Long semLinkId = appUser.getSemLinkId();
        Long payTemplateId;
        if (semLinkId == null) {
            // 使用默认的付费模板  template
            payTemplateId = 7L;
        }else {
            DramaRoomSemLink dramaRoomSemLink = semLinkService.selectDramaRoomSemLinkById(semLinkId);
            if (dramaRoomSemLink == null){
                payTemplateId = 7L;
            }else {
                payTemplateId = dramaRoomSemLink.getPayTemplateId();
            }
        }
        //2.获取用户的付费模板
        List<DramaRoomPayTemplateAmtVO> dramaRoomPayTemplateAmtVOS = iapMapper.selectAmtByPayTemplateId(payTemplateId);
        List<DramaRoomPayTemplateSubscriptionVO> dramaRoomPayTemplateSubscriptionVOS = iapMapper.selectSubscriptionByPayTemplateId(payTemplateId);
        IapGoodsVO iapGoodsVO = new IapGoodsVO();
        iapGoodsVO.setCoinList(dramaRoomPayTemplateAmtVOS);
        iapGoodsVO.setSubscriptionList(dramaRoomPayTemplateSubscriptionVOS);
        return iapGoodsVO;
    }
}
