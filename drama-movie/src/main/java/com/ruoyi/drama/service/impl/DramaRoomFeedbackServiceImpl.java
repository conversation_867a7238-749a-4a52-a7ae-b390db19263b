package com.ruoyi.drama.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.drama.domain.DramaRoomFeedback;
import com.ruoyi.drama.mapper.DramaRoomFeedbackMapper;
import com.ruoyi.drama.service.IDramaRoomFeedbackService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 用户反馈Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-28
 */
@Service
public class DramaRoomFeedbackServiceImpl implements IDramaRoomFeedbackService
{
    @Autowired
    private DramaRoomFeedbackMapper dramaRoomFeedbackMapper;

    /**
     * 查询用户反馈
     *
     * @param id 用户反馈主键
     * @return 用户反馈
     */
    @Override
    public DramaRoomFeedback selectDramaRoomFeedbackById(Long id)
    {
        return dramaRoomFeedbackMapper.selectDramaRoomFeedbackById(id);
    }

    /**
     * 查询用户反馈列表
     *
     * @param dramaRoomFeedback 用户反馈
     * @return 用户反馈
     */
    @Override
    public List<DramaRoomFeedback> selectDramaRoomFeedbackList(DramaRoomFeedback dramaRoomFeedback)
    {
        return dramaRoomFeedbackMapper.selectDramaRoomFeedbackList(dramaRoomFeedback);
    }

    /**
     * 新增用户反馈
     *
     * @param dramaRoomFeedback 用户反馈
     * @return 结果
     */
    @Override
    public int insertDramaRoomFeedback(DramaRoomFeedback dramaRoomFeedback)
    {
        dramaRoomFeedback.setCreateTime(DateUtils.getNowDate());
        return dramaRoomFeedbackMapper.insertDramaRoomFeedback(dramaRoomFeedback);
    }

    /**
     * 修改用户反馈
     *
     * @param dramaRoomFeedback 用户反馈
     * @return 结果
     */
    @Override
    public int updateDramaRoomFeedback(DramaRoomFeedback dramaRoomFeedback)
    {
        dramaRoomFeedback.setUpdateTime(DateUtils.getNowDate());
        return dramaRoomFeedbackMapper.updateDramaRoomFeedback(dramaRoomFeedback);
    }

    /**
     * 批量删除用户反馈
     *
     * @param ids 需要删除的用户反馈主键
     * @return 结果
     */
    @Override
    public int deleteDramaRoomFeedbackByIds(Long[] ids)
    {
        return dramaRoomFeedbackMapper.deleteDramaRoomFeedbackByIds(ids);
    }

    /**
     * 删除用户反馈信息
     *
     * @param id 用户反馈主键
     * @return 结果
     */
    @Override
    public int deleteDramaRoomFeedbackById(Long id)
    {
        return dramaRoomFeedbackMapper.deleteDramaRoomFeedbackById(id);
    }
}
