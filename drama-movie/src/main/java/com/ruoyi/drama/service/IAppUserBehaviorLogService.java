package com.ruoyi.drama.service;

import com.ruoyi.common.drama.dto.UserBehaviorLogDTO;
import com.ruoyi.drama.domain.AppUserBehaviorLog;

import java.util.List;

/**
 * 用户行为记录Service接口
 */
public interface IAppUserBehaviorLogService {

    /**
     * 上报用户行为（包含用户ID提取与行为类型校验）
     */
    int reportBehavior(UserBehaviorLogDTO dto);

    AppUserBehaviorLog selectAppUserBehaviorLogById(Long id);

    List<AppUserBehaviorLog> selectAppUserBehaviorLogList(AppUserBehaviorLog appUserBehaviorLog);

    int insertAppUserBehaviorLog(AppUserBehaviorLog appUserBehaviorLog);

    int updateAppUserBehaviorLog(AppUserBehaviorLog appUserBehaviorLog);

    int deleteAppUserBehaviorLogByIds(Long[] ids);

    int deleteAppUserBehaviorLogById(Long id);
}


