package com.ruoyi.drama.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.drama.domain.DramaRoomOrder;
import com.ruoyi.drama.mapper.DramaRoomOrderMapper;
import com.ruoyi.drama.service.IDramaRoomOrderService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 订单Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-08-08
 */
@Service
public class DramaRoomOrderServiceImpl implements IDramaRoomOrderService
{
    @Autowired
    private DramaRoomOrderMapper dramaRoomOrderMapper;

    /**
     * 查询订单
     * 
     * @param id 订单主键
     * @return 订单
     */
    @Override
    public DramaRoomOrder selectDramaRoomOrderById(Long id)
    {
        return dramaRoomOrderMapper.selectDramaRoomOrderById(id);
    }

    /**
     * 查询订单列表
     * 
     * @param dramaRoomOrder 订单
     * @return 订单
     */
    @Override
    public List<DramaRoomOrder> selectDramaRoomOrderList(DramaRoomOrder dramaRoomOrder)
    {
        return dramaRoomOrderMapper.selectDramaRoomOrderList(dramaRoomOrder);
    }

    /**
     * 新增订单
     * 
     * @param dramaRoomOrder 订单
     * @return 结果
     */
    @Override
    public int insertDramaRoomOrder(DramaRoomOrder dramaRoomOrder)
    {
        dramaRoomOrder.setCreateTime(DateUtils.getNowDate());
        return dramaRoomOrderMapper.insertDramaRoomOrder(dramaRoomOrder);
    }

    /**
     * 修改订单
     * 
     * @param dramaRoomOrder 订单
     * @return 结果
     */
    @Override
    public int updateDramaRoomOrder(DramaRoomOrder dramaRoomOrder)
    {
        dramaRoomOrder.setUpdateTime(DateUtils.getNowDate());
        return dramaRoomOrderMapper.updateDramaRoomOrder(dramaRoomOrder);
    }

    /**
     * 批量删除订单
     * 
     * @param ids 需要删除的订单主键
     * @return 结果
     */
    @Override
    public int deleteDramaRoomOrderByIds(Long[] ids)
    {
        return dramaRoomOrderMapper.deleteDramaRoomOrderByIds(ids);
    }

    /**
     * 删除订单信息
     * 
     * @param id 订单主键
     * @return 结果
     */
    @Override
    public int deleteDramaRoomOrderById(Long id)
    {
        return dramaRoomOrderMapper.deleteDramaRoomOrderById(id);
    }
}