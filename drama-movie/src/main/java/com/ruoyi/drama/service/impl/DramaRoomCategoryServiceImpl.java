package com.ruoyi.drama.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.Logger;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.drama.domain.DramaRoomCategory;
import com.ruoyi.drama.domain.DramaRoomCategoryI18n;
import com.ruoyi.drama.manager.MovieAsyncManager;
import com.ruoyi.drama.manager.factory.I18nAsyncFactory;
import com.ruoyi.drama.mapper.DramaRoomCategoryI18nMapper;
import com.ruoyi.drama.mapper.DramaRoomCategoryMapper;
import com.ruoyi.drama.service.IDramaRoomCategoryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * 分类Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-03
 */
@Service
public class DramaRoomCategoryServiceImpl implements IDramaRoomCategoryService
{
    @Autowired
    private DramaRoomCategoryMapper dramaRoomCategoryMapper;

    @Autowired
    private DramaRoomCategoryI18nMapper dramaRoomCategoryI18nMapper;

    /**
     * 查询分类
     *
     * @param id 分类主键
     * @return 分类
     */
    @Override
    public DramaRoomCategory selectDramaRoomCategoryById(Long id)
    {
        Logger.info("开始查询分类，ID：{}", id);
        DramaRoomCategory category = dramaRoomCategoryMapper.selectDramaRoomCategoryById(id);
        Logger.info("查询分类完成，ID：{}，结果：{}", id, category != null ? "成功" : "未找到");
        return category;
    }

    /**
     * 查询分类列表
     *
     * @param dramaRoomCategory 分类
     * @return 分类
     */
    @Override
    public List<DramaRoomCategory> selectDramaRoomCategoryList(DramaRoomCategory dramaRoomCategory) {
        List<DramaRoomCategory> dramaRoomCategories = dramaRoomCategoryMapper.selectDramaRoomCategoryList(dramaRoomCategory);
        if(CollectionUtil.isEmpty(dramaRoomCategories)){
            return dramaRoomCategories;
        }
        List<Long> categoryIds = dramaRoomCategories.stream().map(DramaRoomCategory::getId).collect(Collectors.toList());
        List<DramaRoomCategoryI18n> i18ns = dramaRoomCategoryI18nMapper.selectDramaRoomCategoryI18nListByCategoryIds(categoryIds);
        Map<Long, List<DramaRoomCategoryI18n>> i18nMap = i18ns.stream().collect(Collectors.groupingBy(DramaRoomCategoryI18n::getCategoryId));
        dramaRoomCategories.forEach(category -> {
            if (i18nMap.containsKey(category.getId())) {
                category.setI18nList(i18nMap.get(category.getId()));
            }
        });
        return dramaRoomCategories;
    }

    /**
     * 新增分类
     *
     * @param dramaRoomCategory 分类
     * @return 结果
     */
    @Override
    public int insertDramaRoomCategory(DramaRoomCategory dramaRoomCategory) {
        dramaRoomCategory.setCreateTime(DateUtils.getNowDate());
        dramaRoomCategory.setCreateBy(String.valueOf(SecurityUtils.getUserId()));
        int i = dramaRoomCategoryMapper.insertDramaRoomCategory(dramaRoomCategory);
        if (i > 0) {
            MovieAsyncManager.me().execute(I18nAsyncFactory.getCategoryI18n(dramaRoomCategory.getId(), dramaRoomCategory.getCategoryName(),String.valueOf(SecurityUtils.getUserId())));
        }
        return i;
    }

    /**
     * 修改分类
     *
     * @param dramaRoomCategory 分类
     * @return 结果
     */
    @Override
    public int updateDramaRoomCategory(DramaRoomCategory dramaRoomCategory) {
        dramaRoomCategory.setUpdateTime(DateUtils.getNowDate());
        return dramaRoomCategoryMapper.updateDramaRoomCategory(dramaRoomCategory);
    }

    /**
     * 批量删除分类
     *
     * @param ids 需要删除的分类主键
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteDramaRoomCategoryByIds(Long[] ids) {
        dramaRoomCategoryI18nMapper.deleteDramaRoomCategoryI18nByCategoryIds(ids);
        return dramaRoomCategoryMapper.deleteDramaRoomCategoryByIds(ids);
    }

    /**
     * 删除分类信息
     *
     * @param id 分类主键
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteDramaRoomCategoryById(Long id) {
        dramaRoomCategoryI18nMapper.deleteDramaRoomCategoryI18nByCategoryId(id);
        return dramaRoomCategoryMapper.deleteDramaRoomCategoryById(id);
    }
}
