package com.ruoyi.drama.service;

import com.ruoyi.common.drama.vo.CoinRuleVO;
import com.ruoyi.common.drama.vo.UserCoinVO;
import com.ruoyi.drama.domain.DramaRoomCoinRule;

import java.util.List;

/**
 * 充值金币规则Service接口
 *
 * <AUTHOR>
 * @date 2025-07-29
 */
public interface IDramaRoomCoinRuleService
{
    /**
     * 查询充值金币规则
     *
     * @param id 充值金币规则主键
     * @return 充值金币规则
     */
    public DramaRoomCoinRule selectDramaRoomCoinRuleById(Long id);

    /**
     * 查询充值金币规则列表
     *
     * @param dramaRoomCoinRule 充值金币规则
     * @return 充值金币规则集合
     */
    public List<DramaRoomCoinRule> selectDramaRoomCoinRuleList(DramaRoomCoinRule dramaRoomCoinRule);

    /**
     * 新增充值金币规则
     *
     * @param dramaRoomCoinRule 充值金币规则
     * @return 结果
     */
    public int insertDramaRoomCoinRule(DramaRoomCoinRule dramaRoomCoinRule);

    /**
     * 修改充值金币规则
     *
     * @param dramaRoomCoinRule 充值金币规则
     * @return 结果
     */
    public int updateDramaRoomCoinRule(DramaRoomCoinRule dramaRoomCoinRule);

    /**
     * 批量删除充值金币规则
     *
     * @param ids 需要删除的充值金币规则主键集合
     * @return 结果
     */
    public int deleteDramaRoomCoinRuleByIds(Long[] ids);

    /**
     * 删除充值金币规则信息
     *
     * @param id 充值金币规则主键
     * @return 结果
     */
    public int deleteDramaRoomCoinRuleById(Long id);

    List<CoinRuleVO> getCoinRuleList(Integer type);

}
