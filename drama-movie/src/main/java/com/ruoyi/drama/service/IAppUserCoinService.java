package com.ruoyi.drama.service;

import com.ruoyi.common.drama.vo.UserCoinVO;
import com.ruoyi.drama.domain.AppUserCoin;

import java.util.List;

/**
 * 用户金币Service接口
 *
 * <AUTHOR>
 * @date 2025-07-29
 */
public interface IAppUserCoinService
{
    /**
     * 查询用户金币
     *
     * @param id 用户金币主键
     * @return 用户金币
     */
    public AppUserCoin selectAppUserCoinById(Long id);

    /**
     * 查询用户金币列表
     *
     * @param appUserCoin 用户金币
     * @return 用户金币集合
     */
    public List<AppUserCoin> selectAppUserCoinList(AppUserCoin appUserCoin);

    /**
     * 新增用户金币
     *
     * @param appUserCoin 用户金币
     * @return 结果
     */
    public int insertAppUserCoin(AppUserCoin appUserCoin);

    /**
     * 修改用户金币
     *
     * @param appUserCoin 用户金币
     * @return 结果
     */
    public int updateAppUserCoin(AppUserCoin appUserCoin);

    /**
     * 批量删除用户金币
     *
     * @param ids 需要删除的用户金币主键集合
     * @return 结果
     */
    public int deleteAppUserCoinByIds(Long[] ids);

    /**
     * 删除用户金币信息
     *
     * @param id 用户金币主键
     * @return 结果
     */
    public int deleteAppUserCoinById(Long id);

    UserCoinVO getUserCoinByUserId(Long appUserId);
}
