package com.ruoyi.drama.service.impl;

import com.ruoyi.common.drama.dto.UserBehaviorLogDTO;
import com.ruoyi.common.drama.dto.UserCoinDTO;
import com.ruoyi.common.enums.AppUserActionEnum;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.mobile.AppSecurityUtils;
import com.ruoyi.drama.domain.AppUserBehaviorLog;
import com.ruoyi.drama.domain.DramaRoomSignInRuleInfo;
import com.ruoyi.drama.mapper.AppUserBehaviorLogMapper;
import com.ruoyi.drama.service.IAppUserBehaviorLogService;
import com.ruoyi.drama.service.IDramaRoomSignInRuleInfoService;
import com.ruoyi.drama.service.IUserCoinService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;

@Service
public class AppUserBehaviorLogServiceImpl implements IAppUserBehaviorLogService {

    @Autowired
    private AppUserBehaviorLogMapper appUserBehaviorLogMapper;

    @Autowired
    private IDramaRoomSignInRuleInfoService signInRuleInfoService;

    @Autowired
    private IUserCoinService iUserCoinService;

    @Override
    public int reportBehavior(UserBehaviorLogDTO dto) {
        // 校验行为类型是否为枚举值
        boolean valid = Arrays.stream(AppUserActionEnum.values())
                .anyMatch(e -> e.name().equalsIgnoreCase(dto.getBehaviorType()));
        if (!valid) {
            throw new ServiceException("非法的行为类型: " + dto.getBehaviorType());
        }

        Long userId = AppSecurityUtils.getAppUserId();
        AppUserBehaviorLog log = new AppUserBehaviorLog();
        log.setUserId(userId);
        log.setBehaviorType(dto.getBehaviorType().toUpperCase());
        log.setStayTime(dto.getStayTime());
        log.setPageUrl(dto.getPageUrl());
        log.setDeviceType(dto.getDeviceType());
        log.setDeviceOs(dto.getDeviceOs());
        log.setDeviceBrand(dto.getDeviceBrand());
        log.setActionDetail(dto.getActionDetail());
        log.setBehaviorDesc(dto.getBehaviorDesc());
        log.setBehaviorTime(DateUtils.getNowDate());
        log.setCreateTime(DateUtils.getNowDate());
        int res = appUserBehaviorLogMapper.insertAppUserBehaviorLog(log);

        if(dto.getBehaviorType().toUpperCase().equalsIgnoreCase(AppUserActionEnum.MOVIE_WATCH_TIME.name())){
            new Thread(() -> {
                int sumMinutes = appUserBehaviorLogMapper.getSumMinutes(userId,AppUserActionEnum.MOVIE_WATCH_TIME.name());
                List<DramaRoomSignInRuleInfo> list = signInRuleInfoService.getByRuleType(3);
                for (DramaRoomSignInRuleInfo signInRuleInfo :list){
                    if(sumMinutes >= Math.toIntExact(signInRuleInfo.getDays())){
                        //签到
                        UserCoinDTO userCoinDTO = new UserCoinDTO();
                        userCoinDTO.setUserId(userId);
                        userCoinDTO.setSignInRuleInfoId(signInRuleInfo.getId());
                        iUserCoinService.signIn(userCoinDTO);
                    }
                }
            }).start();
        }
        return res;
    }

    @Override
    public AppUserBehaviorLog selectAppUserBehaviorLogById(Long id) {
        return appUserBehaviorLogMapper.selectAppUserBehaviorLogById(id);
    }

    @Override
    public List<AppUserBehaviorLog> selectAppUserBehaviorLogList(AppUserBehaviorLog appUserBehaviorLog) {
        return appUserBehaviorLogMapper.selectAppUserBehaviorLogList(appUserBehaviorLog);
    }

    @Override
    public int insertAppUserBehaviorLog(AppUserBehaviorLog appUserBehaviorLog) {
        appUserBehaviorLog.setCreateTime(DateUtils.getNowDate());
        return appUserBehaviorLogMapper.insertAppUserBehaviorLog(appUserBehaviorLog);
    }

    @Override
    public int updateAppUserBehaviorLog(AppUserBehaviorLog appUserBehaviorLog) {
        appUserBehaviorLog.setUpdateTime(DateUtils.getNowDate());
        return appUserBehaviorLogMapper.updateAppUserBehaviorLog(appUserBehaviorLog);
    }

    @Override
    public int deleteAppUserBehaviorLogByIds(Long[] ids) {
        return appUserBehaviorLogMapper.deleteAppUserBehaviorLogByIds(ids);
    }

    @Override
    public int deleteAppUserBehaviorLogById(Long id) {
        return appUserBehaviorLogMapper.deleteAppUserBehaviorLogById(id);
    }
}


