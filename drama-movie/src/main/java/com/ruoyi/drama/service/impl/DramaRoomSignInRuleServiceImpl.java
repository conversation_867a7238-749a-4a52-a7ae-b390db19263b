package com.ruoyi.drama.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.drama.domain.DramaRoomSignInRule;
import com.ruoyi.drama.mapper.DramaRoomSignInRuleMapper;
import com.ruoyi.drama.service.IDramaRoomSignInRuleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 签到规则Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-29
 */
@Service
public class DramaRoomSignInRuleServiceImpl implements IDramaRoomSignInRuleService
{
    @Autowired
    private DramaRoomSignInRuleMapper dramaRoomSignInRuleMapper;

    /**
     * 查询签到规则
     *
     * @param id 签到规则主键
     * @return 签到规则
     */
    @Override
    public DramaRoomSignInRule selectDramaRoomSignInRuleById(Long id)
    {
        return dramaRoomSignInRuleMapper.selectDramaRoomSignInRuleById(id);
    }

    /**
     * 查询签到规则列表
     *
     * @param dramaRoomSignInRule 签到规则
     * @return 签到规则
     */
    @Override
    public List<DramaRoomSignInRule> selectDramaRoomSignInRuleList(DramaRoomSignInRule dramaRoomSignInRule)
    {
        return dramaRoomSignInRuleMapper.selectDramaRoomSignInRuleList(dramaRoomSignInRule);
    }

    /**
     * 新增签到规则
     *
     * @param dramaRoomSignInRule 签到规则
     * @return 结果
     */
    @Override
    public int insertDramaRoomSignInRule(DramaRoomSignInRule dramaRoomSignInRule)
    {
        dramaRoomSignInRule.setCreateTime(DateUtils.getNowDate());
        return dramaRoomSignInRuleMapper.insertDramaRoomSignInRule(dramaRoomSignInRule);
    }

    /**
     * 修改签到规则
     *
     * @param dramaRoomSignInRule 签到规则
     * @return 结果
     */
    @Override
    public int updateDramaRoomSignInRule(DramaRoomSignInRule dramaRoomSignInRule)
    {
        dramaRoomSignInRule.setUpdateTime(DateUtils.getNowDate());
        return dramaRoomSignInRuleMapper.updateDramaRoomSignInRule(dramaRoomSignInRule);
    }

    /**
     * 批量删除签到规则
     *
     * @param ids 需要删除的签到规则主键
     * @return 结果
     */
    @Override
    public int deleteDramaRoomSignInRuleByIds(Long[] ids)
    {
        return dramaRoomSignInRuleMapper.deleteDramaRoomSignInRuleByIds(ids);
    }

    /**
     * 删除签到规则信息
     *
     * @param id 签到规则主键
     * @return 结果
     */
    @Override
    public int deleteDramaRoomSignInRuleById(Long id)
    {
        return dramaRoomSignInRuleMapper.deleteDramaRoomSignInRuleById(id);
    }
}
