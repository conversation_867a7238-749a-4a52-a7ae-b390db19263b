package com.ruoyi.drama.service.impl;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.drama.domain.DramaRoomMovie;
import com.ruoyi.drama.domain.vo.DramaRoomMovieBindTagVO;
import com.ruoyi.drama.mapper.DramaRoomMovieMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.drama.mapper.DramaRoomMovieTagMapper;
import com.ruoyi.drama.domain.DramaRoomMovieTag;
import com.ruoyi.drama.service.IDramaRoomMovieTagService;
import org.springframework.transaction.annotation.Transactional;

/**
 * 短剧标签关联Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-04
 */
@Service
public class DramaRoomMovieTagServiceImpl implements IDramaRoomMovieTagService
{
    @Autowired
    private DramaRoomMovieTagMapper dramaRoomMovieTagMapper;

    @Autowired
    private DramaRoomMovieMapper dramaRoomMovieMapper;

    /**
     * 查询短剧标签关联
     *
     * @param id 短剧标签关联主键
     * @return 短剧标签关联
     */
    @Override
    public DramaRoomMovieTag selectDramaRoomMovieTagById(Long id)
    {
        return dramaRoomMovieTagMapper.selectDramaRoomMovieTagById(id);
    }

    /**
     * 查询短剧标签关联列表
     *
     * @param DramaRoomMovieTag 短剧标签关联
     * @return 短剧标签关联
     */
    @Override
    public List<DramaRoomMovieTag> selectDramaRoomMovieTagList(DramaRoomMovieTag DramaRoomMovieTag)
    {
        return dramaRoomMovieTagMapper.selectDramaRoomMovieTagList(DramaRoomMovieTag);
    }

    /**
     * 新增短剧标签关联
     *
     * @param DramaRoomMovieTag 短剧标签关联
     * @return 结果
     */
    @Override
    public int insertDramaRoomMovieTag(DramaRoomMovieTag DramaRoomMovieTag)
    {
        DramaRoomMovieTag.setCreateTime(DateUtils.getNowDate());
        return dramaRoomMovieTagMapper.insertDramaRoomMovieTag(DramaRoomMovieTag);
    }

    /**
     * 修改短剧标签关联
     *
     * @param DramaRoomMovieTag 短剧标签关联
     * @return 结果
     */
    @Override
    public int updateDramaRoomMovieTag(DramaRoomMovieTag DramaRoomMovieTag)
    {
        DramaRoomMovieTag.setUpdateTime(DateUtils.getNowDate());
        return dramaRoomMovieTagMapper.updateDramaRoomMovieTag(DramaRoomMovieTag);
    }

    /**
     * 批量删除短剧标签关联
     *
     * @param ids 需要删除的短剧标签关联主键
     * @return 结果
     */
    @Override
    public int deleteDramaRoomMovieTagByIds(Long[] ids)
    {
        return dramaRoomMovieTagMapper.deleteDramaRoomMovieTagByIds(ids);
    }

    /**
     * 删除短剧标签关联信息
     *
     * @param id 短剧标签关联主键
     * @return 结果
     */
    @Override
    public int deleteDramaRoomMovieTagById(Long id)
    {
        return dramaRoomMovieTagMapper.deleteDramaRoomMovieTagById(id);
    }

    @Override
    public List<DramaRoomMovieBindTagVO> selectDramaRoomMovieTagListByCategoryId(Long tagId) {
        // 获取标签绑定的电影列表
        List<DramaRoomMovieTag> movieTagList = dramaRoomMovieTagMapper.selectDramaRoomMovieTagByTagId(tagId);
        List<Long> movieIds = movieTagList.stream()
                .map(DramaRoomMovieTag::getMovieId)
                .collect(Collectors.toList());
        // 查出所有短剧
        List<DramaRoomMovie> allMovie = dramaRoomMovieMapper.selectDramaRoomMovieList(new DramaRoomMovie());

        return DramaRoomMovieBindTagVO.setBindStatus(allMovie, movieIds);
    }

    @Override
    @Transactional
    public void updateBindMovies(Long tagId, List<Long> movieIds) {
        // 删除标签下所有绑定短剧
        dramaRoomMovieTagMapper.deleteDramaRoomMovieTagByTagId(tagId);
        List<DramaRoomMovieTag> list = new ArrayList<>();
        movieIds.forEach(movieId -> {
            DramaRoomMovieTag movieTag = new DramaRoomMovieTag();
            movieTag.setTagId(tagId);
            movieTag.setMovieId(movieId);
            movieTag.setCreateTime(DateUtils.getNowDate());
            movieTag.setCreateBy(String.valueOf(SecurityUtils.getUserId()));
            movieTag.setUpdateBy(String.valueOf(SecurityUtils.getUserId()));
            movieTag.setUpdateTime(DateUtils.getNowDate());
            list.add(movieTag);
        });
        dramaRoomMovieTagMapper.insertDramaRoomMovieTagList(list);
    }
}
