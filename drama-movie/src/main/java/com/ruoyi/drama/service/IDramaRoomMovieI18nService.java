package com.ruoyi.drama.service;

import java.util.List;

import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.drama.dto.DramaRoomMovieI18nDTO;
import com.ruoyi.drama.domain.DramaRoomMovieI18n;

/**
 * 短剧翻译Service接口
 * 
 * <AUTHOR>
 * @date 2025-08-05
 */
public interface IDramaRoomMovieI18nService 
{
    /**
     * 查询短剧翻译
     * 
     * @param id 短剧翻译主键
     * @return 短剧翻译
     */
    public DramaRoomMovieI18n selectDramaRoomMovieI18nById(Long id);

    /**
     * 查询短剧翻译列表
     * 
     * @param dramaRoomMovieI18n 短剧翻译
     * @return 短剧翻译集合
     */
    public List<DramaRoomMovieI18n> selectDramaRoomMovieI18nList(DramaRoomMovieI18n dramaRoomMovieI18n);

    /**
     * 新增短剧翻译
     * 
     * @param dramaRoomMovieI18n 短剧翻译
     * @return 结果
     */
    public int insertDramaRoomMovieI18n(DramaRoomMovieI18n dramaRoomMovieI18n);

    /**
     * 修改短剧翻译
     * 
     * @param dramaRoomMovieI18n 短剧翻译
     * @return 结果
     */
    public int updateDramaRoomMovieI18n(DramaRoomMovieI18n dramaRoomMovieI18n);

    /**
     * 批量删除短剧翻译
     * 
     * @param ids 需要删除的短剧翻译主键集合
     * @return 结果
     */
    public int deleteDramaRoomMovieI18nByIds(Long[] ids);

    /**
     * 删除短剧翻译信息
     * 
     * @param id 短剧翻译主键
     * @return 结果
     */
    public int deleteDramaRoomMovieI18nById(Long id);

    /**
     * 根据movieId获取短剧翻译列表
     * @param movieId
     * @param languageCode
     * @return
     */
    List<DramaRoomMovieI18n> getMovieI18nList(Long movieId, String languageCode);

    /**
     * 审核短句翻译结果
     * @param dramaRoomMovieI18n
     * @return
     */
    int updateDramaRoomMovieI18nState(DramaRoomMovieI18n dramaRoomMovieI18n);

    /**
     * 翻译短剧
     * @param dto
     * @return
     */
    AjaxResult translateMovieI18n(DramaRoomMovieI18nDTO dto);

    int deleteMovieI18n(Long movieId, String languageCode);
}
