package com.ruoyi.drama.service.impl;

import java.io.IOException;
import java.util.List;
import java.util.stream.Collectors;

import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.drama.dto.DramaRoomGeneralI18nDTO;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.Gemini25FlashUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.drama.domain.*;
import com.ruoyi.drama.mapper.DramaRoomCategoryMapper;
import com.ruoyi.drama.mapper.DramaRoomLanguageConfigMapper;
import io.jsonwebtoken.lang.Collections;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.drama.mapper.DramaRoomCategoryI18nMapper;
import com.ruoyi.drama.service.IDramaRoomCategoryI18nService;

/**
 * 分类多语言Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-08-05
 */
@Slf4j
@Service
public class DramaRoomCategoryI18nServiceImpl implements IDramaRoomCategoryI18nService {
    @Autowired
    private DramaRoomCategoryI18nMapper dramaRoomCategoryI18nMapper;

    @Autowired
    private DramaRoomCategoryMapper dramaRoomCategoryMapper;

    @Autowired
    private DramaRoomLanguageConfigMapper dramaRoomLanguageConfigMapper;

    /**
     * 查询分类多语言
     *
     * @param id 分类多语言主键
     * @return 分类多语言
     */
    @Override
    public DramaRoomCategoryI18n selectDramaRoomCategoryI18nById(Long id) {
        return dramaRoomCategoryI18nMapper.selectDramaRoomCategoryI18nById(id);
    }

    /**
     * 查询分类多语言列表
     *
     * @param dramaRoomCategoryI18n 分类多语言
     * @return 分类多语言
     */
    @Override
    public List<DramaRoomCategoryI18n> selectDramaRoomCategoryI18nList(DramaRoomCategoryI18n dramaRoomCategoryI18n) {
        return dramaRoomCategoryI18nMapper.selectDramaRoomCategoryI18nList(dramaRoomCategoryI18n);
    }

    /**
     * 新增分类多语言
     *
     * @param dramaRoomCategoryI18n 分类多语言
     * @return 结果
     */
    @Override
    public int insertDramaRoomCategoryI18n(DramaRoomCategoryI18n dramaRoomCategoryI18n) {
        dramaRoomCategoryI18n.setCreateTime(DateUtils.getNowDate());
        return dramaRoomCategoryI18nMapper.insertDramaRoomCategoryI18n(dramaRoomCategoryI18n);
    }

    /**
     * 修改分类多语言
     *
     * @param dramaRoomCategoryI18n 分类多语言
     * @return 结果
     */
    @Override
    public int updateDramaRoomCategoryI18n(DramaRoomCategoryI18n dramaRoomCategoryI18n) {
        dramaRoomCategoryI18n.setUpdateTime(DateUtils.getNowDate());
        return dramaRoomCategoryI18nMapper.updateDramaRoomCategoryI18n(dramaRoomCategoryI18n);
    }

    /**
     * 批量删除分类多语言
     *
     * @param ids 需要删除的分类多语言主键
     * @return 结果
     */
    @Override
    public int deleteDramaRoomCategoryI18nByIds(Long[] ids) {
        return dramaRoomCategoryI18nMapper.deleteDramaRoomCategoryI18nByIds(ids);
    }

    /**
     * 删除分类多语言信息
     *
     * @param id 分类多语言主键
     * @return 结果
     */
    @Override
    public int deleteDramaRoomCategoryI18nById(Long id) {
        return dramaRoomCategoryI18nMapper.deleteDramaRoomCategoryI18nById(id);
    }

    /**
     * 批量插入多语言
     *
     * @param i18ns
     */
    @Override
    public void insertDramaRoomCategoryI18nList(List<DramaRoomCategoryI18n> i18ns) {
        dramaRoomCategoryI18nMapper.insertDramaRoomCategoryI18nList(i18ns);
    }

    @Override
    public AjaxResult translate(DramaRoomGeneralI18nDTO dto) {
        Long id = dto.getId();
        Long loginId = dto.getUserId();
        String language = dto.getLanguage();
        String languageCode = dto.getLanguageCode();
        DramaRoomCategory category = dramaRoomCategoryMapper.selectDramaRoomCategoryById(id);
        if(category == null){
            return AjaxResult.error("分类不存在");
        }

        try {
            String translate = Gemini25FlashUtils.translate(category.getCategoryName(), language);
            if(StringUtils.isEmpty(translate)){
                return AjaxResult.error("翻译结果为空");
            }
            DramaRoomCategoryI18n dramaRoomCategoryI18n = new DramaRoomCategoryI18n();
            dramaRoomCategoryI18n.setCategoryId(id);
            dramaRoomCategoryI18n.setLanguageCode(languageCode);
            dramaRoomCategoryI18n.setCategoryName(translate);
            dramaRoomCategoryI18n.setCreateTime(DateUtils.getNowDate());
            dramaRoomCategoryI18n.setCreateBy(String.valueOf(loginId));
            dramaRoomCategoryI18nMapper.insertDramaRoomCategoryI18n(dramaRoomCategoryI18n);
        } catch (IOException e) {
            log.error("【{}】翻译异常：{}", language, e.getMessage());
            return AjaxResult.error("翻译异常");
        }
        return AjaxResult.success("翻译成功");
    }

    @Override
    public AjaxResult listTranslate(Long id) {
        DramaRoomCategory dramaRoomCategory = dramaRoomCategoryMapper.selectDramaRoomCategoryById(id);
        if (dramaRoomCategory == null) {
            return AjaxResult.error("分类不存在");
        }
        DramaRoomCategoryI18n dramaRoomCategoryI18n = new DramaRoomCategoryI18n();
        dramaRoomCategoryI18n.setCategoryId(id);
        List<DramaRoomCategoryI18n> dramaRoomCategoryI18ns = dramaRoomCategoryI18nMapper.selectDramaRoomCategoryI18nList(dramaRoomCategoryI18n);
        List<DramaRoomLanguageConfig> dramaRoomLanguageConfigs = dramaRoomLanguageConfigMapper.selectDramaRoomLanguageConfigList(new DramaRoomLanguageConfig());
        if (Collections.isEmpty(dramaRoomCategoryI18ns)) {
            return AjaxResult.success(dramaRoomLanguageConfigs);
        }
        if (dramaRoomCategoryI18ns.size() == dramaRoomLanguageConfigs.size()) {
            return AjaxResult.success(dramaRoomCategory.getCategoryName() + "分类已全部翻译，请刷新列表后检查翻译结果！");
        }
        List<String> existingLanguageCodes = dramaRoomCategoryI18ns.stream()
                .map(DramaRoomCategoryI18n::getLanguageCode)
                .collect(Collectors.toList());
        List<DramaRoomLanguageConfig> filteredConfigs = dramaRoomLanguageConfigs.stream()
                .filter(config -> !existingLanguageCodes.contains(config.getCode()))
                .collect(Collectors.toList());
        return AjaxResult.success(filteredConfigs);
    }
}
