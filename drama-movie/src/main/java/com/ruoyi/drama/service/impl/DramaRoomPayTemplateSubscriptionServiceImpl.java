package com.ruoyi.drama.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.drama.mapper.DramaRoomPayTemplateSubscriptionMapper;
import com.ruoyi.drama.domain.DramaRoomPayTemplateSubscription;
import com.ruoyi.drama.service.IDramaRoomPayTemplateSubscriptionService;

/**
 * 订阅模板：定义不同周期和等级下的金币订阅计划Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-09-01
 */
@Service
public class DramaRoomPayTemplateSubscriptionServiceImpl implements IDramaRoomPayTemplateSubscriptionService 
{
    @Autowired
    private DramaRoomPayTemplateSubscriptionMapper dramaRoomPayTemplateSubscriptionMapper;

    /**
     * 查询订阅模板：定义不同周期和等级下的金币订阅计划
     * 
     * @param id 订阅模板：定义不同周期和等级下的金币订阅计划主键
     * @return 订阅模板：定义不同周期和等级下的金币订阅计划
     */
    @Override
    public DramaRoomPayTemplateSubscription selectDramaRoomPayTemplateSubscriptionById(String id)
    {
        return dramaRoomPayTemplateSubscriptionMapper.selectDramaRoomPayTemplateSubscriptionById(id);
    }

    /**
     * 查询订阅模板：定义不同周期和等级下的金币订阅计划列表
     * 
     * @param dramaRoomPayTemplateSubscription 订阅模板：定义不同周期和等级下的金币订阅计划
     * @return 订阅模板：定义不同周期和等级下的金币订阅计划
     */
    @Override
    public List<DramaRoomPayTemplateSubscription> selectDramaRoomPayTemplateSubscriptionList(DramaRoomPayTemplateSubscription dramaRoomPayTemplateSubscription)
    {
        return dramaRoomPayTemplateSubscriptionMapper.selectDramaRoomPayTemplateSubscriptionList(dramaRoomPayTemplateSubscription);
    }

    /**
     * 新增订阅模板：定义不同周期和等级下的金币订阅计划
     * 
     * @param dramaRoomPayTemplateSubscription 订阅模板：定义不同周期和等级下的金币订阅计划
     * @return 结果
     */
    @Override
    public int insertDramaRoomPayTemplateSubscription(DramaRoomPayTemplateSubscription dramaRoomPayTemplateSubscription)
    {
        dramaRoomPayTemplateSubscription.setCreateTime(DateUtils.getNowDate());
        return dramaRoomPayTemplateSubscriptionMapper.insertDramaRoomPayTemplateSubscription(dramaRoomPayTemplateSubscription);
    }

    /**
     * 修改订阅模板：定义不同周期和等级下的金币订阅计划
     * 
     * @param dramaRoomPayTemplateSubscription 订阅模板：定义不同周期和等级下的金币订阅计划
     * @return 结果
     */
    @Override
    public int updateDramaRoomPayTemplateSubscription(DramaRoomPayTemplateSubscription dramaRoomPayTemplateSubscription)
    {
        dramaRoomPayTemplateSubscription.setUpdateTime(DateUtils.getNowDate());
        return dramaRoomPayTemplateSubscriptionMapper.updateDramaRoomPayTemplateSubscription(dramaRoomPayTemplateSubscription);
    }

    /**
     * 批量删除订阅模板：定义不同周期和等级下的金币订阅计划
     * 
     * @param ids 需要删除的订阅模板：定义不同周期和等级下的金币订阅计划主键
     * @return 结果
     */
    @Override
    public int deleteDramaRoomPayTemplateSubscriptionByIds(String[] ids)
    {
        return dramaRoomPayTemplateSubscriptionMapper.deleteDramaRoomPayTemplateSubscriptionByIds(ids);
    }

    /**
     * 删除订阅模板：定义不同周期和等级下的金币订阅计划信息
     * 
     * @param id 订阅模板：定义不同周期和等级下的金币订阅计划主键
     * @return 结果
     */
    @Override
    public int deleteDramaRoomPayTemplateSubscriptionById(String id)
    {
        return dramaRoomPayTemplateSubscriptionMapper.deleteDramaRoomPayTemplateSubscriptionById(id);
    }
}