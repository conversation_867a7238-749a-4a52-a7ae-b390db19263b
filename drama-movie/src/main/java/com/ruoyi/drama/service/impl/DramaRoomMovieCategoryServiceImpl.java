package com.ruoyi.drama.service.impl;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.drama.domain.DramaRoomMovie;
import com.ruoyi.drama.domain.vo.DramaRoomMovieBindCategoryVO;
import com.ruoyi.drama.mapper.DramaRoomMovieMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.drama.mapper.DramaRoomMovieCategoryMapper;
import com.ruoyi.drama.domain.DramaRoomMovieCategory;
import com.ruoyi.drama.service.IDramaRoomMovieCategoryService;
import org.springframework.transaction.annotation.Transactional;

/**
 * 【请填写功能名称】Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-04
 */
@Service
public class DramaRoomMovieCategoryServiceImpl implements IDramaRoomMovieCategoryService {
    @Autowired
    private DramaRoomMovieCategoryMapper dramaRoomMovieCategoryMapper;

    @Autowired
    private DramaRoomMovieMapper dramaRoomMovieMapper;

    /**
     * 查询【请填写功能名称】
     *
     * @param id 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    @Override
    public DramaRoomMovieCategory selectDramaRoomMovieCategoryById(Long id) {
        return dramaRoomMovieCategoryMapper.selectDramaRoomMovieCategoryById(id);
    }

    /**
     * 查询【请填写功能名称】列表
     *
     * @param dramaRoomMovieCategory 【请填写功能名称】
     * @return 【请填写功能名称】
     */
    @Override
    public List<DramaRoomMovieCategory> selectDramaRoomMovieCategoryList(DramaRoomMovieCategory dramaRoomMovieCategory) {
        return dramaRoomMovieCategoryMapper.selectDramaRoomMovieCategoryList(dramaRoomMovieCategory);
    }

    /**
     * 新增【请填写功能名称】
     *
     * @param dramaRoomMovieCategory 【请填写功能名称】
     * @return 结果
     */
    @Override
    public int insertDramaRoomMovieCategory(DramaRoomMovieCategory dramaRoomMovieCategory) {
        dramaRoomMovieCategory.setCreateTime(DateUtils.getNowDate());
        dramaRoomMovieCategory.setCreateBy(String.valueOf(SecurityUtils.getUserId()));
        return dramaRoomMovieCategoryMapper.insertDramaRoomMovieCategory(dramaRoomMovieCategory);
    }

    /**
     * 修改【请填写功能名称】
     *
     * @param dramaRoomMovieCategory 【请填写功能名称】
     * @return 结果
     */
    @Override
    public int updateDramaRoomMovieCategory(DramaRoomMovieCategory dramaRoomMovieCategory) {
        dramaRoomMovieCategory.setUpdateTime(DateUtils.getNowDate());
        dramaRoomMovieCategory.setUpdateBy(SecurityUtils.getUsername());
        return dramaRoomMovieCategoryMapper.updateDramaRoomMovieCategory(dramaRoomMovieCategory);
    }

    /**
     * 批量删除【请填写功能名称】
     *
     * @param ids 需要删除的【请填写功能名称】主键
     * @return 结果
     */
    @Override
    public int deleteDramaRoomMovieCategoryByIds(Long[] ids) {
        return dramaRoomMovieCategoryMapper.deleteDramaRoomMovieCategoryByIds(ids);
    }

    /**
     * 删除【请填写功能名称】信息
     *
     * @param id 【请填写功能名称】主键
     * @return 结果
     */
    @Override
    public int deleteDramaRoomMovieCategoryById(Long id) {
        return dramaRoomMovieCategoryMapper.deleteDramaRoomMovieCategoryById(id);
    }

    @Override
    public List<DramaRoomMovieBindCategoryVO> selectDramaRoomMovieCategoryListByCategoryId(Long categoryId) {
        // 获取分类绑定的电影列表
        List<DramaRoomMovieCategory> movieCategoryList = dramaRoomMovieCategoryMapper.selectDramaRoomMovieCategoryByCategoryId(categoryId);
        List<Long> movieIds = movieCategoryList.stream()
                .map(DramaRoomMovieCategory::getMovieId)
                .collect(Collectors.toList());

        // 查出所有的短剧
        List<DramaRoomMovie> allMovie = dramaRoomMovieMapper.selectDramaRoomMovieList(new DramaRoomMovie());

        return DramaRoomMovieBindCategoryVO.setBindStatus(allMovie, movieIds);
    }

    @Override
    @Transactional
    public void updateBindMovies(Long categoryId, List<Long> movieIds) {
        // 删除分类下所有绑定短剧
        dramaRoomMovieCategoryMapper.deleteDramaRoomMovieCategoryByCategoryId(categoryId);
        List<DramaRoomMovieCategory> list = new ArrayList<>();
        movieIds.forEach(movieId -> {
            DramaRoomMovieCategory movieCategory = new DramaRoomMovieCategory();
            movieCategory.setCategoryId(categoryId);
            movieCategory.setMovieId(movieId);
            movieCategory.setCreateTime(DateUtils.getNowDate());
            movieCategory.setCreateBy(String.valueOf(SecurityUtils.getUserId()));
            movieCategory.setUpdateBy(String.valueOf(SecurityUtils.getUserId()));
            movieCategory.setUpdateTime(DateUtils.getNowDate());
            list.add(movieCategory);
        });
        // 批量插入
        dramaRoomMovieCategoryMapper.insertDramaRoomMovieCategoryList(list);
    }
}
