package com.ruoyi.drama.service.impl;

import java.util.List;

import com.ruoyi.common.drama.vo.DramaRoomVideoI18nVO;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.drama.mapper.DramaRoomVideoI18nMapper;
import com.ruoyi.drama.domain.DramaRoomVideoI18n;
import com.ruoyi.drama.service.IDramaRoomVideoI18nService;

/**
 * 剧集多语言Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-08-05
 */
@Service
public class DramaRoomVideoI18nServiceImpl implements IDramaRoomVideoI18nService 
{
    @Autowired
    private DramaRoomVideoI18nMapper dramaRoomVideoI18nMapper;

    /**
     * 查询剧集多语言
     * 
     * @param id 剧集多语言主键
     * @return 剧集多语言
     */
    @Override
    public DramaRoomVideoI18n selectDramaRoomVideoI18nById(Long id)
    {
        return dramaRoomVideoI18nMapper.selectDramaRoomVideoI18nById(id);
    }

    /**
     * 查询剧集多语言列表
     * 
     * @param dramaRoomVideoI18n 剧集多语言
     * @return 剧集多语言
     */
    @Override
    public List<DramaRoomVideoI18n> selectDramaRoomVideoI18nList(DramaRoomVideoI18n dramaRoomVideoI18n)
    {
        return dramaRoomVideoI18nMapper.selectDramaRoomVideoI18nList(dramaRoomVideoI18n);
    }

    /**
     * 新增剧集多语言
     * 
     * @param dramaRoomVideoI18n 剧集多语言
     * @return 结果
     */
    @Override
    public int insertDramaRoomVideoI18n(DramaRoomVideoI18n dramaRoomVideoI18n)
    {
        dramaRoomVideoI18n.setCreateTime(DateUtils.getNowDate());
        return dramaRoomVideoI18nMapper.insertDramaRoomVideoI18n(dramaRoomVideoI18n);
    }

    /**
     * 修改剧集多语言
     * 
     * @param dramaRoomVideoI18n 剧集多语言
     * @return 结果
     */
    @Override
    public int updateDramaRoomVideoI18n(DramaRoomVideoI18n dramaRoomVideoI18n)
    {
        dramaRoomVideoI18n.setUpdateTime(DateUtils.getNowDate());
        return dramaRoomVideoI18nMapper.updateDramaRoomVideoI18n(dramaRoomVideoI18n);
    }

    /**
     * 批量删除剧集多语言
     * 
     * @param ids 需要删除的剧集多语言主键
     * @return 结果
     */
    @Override
    public int deleteDramaRoomVideoI18nByIds(Long[] ids)
    {
        return dramaRoomVideoI18nMapper.deleteDramaRoomVideoI18nByIds(ids);
    }

    /**
     * 删除剧集多语言信息
     * 
     * @param id 剧集多语言主键
     * @return 结果
     */
    @Override
    public int deleteDramaRoomVideoI18nById(Long id)
    {
        return dramaRoomVideoI18nMapper.deleteDramaRoomVideoI18nById(id);
    }

    @Override
    public List<DramaRoomVideoI18n> getVideoI18nList(Long movieId) {
        DramaRoomVideoI18n query = new DramaRoomVideoI18n();
        query.setVideoId(movieId);
        return dramaRoomVideoI18nMapper.selectDramaRoomVideoI18nList(query);
    }
}
