package com.ruoyi.drama.service;

import com.ruoyi.common.drama.vo.SignInRuleVO;
import com.ruoyi.drama.domain.DramaRoomSignInRuleInfo;

import java.util.List;

/**
 * 签到规则明细Service接口
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
public interface IDramaRoomSignInRuleInfoService
{
    /**
     * 查询签到规则明细
     *
     * @param id 签到规则明细主键
     * @return 签到规则明细
     */
    public DramaRoomSignInRuleInfo selectDramaRoomSignInRuleInfoById(Long id);

    /**
     * 查询签到规则明细列表
     *
     * @param dramaRoomSignInRuleInfo 签到规则明细
     * @return 签到规则明细集合
     */
    public List<DramaRoomSignInRuleInfo> selectDramaRoomSignInRuleInfoList(DramaRoomSignInRuleInfo dramaRoomSignInRuleInfo);

    /**
     * 新增签到规则明细
     *
     * @param dramaRoomSignInRuleInfo 签到规则明细
     * @return 结果
     */
    public int insertDramaRoomSignInRuleInfo(DramaRoomSignInRuleInfo dramaRoomSignInRuleInfo);

    /**
     * 修改签到规则明细
     *
     * @param dramaRoomSignInRuleInfo 签到规则明细
     * @return 结果
     */
    public int updateDramaRoomSignInRuleInfo(DramaRoomSignInRuleInfo dramaRoomSignInRuleInfo);

    /**
     * 批量删除签到规则明细
     *
     * @param ids 需要删除的签到规则明细主键集合
     * @return 结果
     */
    public int deleteDramaRoomSignInRuleInfoByIds(Long[] ids);

    /**
     * 删除签到规则明细信息
     *
     * @param id 签到规则明细主键
     * @return 结果
     */
    public int deleteDramaRoomSignInRuleInfoById(Long id);

    List<SignInRuleVO> getUserBenefits(Integer type);

    List<DramaRoomSignInRuleInfo> getByRuleType(Integer type);
}
