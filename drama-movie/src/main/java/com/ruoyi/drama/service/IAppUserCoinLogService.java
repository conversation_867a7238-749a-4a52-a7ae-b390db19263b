package com.ruoyi.drama.service;

import com.ruoyi.common.drama.vo.UserCoinLogTradeVO;
import com.ruoyi.common.drama.vo.UserCoinLogVO;
import com.ruoyi.drama.domain.AppUserCoinLog;

import java.util.List;

/**
 * 金币记录Service接口
 *
 * <AUTHOR>
 * @date 2025-07-29
 */
public interface IAppUserCoinLogService
{
    /**
     * 查询金币记录
     *
     * @param id 金币记录主键
     * @return 金币记录
     */
    public AppUserCoinLog selectAppUserCoinLogById(Long id);

    /**
     * 查询金币记录列表
     *
     * @param appUserCoinLog 金币记录
     * @return 金币记录集合
     */
    public List<AppUserCoinLog> selectAppUserCoinLogList(AppUserCoinLog appUserCoinLog);

    /**
     * 新增金币记录
     *
     * @param appUserCoinLog 金币记录
     * @return 结果
     */
    public int insertAppUserCoinLog(AppUserCoinLog appUserCoinLog);

    /**
     * 修改金币记录
     *
     * @param appUserCoinLog 金币记录
     * @return 结果
     */
    public int updateAppUserCoinLog(AppUserCoinLog appUserCoinLog);

    /**
     * 批量删除金币记录
     *
     * @param ids 需要删除的金币记录主键集合
     * @return 结果
     */
    public int deleteAppUserCoinLogByIds(Long[] ids);

    /**
     * 删除金币记录信息
     *
     * @param id 金币记录主键
     * @return 结果
     */
    public int deleteAppUserCoinLogById(Long id);

    List<UserCoinLogVO> getSignInByUserId();

    List<UserCoinLogTradeVO> rechargeRecord();

    List<UserCoinLogTradeVO> rewardRecords();

    int selectByTypeAndUserIdAndSourceId(Long type, Long userId, Long signInRuleInfoId);
}
