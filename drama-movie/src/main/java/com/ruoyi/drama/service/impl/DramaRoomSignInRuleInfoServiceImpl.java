package com.ruoyi.drama.service.impl;

import java.util.Collections;
import java.util.List;

import com.ruoyi.common.drama.vo.SignInRuleVO;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.mobile.AppSecurityUtils;
import com.ruoyi.drama.domain.DramaRoomSignInRuleInfo;
import com.ruoyi.drama.mapper.DramaRoomSignInRuleInfoMapper;
import com.ruoyi.drama.service.IDramaRoomSignInRuleInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 签到规则明细Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
@Service
public class DramaRoomSignInRuleInfoServiceImpl implements IDramaRoomSignInRuleInfoService
{
    @Autowired
    private DramaRoomSignInRuleInfoMapper dramaRoomSignInRuleInfoMapper;

    /**
     * 查询签到规则明细
     *
     * @param id 签到规则明细主键
     * @return 签到规则明细
     */
    @Override
    public DramaRoomSignInRuleInfo selectDramaRoomSignInRuleInfoById(Long id)
    {
        return dramaRoomSignInRuleInfoMapper.selectDramaRoomSignInRuleInfoById(id);
    }

    /**
     * 查询签到规则明细列表
     *
     * @param dramaRoomSignInRuleInfo 签到规则明细
     * @return 签到规则明细
     */
    @Override
    public List<DramaRoomSignInRuleInfo> selectDramaRoomSignInRuleInfoList(DramaRoomSignInRuleInfo dramaRoomSignInRuleInfo)
    {
        return dramaRoomSignInRuleInfoMapper.selectDramaRoomSignInRuleInfoList(dramaRoomSignInRuleInfo);
    }

    /**
     * 新增签到规则明细
     *
     * @param dramaRoomSignInRuleInfo 签到规则明细
     * @return 结果
     */
    @Override
    public int insertDramaRoomSignInRuleInfo(DramaRoomSignInRuleInfo dramaRoomSignInRuleInfo)
    {
        dramaRoomSignInRuleInfo.setCreateTime(DateUtils.getNowDate());
        return dramaRoomSignInRuleInfoMapper.insertDramaRoomSignInRuleInfo(dramaRoomSignInRuleInfo);
    }

    /**
     * 修改签到规则明细
     *
     * @param dramaRoomSignInRuleInfo 签到规则明细
     * @return 结果
     */
    @Override
    public int updateDramaRoomSignInRuleInfo(DramaRoomSignInRuleInfo dramaRoomSignInRuleInfo)
    {
        dramaRoomSignInRuleInfo.setUpdateTime(DateUtils.getNowDate());
        return dramaRoomSignInRuleInfoMapper.updateDramaRoomSignInRuleInfo(dramaRoomSignInRuleInfo);
    }

    /**
     * 批量删除签到规则明细
     *
     * @param ids 需要删除的签到规则明细主键
     * @return 结果
     */
    @Override
    public int deleteDramaRoomSignInRuleInfoByIds(Long[] ids)
    {
        return dramaRoomSignInRuleInfoMapper.deleteDramaRoomSignInRuleInfoByIds(ids);
    }

    /**
     * 删除签到规则明细信息
     *
     * @param id 签到规则明细主键
     * @return 结果
     */
    @Override
    public int deleteDramaRoomSignInRuleInfoById(Long id)
    {
        return dramaRoomSignInRuleInfoMapper.deleteDramaRoomSignInRuleInfoById(id);
    }

    @Override
    public List<SignInRuleVO> getUserBenefits(Integer type) {
        Long appUserId = AppSecurityUtils.getAppUserId();
        return dramaRoomSignInRuleInfoMapper.getUserBenefits(appUserId,type);
    }

    @Override
    public List<DramaRoomSignInRuleInfo> getByRuleType(Integer type) {
        return dramaRoomSignInRuleInfoMapper.getByRuleType(type);
    }
}
