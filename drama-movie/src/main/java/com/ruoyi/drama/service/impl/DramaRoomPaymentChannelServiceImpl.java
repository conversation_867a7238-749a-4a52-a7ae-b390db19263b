package com.ruoyi.drama.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.drama.domain.DramaRoomPaymentChannel;
import com.ruoyi.drama.mapper.DramaRoomPaymentChannelMapper;
import com.ruoyi.drama.service.IDramaRoomPaymentChannelService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


/**
 * 支付渠道配置Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-08-08
 */
@Service
public class DramaRoomPaymentChannelServiceImpl implements IDramaRoomPaymentChannelService
{
    @Autowired
    private DramaRoomPaymentChannelMapper dramaRoomPaymentChannelMapper;

    /**
     * 查询支付渠道配置
     * 
     * @param id 支付渠道配置主键
     * @return 支付渠道配置
     */
    @Override
    public DramaRoomPaymentChannel selectDramaRoomPaymentChannelById(Long id)
    {
        return dramaRoomPaymentChannelMapper.selectDramaRoomPaymentChannelById(id);
    }

    /**
     * 查询支付渠道配置列表
     * 
     * @param dramaRoomPaymentChannel 支付渠道配置
     * @return 支付渠道配置
     */
    @Override
    public List<DramaRoomPaymentChannel> selectDramaRoomPaymentChannelList(DramaRoomPaymentChannel dramaRoomPaymentChannel)
    {
        return dramaRoomPaymentChannelMapper.selectDramaRoomPaymentChannelList(dramaRoomPaymentChannel);
    }

    /**
     * 新增支付渠道配置
     * 
     * @param dramaRoomPaymentChannel 支付渠道配置
     * @return 结果
     */
    @Override
    public int insertDramaRoomPaymentChannel(DramaRoomPaymentChannel dramaRoomPaymentChannel)
    {
        dramaRoomPaymentChannel.setCreateTime(DateUtils.getNowDate());
        return dramaRoomPaymentChannelMapper.insertDramaRoomPaymentChannel(dramaRoomPaymentChannel);
    }

    /**
     * 修改支付渠道配置
     * 
     * @param dramaRoomPaymentChannel 支付渠道配置
     * @return 结果
     */
    @Override
    public int updateDramaRoomPaymentChannel(DramaRoomPaymentChannel dramaRoomPaymentChannel)
    {
        dramaRoomPaymentChannel.setUpdateTime(DateUtils.getNowDate());
        return dramaRoomPaymentChannelMapper.updateDramaRoomPaymentChannel(dramaRoomPaymentChannel);
    }

    /**
     * 批量删除支付渠道配置
     * 
     * @param ids 需要删除的支付渠道配置主键
     * @return 结果
     */
    @Override
    public int deleteDramaRoomPaymentChannelByIds(Long[] ids)
    {
        return dramaRoomPaymentChannelMapper.deleteDramaRoomPaymentChannelByIds(ids);
    }

    /**
     * 删除支付渠道配置信息
     * 
     * @param id 支付渠道配置主键
     * @return 结果
     */
    @Override
    public int deleteDramaRoomPaymentChannelById(Long id)
    {
        return dramaRoomPaymentChannelMapper.deleteDramaRoomPaymentChannelById(id);
    }
}