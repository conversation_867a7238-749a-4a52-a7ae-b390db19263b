package com.ruoyi.drama.service.impl;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import cn.hutool.core.collection.CollectionUtil;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.drama.domain.DramaRoomVideo;
import com.ruoyi.drama.domain.DramaRoomVideoI18n;
import com.ruoyi.drama.mapper.DramaRoomVideoI18nMapper;
import com.ruoyi.drama.mapper.DramaRoomVideoMapper;
import com.ruoyi.drama.service.IDramaRoomVideoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


/**
 * 剧集Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-04
 */
@Service
public class DramaRoomVideoServiceImpl implements IDramaRoomVideoService
{
    @Autowired
    private DramaRoomVideoMapper dramaRoomVideoMapper;

    @Autowired
    private DramaRoomVideoI18nMapper dramaRoomVideoI18nMapper;

    /**
     * 查询剧集
     *
     * @param id 剧集主键
     * @return 剧集
     */
    @Override
    public DramaRoomVideo selectDramaRoomVideoById(Long id)
    {
        return dramaRoomVideoMapper.selectDramaRoomVideoById(id);
    }

    /**
     * 查询剧集列表
     *
     * @param dramaRoomVideo 剧集
     * @return 剧集
     */
    @Override
    public List<DramaRoomVideo> selectDramaRoomVideoList(DramaRoomVideo dramaRoomVideo)
    {
        return dramaRoomVideoMapper.selectDramaRoomVideoList(dramaRoomVideo);
    }

    /**
     * 新增剧集
     *
     * @param dramaRoomVideo 剧集
     * @return 结果
     */
    @Override
    public int insertDramaRoomVideo(DramaRoomVideo dramaRoomVideo)
    {
        dramaRoomVideo.setCreateTime(DateUtils.getNowDate());
        return dramaRoomVideoMapper.insertDramaRoomVideo(dramaRoomVideo);
    }

    /**
     * 修改剧集
     *
     * @param dramaRoomVideo 剧集
     * @return 结果
     */
    @Override
    public int updateDramaRoomVideo(DramaRoomVideo dramaRoomVideo)
    {
        dramaRoomVideo.setUpdateTime(DateUtils.getNowDate());
        return dramaRoomVideoMapper.updateDramaRoomVideo(dramaRoomVideo);
    }

    /**
     * 批量删除剧集
     *
     * @param ids 需要删除的剧集主键
     * @return 结果
     */
    @Override
    public int deleteDramaRoomVideoByIds(Long[] ids)
    {
        return dramaRoomVideoMapper.deleteDramaRoomVideoByIds(ids);
    }

    /**
     * 删除剧集信息
     *
     * @param id 剧集主键
     * @return 结果
     */
    @Override
    public int deleteDramaRoomVideoById(Long id)
    {
        return dramaRoomVideoMapper.deleteDramaRoomVideoById(id);
    }

    @Override
    public List<DramaRoomVideo> selectByMovieId(Long movieId, String languageCode) {
        DramaRoomVideo query = new DramaRoomVideo();
        query.setMovieId(movieId);
        List<DramaRoomVideo> dramaRoomVideoList = dramaRoomVideoMapper.selectDramaRoomVideoList(query);
        List<Long> videoIdList = dramaRoomVideoList.stream().map(DramaRoomVideo::getId).collect(Collectors.toList());
        List<DramaRoomVideoI18n> shortVideoI18nList = dramaRoomVideoI18nMapper.selectDramaRoomVideoI18nByVideoIds(videoIdList, Collections.singletonList(languageCode));
        Map<Long, String> videoI18nMap = shortVideoI18nList.stream().collect(Collectors.toMap(DramaRoomVideoI18n::getVideoId, DramaRoomVideoI18n::getSubtitleUrl));
        dramaRoomVideoList.forEach(video -> video.setSubtitleUrl(videoI18nMap.get(video.getId())));
        return dramaRoomVideoList;
    }

    @Override
    public List<DramaRoomVideo> getVideoListByMovieId(Long movieId) {
        DramaRoomVideo query = new DramaRoomVideo();
        query.setMovieId(movieId);
        List<DramaRoomVideo> dramaRoomVideoList = dramaRoomVideoMapper.selectDramaRoomVideoList(query);
        List<Long> videoIdList = dramaRoomVideoList.stream().map(DramaRoomVideo::getId).collect(Collectors.toList());
        List<DramaRoomVideoI18n> shortVideoI18nList = dramaRoomVideoI18nMapper.selectDramaRoomVideoI18nByVideoIds(videoIdList, null);
        Map<Long, List<DramaRoomVideoI18n>> videoMap = shortVideoI18nList.stream().collect(Collectors.groupingBy(DramaRoomVideoI18n::getVideoId));
        dramaRoomVideoList.forEach(video -> video.setVideoI18nList(videoMap.get(video.getId())));
        return dramaRoomVideoList;

    }
}
