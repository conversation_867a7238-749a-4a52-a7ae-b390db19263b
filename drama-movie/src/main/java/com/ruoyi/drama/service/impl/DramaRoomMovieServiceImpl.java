package com.ruoyi.drama.service.impl;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import cn.hutool.core.collection.CollectionUtil;
import com.ruoyi.common.drama.dto.DramaRoomMovieQueryDTO;
import com.ruoyi.common.drama.dto.DramaRoomMovieUnlockVideoDTO;
import com.ruoyi.common.drama.vo.*;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.mobile.AppSecurityUtils;
import com.ruoyi.drama.domain.*;
import com.ruoyi.drama.mapper.DramaRoomMovieCategoryMapper;
import com.ruoyi.drama.mapper.DramaRoomMovieMapper;
import com.ruoyi.drama.mapper.DramaRoomUserCollectMapper;
import com.ruoyi.drama.mapper.DramaRoomVideoMapper;
import com.ruoyi.drama.mapper.*;
import com.ruoyi.drama.service.IDramaRoomMovieService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import software.amazon.awssdk.services.s3.endpoints.internal.Value;

import java.util.Collections;
import java.util.Comparator;
import java.util.List;


/**
 * 短剧Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-04
 */
@Service
public class DramaRoomMovieServiceImpl implements IDramaRoomMovieService {
    @Autowired
    private DramaRoomMovieMapper dramaRoomMovieMapper;

    @Autowired
    private DramaRoomUserCollectMapper dramaRoomUserFavoriteMapper;

    @Autowired
    private DramaRoomVideoMapper dramaRoomVideoMapper;

    @Autowired
    private DramaRoomTagMapper dramaRoomTagMapper;

    @Autowired
    private DramaRoomMovieActionMapper dramaRoomMovieActionMapper;

    @Autowired
    private DramaRoomMovieCategoryMapper dramaRoomMovieCategoryMapper;

    @Autowired
    private DramaRoomMovieI18nMapper dramaRoomMovieI18nMapper;

    @Autowired
    private DramaRoomLanguageConfigMapper dramaRoomLanguageConfigMapper;

    @Autowired
    private DramaRoomVideoI18nMapper dramaRoomVideoI18nMapper;

    @Autowired
    private DramaRoomTagI18nMapper dramaRoomTagI18nMapper;

    @Autowired
    private DramaRoomUserUnlockVideoMapper dramaRoomUserUnlockVideoMapper;

    @Autowired
    private DramaRoomVideoChannelCoinMapper dramaRoomVideoChannelCoinMapper;

    /**
     * 查询短剧
     *
     * @param id 短剧主键
     * @return 短剧
     */
    @Override
    public DramaRoomMovie selectDramaRoomMovieById(Long id) {
        return dramaRoomMovieMapper.selectDramaRoomMovieById(id);
    }

    /**
     * 查询短剧列表
     *
     * @return 短剧
     */
    @Override
    public List<DramaRoomMovie> selectDramaRoomMovieList(DramaRoomMovie dramaRoomMovie) {
        //查询短剧的翻译情况
        List<DramaRoomMovie> dramaRoomMovieList = dramaRoomMovieMapper.selectDramaRoomMovieList(dramaRoomMovie);
        if(CollectionUtil.isNotEmpty(dramaRoomMovieList)){
            List<DramaRoomLanguageConfig> dramaRoomLanguageConfigList = dramaRoomLanguageConfigMapper.selectDramaRoomLanguageConfigList(new DramaRoomLanguageConfig());
            Map<String, DramaRoomLanguageConfig> langguageMap = dramaRoomLanguageConfigList.stream().collect(Collectors.toMap(DramaRoomLanguageConfig::getCode, Function.identity()));
            List<Long> movieIdList = dramaRoomMovieList.stream().map(DramaRoomMovie::getId).collect(Collectors.toList());
            //查询已翻译的剧
            List<DramaRoomMovieI18n> shortMovieI18nList = dramaRoomMovieI18nMapper.selectDramaRoomMovieI18nByMovieIdList(movieIdList);
            Map<Long, List<DramaRoomMovieI18n>> movieI18nMap = shortMovieI18nList.stream().collect(Collectors.groupingBy(DramaRoomMovieI18n::getMovieId));
            for (DramaRoomMovie roomMovie : dramaRoomMovieList) {
                List<DramaRoomMovieI18n> movieI18nList = movieI18nMap.get(roomMovie.getId());
                if(CollectionUtil.isNotEmpty(movieI18nList)){
                    List<DramaRoomLanguageConfig> tmpLanguageConfigList = new ArrayList<>();
                    for (DramaRoomMovieI18n dramaRoomMovieI18n : movieI18nList) {
                        if(langguageMap.containsKey(dramaRoomMovieI18n.getLanguageCode())){
                            DramaRoomLanguageConfig dramaRoomLanguageConfig = langguageMap.get(dramaRoomMovieI18n.getLanguageCode());
                            DramaRoomLanguageConfig tmp = new DramaRoomLanguageConfig();
                            BeanUtils.copyProperties(dramaRoomLanguageConfig, tmp);
                            tmp.setTranslateStatus(dramaRoomMovieI18n.getStatus());
                            tmpLanguageConfigList.add(tmp);
                        }
                    }
                    roomMovie.setLanguageConfigList(tmpLanguageConfigList);
                }
            }
        }
        return dramaRoomMovieList;
    }

    /**
     * 新增短剧
     *
     * @param dramaRoomMovie 短剧
     * @return 结果
     */
    @Override
    public int insertDramaRoomMovie(DramaRoomMovie dramaRoomMovie) {
        dramaRoomMovie.setCreateTime(DateUtils.getNowDate());
        int i = dramaRoomMovieMapper.insertDramaRoomMovie(dramaRoomMovie);
        if (i > 0) {
            i = dramaRoomMovieActionMapper.insertDramaRoomMovieAction(dramaRoomMovie);
        }
        return i;
    }

    /**
     * 修改短剧
     *
     * @param dramaRoomMovie 短剧
     * @return 结果
     */
    @Override
    public int updateDramaRoomMovie(DramaRoomMovie dramaRoomMovie) {
        dramaRoomMovie.setUpdateTime(DateUtils.getNowDate());
        return dramaRoomMovieMapper.updateDramaRoomMovie(dramaRoomMovie);
    }

    /**
     * 批量删除短剧
     *
     * @param ids 需要删除的短剧主键
     * @return 结果
     */
    @Override
    public int deleteDramaRoomMovieByIds(Long[] ids) {
        return dramaRoomMovieMapper.deleteDramaRoomMovieByIds(ids);
    }

    /**
     * 删除短剧信息
     *
     * @param id 短剧主键
     * @return 结果
     */
    @Override
    public int deleteDramaRoomMovieById(Long id) {
        return dramaRoomMovieMapper.deleteDramaRoomMovieById(id);
    }

    @Override
    public MovieDetailVO getMovieDetail(Long id, String languageCode) {
        MovieDetailVO movieDetailVO = new MovieDetailVO();
        //先查询短剧基本信息
        DramaRoomMovie movie = dramaRoomMovieMapper.selectDramaRoomMovieById(id);
        BeanUtils.copyProperties(movie, movieDetailVO);
        //查询收藏状态
        DramaRoomUserCollect userFavoriteQuery = new DramaRoomUserCollect();
        userFavoriteQuery.setUserId(AppSecurityUtils.getAppUserId());
        userFavoriteQuery.setMovieId(id);
        //查询 收藏次数 观看次数 搜索次数
        DramaRoomMovieAction dramaRoomMovieAction = dramaRoomMovieActionMapper.selectByMovieId(id);
        if(null != dramaRoomMovieAction){
            movieDetailVO.setPlayCount(dramaRoomMovieAction.getPlayCount());
            movieDetailVO.setCollectCount(dramaRoomMovieAction.getCollectCount());
            movieDetailVO.setSearchCount(dramaRoomMovieAction.getSearchCount());
            movieDetailVO.setLikeCount(dramaRoomMovieAction.getLikeCount());
        }
        List<DramaRoomUserCollect> dramaRoomUserFavoriteList = dramaRoomUserFavoriteMapper.selectDramaRoomUserCollectList(userFavoriteQuery);
        if (CollectionUtils.isEmpty(dramaRoomUserFavoriteList)) {
            movieDetailVO.setIsCollect(false);
        } else {
            movieDetailVO.setIsCollect(dramaRoomUserFavoriteList.get(0).getStatus() == 0L);
        }
        //多语言设置
        DramaRoomMovieI18n dramaRoomMovieI18nQuery = new DramaRoomMovieI18n();
        dramaRoomMovieI18nQuery.setMovieId(id);
        dramaRoomMovieI18nQuery.setLanguageCode(languageCode);
        List<DramaRoomMovieI18n> dramaRoomMovieI18ns = dramaRoomMovieI18nMapper.selectDramaRoomMovieI18nList(dramaRoomMovieI18nQuery);
        if(dramaRoomMovieI18ns.iterator().hasNext()){
            DramaRoomMovieI18n next = dramaRoomMovieI18ns.iterator().next();
            movieDetailVO.setTitle(next.getTitle());
            movieDetailVO.setDescription(next.getDescription());
            movieDetailVO.setCoverImage(next.getCoverImage());
            movieDetailVO.setDirector(next.getDirector());
            movieDetailVO.setActors(next.getActors());
        }
        //剧集
        DramaRoomVideo dramaRoomVideoQuery = new DramaRoomVideo();
        dramaRoomVideoQuery.setMovieId(id);
        List<DramaRoomVideo> videoList = dramaRoomVideoMapper.selectDramaRoomVideoList(dramaRoomVideoQuery);
        //根据videoNum 正序排序
        videoList.sort(Comparator.comparing(DramaRoomVideo::getVideoNum));
        List<VideoVO> videoVOList = DramaRoomVideo.toVideoVOList(videoList);
        //查询剧集多语言
        List<Long> videoIdList = videoVOList.stream().map(VideoVO::getId).collect(Collectors.toList());
        List<DramaRoomVideoI18n> dramaRoomVideoI18nList = dramaRoomVideoI18nMapper.selectDramaRoomVideoI18nByVideoIds(videoIdList, null);
        List<DramaRoomVideoI18nVO> videoI18nVOList = DramaRoomVideoI18n.toVOList(dramaRoomVideoI18nList);
        Map<Long, List<DramaRoomVideoI18nVO>> videoI18nMap = videoI18nVOList.stream().collect(Collectors.groupingBy(DramaRoomVideoI18nVO::getVideoId));
        videoVOList.forEach(videoVO -> videoVO.setSubtitleUrlList(videoI18nMap.getOrDefault(videoVO.getId(), null)));


        // 查询渠道视频所需金币进行扣费解锁，TODO 等待sem的深链逻辑完成后，才可以获取金币扣费规则ID
        List<DramaRoomVideoChannelCoin> dramaRoomVideoChannelCoins = dramaRoomVideoChannelCoinMapper
                .selectDramaRoomVideoChannelCoinByCoinRuleIdAndMovieId(0L, id);
        if (!CollectionUtils.isEmpty(dramaRoomVideoChannelCoins)) {
            videoVOList.forEach(videoVO -> {
                Optional<DramaRoomVideoChannelCoin> dramaRoomVideoChannelCoinOptional = dramaRoomVideoChannelCoins
                        .stream().filter(coin -> coin.getVideoId().equals(videoVO.getId())).findFirst();
                dramaRoomVideoChannelCoinOptional.ifPresent(dramaRoomVideoChannelCoin -> videoVO.setUnlockCoin(dramaRoomVideoChannelCoin.getCoin()));
            });
        }

        // 处理视频解锁状态
        Long appUserId = AppSecurityUtils.getAppUserId();
        List<DramaRoomUserUnlockVideo> dramaRoomUserUnlockVideos = dramaRoomUserUnlockVideoMapper.selectDramaRoomUserUnlockVideoByUserIdAndMovieId(appUserId, id);
        videoVOList.forEach(videoVO -> videoVO.setUnlock(videoVO.getUnlockCoin() == null || videoVO.getUnlockCoin() == 0 || dramaRoomUserUnlockVideos.stream().anyMatch(unlock -> unlock.getVideoId().equals(videoVO.getId()))));

        movieDetailVO.setVideos(videoVOList);
        return movieDetailVO;
    }

    @Override
    public List<MovieVO> getHotRecommend(String languageCode) {
        List<DramaRoomMovie> hotRecommend = dramaRoomMovieMapper.getHotRecommend(10);
        if (CollectionUtils.isEmpty(hotRecommend)) {
            return Collections.emptyList();
        }
        return convertMovieI18n(selectMovieTags(hotRecommend), languageCode);
    }

    @Override
    public List<MovieVO> getNewList(String languageCode) {
        List<DramaRoomMovie> dramaRoomMovies = dramaRoomMovieMapper.selectDramaRoomMovieNewList(10);
        if (CollectionUtils.isEmpty(dramaRoomMovies)) {
            return Collections.emptyList();
        }
        return convertMovieI18n(selectMovieTags(dramaRoomMovies), languageCode);
    }

    @Override
    public List<MovieVO> getHotSearch(String languageCode) {
        List<DramaRoomMovie> dramaRoomMovies = dramaRoomMovieMapper.getHotSearch(10);
        if (CollectionUtils.isEmpty(dramaRoomMovies)) {
            return Collections.emptyList();
        }
        return convertMovieI18n(selectMovieTags(dramaRoomMovies), languageCode);
    }

    @Override
    public List<DramaRoomMovie> queryByMovieName(String name) {
        if(StringUtils.isEmpty(name)){
            return Collections.emptyList();
        }
        //再短剧多语言表模糊查询剧名
        List<DramaRoomMovieI18n> list =dramaRoomMovieI18nMapper.selectByMovieName(name);
        if(CollectionUtil.isNotEmpty(list)){
            Map<Long, DramaRoomMovieI18n> movieI18nMap = list.stream().collect(Collectors.toMap(DramaRoomMovieI18n::getMovieId, Function.identity(), (o, v) -> v));
            Long[] array = list.stream().map(DramaRoomMovieI18n::getMovieId).distinct().toArray(Long[]::new);
            List<DramaRoomMovie> dramaRoomMovies = dramaRoomMovieMapper.selectDramaRoomMovieByIds(array);
            if(CollectionUtil.isNotEmpty(dramaRoomMovies)){
                for (DramaRoomMovie data : dramaRoomMovies) {
                    data.setTitle(movieI18nMap.get(data.getId()).getTitle());
                    data.setDescription(movieI18nMap.get(data.getId()).getDescription());
                    data.setCoverImage(movieI18nMap.get(data.getId()).getCoverImage());
                    data.setDirector(movieI18nMap.get(data.getId()).getDirector());
                    data.setActors(movieI18nMap.get(data.getId()).getActors());
                }
            }
            //获取相应语言的翻译
            return dramaRoomMovies;
        }
        return new ArrayList<>();
    }

    private List<MovieVO> selectMovieTags(List<DramaRoomMovie> hotRecommend) {
        // 查询标签
        List<MovieVO> movieVOS = DramaRoomMovie.toVOList(hotRecommend);
        for (MovieVO movieVO : movieVOS) {
            List<MovieTagVO> tags = DramaRoomTag.toVOList(dramaRoomTagMapper.selectTagListByMovieId(movieVO.getId()));
            movieVO.setTags(tags);
        }
        return movieVOS;
    }

    private List<MovieVO> convertMovieI18n(List<MovieVO> list, String languageCode){
        if(CollectionUtil.isEmpty(list)){
            return Collections.emptyList();
        }
        List<Long> movieIdList = list.stream().map(MovieVO::getId).collect(Collectors.toList());
        List<DramaRoomMovieI18n> dramaRoomMovieI18nList = dramaRoomMovieI18nMapper.selectDramaRoomMovieI18nByMovieIdListAndLanguageCode(movieIdList, languageCode);
        Map<Long, DramaRoomMovieI18n> movieI18nMap = dramaRoomMovieI18nList.stream().collect(Collectors.toMap(DramaRoomMovieI18n::getMovieId, Function.identity()));
        //处理标签
        List<Long> tagIdList = list.stream().flatMap(movieVO -> movieVO.getTags().stream().map(MovieTagVO::getId)).distinct().collect(Collectors.toList());
        //查询tag翻译记录
        Map<Long, String> tagI18nMap = new HashMap<>();
        if(CollectionUtil.isNotEmpty(tagIdList)){
            List<DramaRoomTagI18n> tagI18nList = dramaRoomTagI18nMapper.selectDramaRoomTagI18nByTagIdsAndLanguageCode(tagIdList, languageCode);
            tagI18nMap = tagI18nList.stream().collect(Collectors.toMap(DramaRoomTagI18n::getTagId, DramaRoomTagI18n::getTagName));
        }
        Map<Long, String> finalTagI18nMap = tagI18nMap;
        for (MovieVO movieVO : list) {
            movieVO.setTitle(movieI18nMap.containsKey(movieVO.getId()) ? movieI18nMap.get(movieVO.getId()).getTitle() : movieVO.getTitle());
            movieVO.setCoverImage(movieI18nMap.containsKey(movieVO.getId()) ? movieI18nMap.get(movieVO.getId()).getCoverImage() : movieVO.getCoverImage());
            List<MovieTagVO> tags = movieVO.getTags();
            if(CollectionUtil.isNotEmpty(tags)){
                tags.forEach(movieTagVO -> movieTagVO.setTag(finalTagI18nMap.containsKey(movieTagVO.getId()) ? finalTagI18nMap.get(movieTagVO.getId()) : movieTagVO.getTag()));
            }
        }
        return list;
    }

    @Override
    public List<DramaRoomMovie> queryMovie(DramaRoomMovieQueryDTO queryDTO, String languageCode) {
        //先根据分类Ids 查询各个分类下的剧 然后求各个分类下剧的交集
        if(null != queryDTO.getCategoryId()){
            //查询该分类下的短剧Id
            DramaRoomMovieCategory dramaRoomMovieCategoryQuery = new DramaRoomMovieCategory();
            dramaRoomMovieCategoryQuery.setCategoryId(queryDTO.getCategoryId());
            List<DramaRoomMovieCategory> dramaRoomMovieCategories = dramaRoomMovieCategoryMapper.selectDramaRoomMovieCategoryList(dramaRoomMovieCategoryQuery);
            if(!CollectionUtils.isEmpty(dramaRoomMovieCategories)){
                List<Long> collect = dramaRoomMovieCategories.stream().map(DramaRoomMovieCategory::getMovieId).collect(Collectors.toList());
                queryDTO.setMovieIdList(collect);
            }
        }
        List<DramaRoomMovie> dramaRoomMovieList = dramaRoomMovieMapper.queryMovie(queryDTO);
        //处理多语言
        List<Long> movieIdList = dramaRoomMovieList.stream().map(DramaRoomMovie::getId).collect(Collectors.toList());
        List<DramaRoomMovieI18n> dramaRoomMovieI18nList = dramaRoomMovieI18nMapper.selectDramaRoomMovieI18nByMovieIdListAndLanguageCode(movieIdList, languageCode);
        Map<Long, DramaRoomMovieI18n> movieI18nMap = dramaRoomMovieI18nList.stream().collect(Collectors.toMap(DramaRoomMovieI18n::getMovieId, Function.identity()));
        for (DramaRoomMovie dramaRoomMovie : dramaRoomMovieList) {
            dramaRoomMovie.setTitle(movieI18nMap.containsKey(dramaRoomMovie.getId()) ? movieI18nMap.get(dramaRoomMovie.getId()).getTitle() : dramaRoomMovie.getTitle());
            dramaRoomMovie.setDescription(movieI18nMap.containsKey(dramaRoomMovie.getId()) ? movieI18nMap.get(dramaRoomMovie.getId()).getDescription() : dramaRoomMovie.getDescription());
            dramaRoomMovie.setCoverImage(movieI18nMap.containsKey(dramaRoomMovie.getId()) ? movieI18nMap.get(dramaRoomMovie.getId()).getCoverImage() : dramaRoomMovie.getCoverImage());
            dramaRoomMovie.setDirector(movieI18nMap.containsKey(dramaRoomMovie.getId()) ? movieI18nMap.get(dramaRoomMovie.getId()).getDirector() : dramaRoomMovie.getDirector());
            dramaRoomMovie.setActors(movieI18nMap.containsKey(dramaRoomMovie.getId()) ? movieI18nMap.get(dramaRoomMovie.getId()).getActors() : dramaRoomMovie.getActors());
        }
        return dramaRoomMovieList;
    }
}
