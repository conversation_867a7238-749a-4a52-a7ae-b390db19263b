package com.ruoyi.drama.manager.factory;

import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.Gemini25FlashUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.spring.SpringUtils;
import com.ruoyi.drama.domain.*;
import com.ruoyi.drama.service.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.TimerTask;

@Slf4j
public class I18nAsyncFactory {

    public static TimerTask getAreaI18n(Long areaId, String area, String loginId) {
        return new TimerTask() {
            @Override
            public void run() {
                // 获取系统语言配置
                IDramaRoomLanguageConfigService languageConfigService = SpringUtils.getBean(IDramaRoomLanguageConfigService.class);
                List<DramaRoomLanguageConfig> languageConfigs = languageConfigService.selectDramaRoomLanguageConfigList(new DramaRoomLanguageConfig());
                List<DramaRoomAreaI18n> i18ns = new ArrayList<>();
                for (DramaRoomLanguageConfig languageConfig : languageConfigs) {
                    DramaRoomAreaI18n dramaRoomAreaI18n = new DramaRoomAreaI18n();
                    log.info("获取语言配置：{}", languageConfig);
                    try {
                        String lang = Gemini25FlashUtils.translate(area, languageConfig.getZhName());
                        if (StringUtils.isEmpty(lang)) {
                            dramaRoomAreaI18n.setArea(area);
                        } else {
                            dramaRoomAreaI18n.setArea(lang);
                        }
                        dramaRoomAreaI18n.setLanguageCode(languageConfig.getCode());
                        dramaRoomAreaI18n.setAreaId(areaId);
                        dramaRoomAreaI18n.setCreateTime(DateUtils.getNowDate());
                        dramaRoomAreaI18n.setCreateBy(loginId);
                        i18ns.add(dramaRoomAreaI18n);
                    } catch (IOException e) {
                        log.error("【{}】翻译异常：{}", languageConfig.getZhName(), e.getMessage());
                    }
                }
                if (CollectionUtils.isEmpty(i18ns)) {
                    log.error("【{}】翻译异常：{}", area, "没有获取到翻译结果");
                    return;
                }
                IDramaRoomAreaI18nService areaI18nService = SpringUtils.getBean(IDramaRoomAreaI18nService.class);
                areaI18nService.insertDramaRoomAreaI18nList(i18ns);
            }
        };
    }

    public static TimerTask getAudioI18n(Long audioId, String audio, String loginId) {
        return new TimerTask() {
            @Override
            public void run() {
                // 获取系统语言配置
                IDramaRoomLanguageConfigService languageConfigService = SpringUtils.getBean(IDramaRoomLanguageConfigService.class);
                List<DramaRoomLanguageConfig> languageConfigs = languageConfigService.selectDramaRoomLanguageConfigList(new DramaRoomLanguageConfig());
                List<DramaRoomAudioI18n> i18ns = new ArrayList<>();
                for (DramaRoomLanguageConfig languageConfig : languageConfigs) {
                    DramaRoomAudioI18n dramaRoomAudioI18n = new DramaRoomAudioI18n();
                    log.info("获取语言配置：{}", languageConfig);
                    try {
                        String lang = Gemini25FlashUtils.translate(audio, languageConfig.getZhName());
                        if (StringUtils.isEmpty(lang)) {
                            dramaRoomAudioI18n.setAudio(audio);
                        } else {
                            dramaRoomAudioI18n.setAudio(lang);
                        }
                        dramaRoomAudioI18n.setLanguageCode(languageConfig.getCode());
                        dramaRoomAudioI18n.setAudioId(audioId);
                        dramaRoomAudioI18n.setCreateTime(DateUtils.getNowDate());
                        dramaRoomAudioI18n.setCreateBy(loginId);
                        i18ns.add(dramaRoomAudioI18n);
                    } catch (IOException e) {
                        log.error("【{}】翻译异常：{}", languageConfig.getZhName(), e.getMessage());
                    }
                }
                if (CollectionUtils.isEmpty(i18ns)) {
                    log.error("【{}】翻译异常：{}", audio, "没有获取到翻译结果");
                    return;
                }
                IDramaRoomAudioI18nService audioI18nService = SpringUtils.getBean(IDramaRoomAudioI18nService.class);
                audioI18nService.insertDramaRoomAudioI18nList(i18ns);
            }
        };
    }

    public static TimerTask getCategoryI18n(Long categoryId, String category, String loginId) {
        return new TimerTask() {
            @Override
            public void run() {
                // 获取系统语言配置
                IDramaRoomLanguageConfigService languageConfigService = SpringUtils.getBean(IDramaRoomLanguageConfigService.class);
                List<DramaRoomLanguageConfig> languageConfigs = languageConfigService.selectDramaRoomLanguageConfigList(new DramaRoomLanguageConfig());
                List<DramaRoomCategoryI18n> i18ns = new ArrayList<>();
                for (DramaRoomLanguageConfig languageConfig : languageConfigs) {
                    DramaRoomCategoryI18n dramaRoomCategoryI18n = new DramaRoomCategoryI18n();
                    log.info("获取语言配置：{}", languageConfig);
                    try {
                        String lang = Gemini25FlashUtils.translate(category, languageConfig.getZhName());
                        if (StringUtils.isEmpty(lang)) {
                            dramaRoomCategoryI18n.setCategoryName(category);
                        } else {
                            dramaRoomCategoryI18n.setCategoryName(lang);
                        }
                        dramaRoomCategoryI18n.setLanguageCode(languageConfig.getCode());
                        dramaRoomCategoryI18n.setCategoryId(categoryId);
                        dramaRoomCategoryI18n.setCreateTime(DateUtils.getNowDate());
                        dramaRoomCategoryI18n.setCreateBy(loginId);
                        i18ns.add(dramaRoomCategoryI18n);
                    }catch (IOException e){
                        log.error("【{}】翻译异常：{}", languageConfig.getZhName(), e.getMessage());
                    }
                }
                if (CollectionUtils.isEmpty(i18ns)) {
                    log.error("【{}】翻译异常：{}", category, "没有获取到翻译结果");
                    return;
                }
                IDramaRoomCategoryI18nService categoryI18nService = SpringUtils.getBean(IDramaRoomCategoryI18nService.class);
                categoryI18nService.insertDramaRoomCategoryI18nList(i18ns);
            }
        };
    }

    public static TimerTask getTagI18n(Long tagId, String tag, String loginId) {
        return new TimerTask() {
            @Override
            public void run() {
                // 获取系统语言配置
                IDramaRoomLanguageConfigService languageConfigService = SpringUtils.getBean(IDramaRoomLanguageConfigService.class);
                List<DramaRoomLanguageConfig> languageConfigs = languageConfigService.selectDramaRoomLanguageConfigList(new DramaRoomLanguageConfig());
                List<DramaRoomTagI18n> i18ns = new ArrayList<>();
                for (DramaRoomLanguageConfig languageConfig : languageConfigs) {
                    DramaRoomTagI18n dramaRoomTagI18n = new DramaRoomTagI18n();
                    log.info("获取语言配置：{}", languageConfig);
                    try {
                        String lang = Gemini25FlashUtils.translate(tag, languageConfig.getZhName());
                        if (StringUtils.isEmpty(lang)) {
                            dramaRoomTagI18n.setTagName(tag);
                        } else {
                            dramaRoomTagI18n.setTagName(lang);
                        }
                        dramaRoomTagI18n.setLanguageCode(languageConfig.getCode());
                        dramaRoomTagI18n.setTagId(tagId);
                        dramaRoomTagI18n.setCreateTime(DateUtils.getNowDate());
                        dramaRoomTagI18n.setCreateBy(loginId);
                        i18ns.add(dramaRoomTagI18n);
                    }catch (IOException e){
                        log.error("【{}】翻译异常：{}", languageConfig.getZhName(), e.getMessage());
                    }
                }
                if (CollectionUtils.isEmpty(i18ns)) {
                    log.error("【{}】翻译异常：{}", tag, "没有获取到翻译结果");
                    return;
                }
                IDramaRoomTagI18nService tagI18nService = SpringUtils.getBean(IDramaRoomTagI18nService.class);
                tagI18nService.insertDramaRoomTagI18nList(i18ns);
            }
        };
    }
}
