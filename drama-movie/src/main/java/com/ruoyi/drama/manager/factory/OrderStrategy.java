package com.ruoyi.drama.manager.factory;

import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.drama.domain.ShortAppPurchaseVerifyResult;
import com.ruoyi.common.drama.dto.IapVerifyDTO;
import com.ruoyi.common.enums.OrderRefundStatusEnum;
import com.ruoyi.common.enums.OrderStatusEnum;
import com.ruoyi.common.enums.PayChannelEnum;
import com.ruoyi.common.enums.PayMethodEnum;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.OrderUtils;
import com.ruoyi.common.utils.spring.SpringUtils;
import com.ruoyi.drama.domain.DramaRoomOrder;
import com.ruoyi.drama.domain.DramaRoomTransactionRecord;
import com.ruoyi.drama.mapper.DramaRoomOrderMapper;
import com.ruoyi.drama.mapper.DramaRoomTransactionRecordMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.TimerTask;

@Slf4j
public class OrderStrategy {

    /**
     * 内购验证通过-批量创建订单
     */
    public static TimerTask iapBatchCreateOrder(List<ShortAppPurchaseVerifyResult> appleVerifyResultList, IapVerifyDTO dto, String verificationJson) {
        return new TimerTask() {
            @Override
            @Transactional
            public void run() {
                log.info("创建订单-交易流水-修改用户金币等信息");
                Long userId = dto.getUserId();
                String receipt = dto.getReceipt();

                // 票据和验证结果 保存流水中
                Map<String, Object> extraMap = new HashMap<>();
                extraMap.put("receipt", receipt);
                extraMap.put("verificationJson", verificationJson);
                String extra = JSONObject.toJSONString(extraMap);

                // todo 调整

                DramaRoomOrderMapper orderMapper = SpringUtils.getBean(DramaRoomOrderMapper.class);
                DramaRoomTransactionRecordMapper transactionRecordMapper = SpringUtils.getBean(DramaRoomTransactionRecordMapper.class);

                appleVerifyResultList.forEach(appleVerifyResult -> {
                    // 初始化订单
                    DramaRoomOrder dramaRoomOrder = new DramaRoomOrder();
                    dramaRoomOrder.setOrderNo(OrderUtils.getOrderNo());
                    dramaRoomOrder.setUserId(userId);
                    dramaRoomOrder.setProductId(appleVerifyResult.getProductId());
                    dramaRoomOrder.setProductType("充值".equals(appleVerifyResult.getOrderType()) ? 0 : 1);
                    dramaRoomOrder.setAmount(dto.getAmount());
                    dramaRoomOrder.setStatus(OrderStatusEnum.PAID.getStatus());
                    dramaRoomOrder.setRefundStatus(OrderRefundStatusEnum.UNREFUNDED.getStatus());
                    dramaRoomOrder.setChannelCode(PayChannelEnum.APPLE_IAP.getCode());
                    dramaRoomOrder.setPaymentMethod(PayMethodEnum.APPLE_IAP.getCode());
                    dramaRoomOrder.setPaymentTime(DateUtils.getNowDate());
                    dramaRoomOrder.setCreateBy("系统自建");
                    dramaRoomOrder.setCreateTime(DateUtils.getNowDate());
                    dramaRoomOrder.setPayEnvironment("Sandbox".equals(appleVerifyResult.getEnvironment()) ? 0 : 1);
                    dramaRoomOrder.setChannelTransactionId(appleVerifyResult.getTransactionId());
                    orderMapper.insertDramaRoomOrder(dramaRoomOrder);

                    // 初始化流水
                    DramaRoomTransactionRecord dramaRoomTransactionRecord = new DramaRoomTransactionRecord();
                    dramaRoomTransactionRecord.setOrderId(dramaRoomOrder.getId());
                    dramaRoomTransactionRecord.setTransNo(OrderUtils.getTransNo());
                    dramaRoomTransactionRecord.setUserId(userId);
                    dramaRoomTransactionRecord.setChannelCode(PayChannelEnum.APPLE_IAP.getCode());
                    dramaRoomTransactionRecord.setAmount(dto.getAmount());
                    dramaRoomTransactionRecord.setStatus(1);
                    dramaRoomTransactionRecord.setType(1);
                    dramaRoomTransactionRecord.setTransactionId(appleVerifyResult.getTransactionId());
                    dramaRoomTransactionRecord.setMethod(PayMethodEnum.APPLE_IAP.getCode());
                    dramaRoomTransactionRecord.setExtra(extra);
                    dramaRoomTransactionRecord.setCreateBy("系统自建");
                    dramaRoomTransactionRecord.setCreateTime(DateUtils.getNowDate());
                    transactionRecordMapper.insertDramaRoomTransactionRecord(dramaRoomTransactionRecord);

                    // todo 修改用户金币等信息

                });
            }
        };
    }


}
