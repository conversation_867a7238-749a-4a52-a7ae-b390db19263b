package com.ruoyi.drama.manager.factory;

import com.ruoyi.common.enums.AppUserActionEnum;
import com.ruoyi.common.utils.spring.SpringUtils;
import com.ruoyi.drama.domain.DramaRoomMovieAction;
import com.ruoyi.drama.mapper.DramaRoomMovieActionMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.TimerTask;

public class AppUserAsyncFactory {

    private static final Logger app_user_logger = LoggerFactory.getLogger("app-user");

    /**
     * 更新短剧用户行为数据
     *
     * @param movieId 短剧ID
     * @param count   行为次数
     * @param action  行为类型
     * @return
     */
    public static TimerTask updateDramaActionCount(Long movieId, Long count, AppUserActionEnum action) {

        return new TimerTask() {
            @Override
            public void run() {
                app_user_logger.info("更新短剧收藏次数，movie: {}", movieId);
                DramaRoomMovieActionMapper dramaRoomMovieActionMapper = SpringUtils.getBean(DramaRoomMovieActionMapper.class);
                // 实际更新数据库收藏次数的逻辑
                if (dramaRoomMovieActionMapper != null) {
                    try {
                        // 先查询短剧信息
                        DramaRoomMovieAction dramaRoomMovieAction = dramaRoomMovieActionMapper.selectByMovieId(movieId);
                        if (dramaRoomMovieAction != null) {
                            switch (action) {
                                case COLLECT:
                                    // 更新收藏次数
                                    Long collectCount = dramaRoomMovieAction.getCollectCount() != null ? dramaRoomMovieAction.getCollectCount() : 0L;
                                    Long newCollectCount = collectCount + count;
                                    // 确保收藏次数不为负数
                                    if (newCollectCount < 0) {
                                        newCollectCount = 0L;
                                    }
                                    dramaRoomMovieAction.setCollectCount(newCollectCount);
                                    app_user_logger.info("短剧收藏次数更新，movieid: {}, 新的收藏次数: {}", movieId, newCollectCount);
                                    break;
                                case LIKE:
                                    // 更新点赞次数
                                    Long likeCount = dramaRoomMovieAction.getLikeCount() != null ? dramaRoomMovieAction.getLikeCount() : 0L;
                                    Long newLikeCount = likeCount + count;
                                    if (newLikeCount < 0) {
                                        newLikeCount = 0L;
                                    }
                                    dramaRoomMovieAction.setLikeCount(newLikeCount);
                                    app_user_logger.info("短剧点赞次数更新，movieid: {}, 新的点赞次数: {}", movieId, newLikeCount);
                                    break;
                                case SEARCH:
                                    // 搜索
                                    Long searchCount = dramaRoomMovieAction.getSearchCount() != null ? dramaRoomMovieAction.getSearchCount() : 0L;
                                    Long newSearchCount = searchCount + count;
                                    if (newSearchCount < 0) {
                                        newSearchCount = 0L;
                                    }
                                    dramaRoomMovieAction.setSearchCount(newSearchCount);
                                    app_user_logger.info("短剧搜索次数更新，movieid: {}, 新的点赞次数: {}", movieId, newSearchCount);
                                    break;
                                case PLAY:
                                    // 播放 todo 这个是所有剧集的播放总和
                                    break;
                                default:
                                    break;
                            }
                            dramaRoomMovieActionMapper.updateDramaRoomMovieAction(dramaRoomMovieAction);
                        } else {
                            app_user_logger.warn("未找到短剧信息，movieId: {}", movieId);
                        }
                    } catch (Exception e) {
                        app_user_logger.error("更新短剧收藏次数数据库操作异常: {}", e.getMessage(), e);
                    }
                } else {
                    app_user_logger.error("DramaRoomMovieActionMapper未注入，无法更新短剧收藏次数");
                }
            }
        };
    }
}
