package com.ruoyi.drama.manager.factory;

import com.ruoyi.drama.strategy.PaymentStrategy;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 支付策略工厂
 * 用于管理和获取不同支付渠道的策略实现，实现支付渠道的动态切换
 */
@Component
public class PaymentStrategyFactory {

    /**
     * 存储支付策略的映射关系：channelCode -> PaymentStrategy
     */
    private final Map<String, PaymentStrategy> strategyMap = new ConcurrentHashMap<>();

    /**
     * 构造函数，自动注入所有PaymentStrategy实现类并初始化映射关系
     * Spring会自动将所有实现了PaymentStrategy接口的Bean注入到这个列表中
     */
    @Autowired
    public PaymentStrategyFactory(List<PaymentStrategy> paymentStrategies) {
        // 遍历所有支付策略实现类，建立channelCode与策略的映射
        for (PaymentStrategy strategy : paymentStrategies) {
            String channelCode = strategy.getChannelCode();
            // 检查是否有重复的渠道编码
            if (strategyMap.containsKey(channelCode)) {
                throw new IllegalStateException("支付渠道编码重复: " + channelCode + "，请检查策略实现类");
            }
            strategyMap.put(channelCode, strategy);
            // 日志输出加载的支付渠道
            System.out.println("已加载支付渠道策略: " + channelCode + "，实现类: " + strategy.getClass().getSimpleName());
        }
    }

    /**
     * 根据渠道编码获取对应的支付策略
     *
     * @param channelCode 支付渠道编码（如APPLE_IAP、GOOGLE_IAP、CARD_PAY等）
     * @return 对应的支付策略实现类
     * @throws IllegalArgumentException 如果渠道编码不存在或未实现
     */
    public PaymentStrategy getStrategy(String channelCode) {
        // 从映射中获取策略
        PaymentStrategy strategy = strategyMap.get(channelCode);
        if (strategy == null) {
            throw new IllegalArgumentException("不支持的支付渠道: " + channelCode + "，请检查渠道编码是否正确或是否已实现对应策略");
        }
        return strategy;
    }

    /**
     * 获取所有支持的支付渠道编码
     *
     * @return 支付渠道编码列表
     */
    public List<String> getSupportedChannels() {
        return new ArrayList<>(strategyMap.keySet());
    }
}
    