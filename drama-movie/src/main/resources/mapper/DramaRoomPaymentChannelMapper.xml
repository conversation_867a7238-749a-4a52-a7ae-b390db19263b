<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.drama.mapper.DramaRoomPaymentChannelMapper">

    <resultMap type="DramaRoomPaymentChannel" id="DramaRoomPaymentChannelResult">
        <result property="id"    column="id"    />
        <result property="channelCode"    column="channel_code"    />
        <result property="channelName"    column="channel_name"    />
        <result property="config"    column="config"    />
        <result property="status"    column="status"    />
        <result property="remark"    column="remark"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectDramaRoomPaymentChannelVo">
        select id, channel_code, channel_name, config, status, remark, create_by, create_time, update_by, update_time from drama_room_payment_channel
    </sql>

    <select id="selectDramaRoomPaymentChannelList" parameterType="DramaRoomPaymentChannel" resultMap="DramaRoomPaymentChannelResult">
        <include refid="selectDramaRoomPaymentChannelVo"/>
        <where>
            <if test="channelCode != null  and channelCode != ''"> and channel_code = #{channelCode}</if>
            <if test="channelName != null  and channelName != ''"> and channel_name like concat('%', #{channelName}, '%')</if>
            <if test="config != null  and config != ''"> and config = #{config}</if>
            <if test="status != null "> and status = #{status}</if>
        </where>
    </select>

    <select id="selectDramaRoomPaymentChannelById" parameterType="Long" resultMap="DramaRoomPaymentChannelResult">
        <include refid="selectDramaRoomPaymentChannelVo"/>
        where id = #{id}
    </select>
    <select id="selectDramaRoomPaymentChannel" resultMap="DramaRoomPaymentChannelResult">
        <include refid="selectDramaRoomPaymentChannelVo"/>
        where channel_code = #{channelCode} and status = 1
    </select>

    <insert id="insertDramaRoomPaymentChannel" parameterType="DramaRoomPaymentChannel" useGeneratedKeys="true" keyProperty="id">
        insert into drama_room_payment_channel
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="channelCode != null and channelCode != ''">channel_code,</if>
            <if test="channelName != null and channelName != ''">channel_name,</if>
            <if test="config != null">config,</if>
            <if test="status != null">status,</if>
            <if test="remark != null">remark,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="channelCode != null and channelCode != ''">#{channelCode},</if>
            <if test="channelName != null and channelName != ''">#{channelName},</if>
            <if test="config != null">#{config},</if>
            <if test="status != null">#{status},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updateDramaRoomPaymentChannel" parameterType="DramaRoomPaymentChannel">
        update drama_room_payment_channel
        <trim prefix="SET" suffixOverrides=",">
            <if test="channelCode != null and channelCode != ''">channel_code = #{channelCode},</if>
            <if test="channelName != null and channelName != ''">channel_name = #{channelName},</if>
            <if test="config != null">config = #{config},</if>
            <if test="status != null">status = #{status},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteDramaRoomPaymentChannelById" parameterType="Long">
        delete from drama_room_payment_channel where id = #{id}
    </delete>

    <delete id="deleteDramaRoomPaymentChannelByIds" parameterType="String">
        delete from drama_room_payment_channel where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>