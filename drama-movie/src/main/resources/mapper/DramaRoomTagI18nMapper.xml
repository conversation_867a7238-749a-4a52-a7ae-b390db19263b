<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.drama.mapper.DramaRoomTagI18nMapper">

    <resultMap type="DramaRoomTagI18n" id="DramaRoomTagI18nResult">
        <result property="id" column="id"/>
        <result property="tagId" column="tag_id"/>
        <result property="tagName" column="tag_name"/>
        <result property="languageCode" column="language_code"/>
        <result property="remark" column="remark"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="selectDramaRoomTagI18nVo">
        select id,
               tag_id,
               tag_name,
               language_code,
               remark,
               create_by,
               create_time,
               update_by,
               update_time
        from drama_room_tag_i18n
    </sql>

    <select id="selectDramaRoomTagI18nList" parameterType="DramaRoomTagI18n" resultMap="DramaRoomTagI18nResult">
        <include refid="selectDramaRoomTagI18nVo"/>
        <where>
            <if test="tagId != null ">and tag_id = #{tagId}</if>
            <if test="tagName != null  and tagName != ''">and tag_name like concat('%', #{tagName}, '%')</if>
            <if test="languageCode != null and languageCode != ''">and language_code = #{languageCode}</if>
        </where>
    </select>

    <select id="selectDramaRoomTagI18nById" parameterType="Long" resultMap="DramaRoomTagI18nResult">
        <include refid="selectDramaRoomTagI18nVo"/>
        where id = #{id}
    </select>

    <select id="selectDramaRoomTagI18nByTagIds" resultMap="DramaRoomTagI18nResult">
        <include refid="selectDramaRoomTagI18nVo"/>
        where tag_id in
        <foreach item="tagId" collection="tagIds" open="(" separator="," close=")">
            #{tagId}
        </foreach>
    </select>
    <select id="selectDramaRoomTagI18nByTagIdsAndLanguageCode" resultMap="DramaRoomTagI18nResult">
        <include refid="selectDramaRoomTagI18nVo"/>
        where  tag_id in
        <foreach item="tagId" collection="tagIds" open="(" separator="," close=")">
            #{tagId}
        </foreach>
        <if test="languageCode != null and languageCode != ''">and language_code = #{languageCode}</if>
    </select>

    <insert id="insertDramaRoomTagI18n" parameterType="DramaRoomTagI18n" useGeneratedKeys="true" keyProperty="id">
        insert into drama_room_tag_i18n
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="tagId != null">tag_id,</if>
            <if test="tagName != null and tagName != ''">tag_name,</if>
            <if test="languageCode != null and languageCode != ''">language_code,</if>
            <if test="remark != null">remark,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="tagId != null">#{tagId},</if>
            <if test="tagName != null and tagName != ''">#{tagName},</if>
            <if test="languageCode != null and languageCode != ''">#{languageCode},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <insert id="insertDramaRoomTagI18nList">
        insert into drama_room_tag_i18n (tag_id, tag_name, language_code, remark, create_by, create_time, update_by,
        update_time) values
        <foreach item="i18n" collection="list" separator=",">
            (#{i18n.tagId}, #{i18n.tagName}, #{i18n.languageCode}, #{i18n.remark}, #{i18n.createBy}, #{i18n.createTime},
            #{i18n.updateBy}, #{i18n.updateTime})
        </foreach>
    </insert>

    <update id="updateDramaRoomTagI18n" parameterType="DramaRoomTagI18n">
        update drama_room_tag_i18n
        <trim prefix="SET" suffixOverrides=",">
            <if test="tagId != null">tag_id = #{tagId},</if>
            <if test="tagName != null and tagName != ''">tag_name = #{tagName},</if>
            <if test="languageCode != null and languageCode != ''">language_code = #{languageCode},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteDramaRoomTagI18nById" parameterType="Long">
        delete
        from drama_room_tag_i18n
        where id = #{id}
    </delete>

    <delete id="deleteDramaRoomTagI18nByIds" parameterType="String">
        delete from drama_room_tag_i18n where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    <delete id="deleteDramaRoomTagI18nByTagIds">
        delete from drama_room_tag_i18n where tag_id in
        <foreach item="tagId" collection="tagIds" open="(" separator="," close=")">
            #{tagId}
        </foreach>
    </delete>
    <delete id="deleteDramaRoomTagI18nByTagId">
        delete from drama_room_tag_i18n where tag_id = #{tagId}
    </delete>
</mapper>