<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.drama.mapper.AppWebPageConfigMapper">
    
    <resultMap type="AppWebPageConfig" id="AppWebPageConfigResult">
        <result property="id"    column="id"    />
        <result property="pageKey"    column="page_key"    />
        <result property="pageCname"    column="page_cname"    />
        <result property="pageEname"    column="page_ename"    />
        <result property="pageLinkUrl"    column="page_link_url"    />
        <result property="isActive"    column="is_active"    />
        <result property="createTime"    column="create_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectAppWebPageConfigVo">
        select id, page_key, page_cname, page_ename, page_link_url, is_active, create_time, create_by, update_time, update_by, remark from app_web_page_config
    </sql>

    <select id="selectAppWebPageConfigList" parameterType="AppWebPageConfig" resultMap="AppWebPageConfigResult">
        <include refid="selectAppWebPageConfigVo"/>
        <where>  
            <if test="pageKey != null  and pageKey != ''"> and page_key = #{pageKey}</if>
            <if test="pageCname != null  and pageCname != ''"> and page_cname like concat('%', #{pageCname}, '%')</if>
            <if test="pageEname != null  and pageEname != ''"> and page_ename like concat('%', #{pageEname}, '%')</if>
            <if test="pageLinkUrl != null  and pageLinkUrl != ''"> and page_link_url = #{pageLinkUrl}</if>
            <if test="isActive != null "> and is_active = #{isActive}</if>
        </where>
    </select>
    
    <select id="selectAppWebPageConfigById" parameterType="Long" resultMap="AppWebPageConfigResult">
        <include refid="selectAppWebPageConfigVo"/>
        where id = #{id}
    </select>

    <insert id="insertAppWebPageConfig" parameterType="AppWebPageConfig" useGeneratedKeys="true" keyProperty="id">
        insert into app_web_page_config
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="pageKey != null and pageKey != ''">page_key,</if>
            <if test="pageCname != null and pageCname != ''">page_cname,</if>
            <if test="pageEname != null and pageEname != ''">page_ename,</if>
            <if test="pageLinkUrl != null and pageLinkUrl != ''">page_link_url,</if>
            <if test="isActive != null">is_active,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="pageKey != null and pageKey != ''">#{pageKey},</if>
            <if test="pageCname != null and pageCname != ''">#{pageCname},</if>
            <if test="pageEname != null and pageEname != ''">#{pageEname},</if>
            <if test="pageLinkUrl != null and pageLinkUrl != ''">#{pageLinkUrl},</if>
            <if test="isActive != null">#{isActive},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateAppWebPageConfig" parameterType="AppWebPageConfig">
        update app_web_page_config
        <trim prefix="SET" suffixOverrides=",">
            <if test="pageKey != null and pageKey != ''">page_key = #{pageKey},</if>
            <if test="pageCname != null and pageCname != ''">page_cname = #{pageCname},</if>
            <if test="pageEname != null and pageEname != ''">page_ename = #{pageEname},</if>
            <if test="pageLinkUrl != null and pageLinkUrl != ''">page_link_url = #{pageLinkUrl},</if>
            <if test="isActive != null">is_active = #{isActive},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteAppWebPageConfigById" parameterType="Long">
        delete from app_web_page_config where id = #{id}
    </delete>

    <delete id="deleteAppWebPageConfigByIds" parameterType="String">
        delete from app_web_page_config where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>