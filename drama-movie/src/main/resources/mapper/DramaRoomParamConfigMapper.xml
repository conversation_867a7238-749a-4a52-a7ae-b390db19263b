<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.drama.mapper.DramaRoomParamConfigMapper">

    <resultMap type="DramaRoomParamConfig" id="DramaRoomParamConfigResult">
        <result property="id"    column="id"    />
        <result property="paramKey"    column="param_key"    />
        <result property="paramValue"    column="param_value"    />
        <result property="remark"    column="remark"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectDramaRoomParamConfigVo">
        select id, param_key, param_value, remark, create_by, create_time, update_by, update_time from drama_room_param_config
    </sql>

    <select id="selectDramaRoomParamConfigList" parameterType="DramaRoomParamConfig" resultMap="DramaRoomParamConfigResult">
        <include refid="selectDramaRoomParamConfigVo"/>
        <where>
            <if test="paramKey != null  and paramKey != ''"> and param_key = #{paramKey}</if>
            <if test="paramValue != null  and paramValue != ''"> and param_value = #{paramValue}</if>
        </where>
    </select>

    <select id="selectDramaRoomParamConfigById" parameterType="Long" resultMap="DramaRoomParamConfigResult">
        <include refid="selectDramaRoomParamConfigVo"/>
        where id = #{id}
    </select>
    <select id="selectDramaRoomParamConfigByKey" resultType="java.lang.String">
        select param_value from drama_room_param_config where param_key = #{paramKey}
    </select>

    <insert id="insertDramaRoomParamConfig" parameterType="DramaRoomParamConfig">
        insert into drama_room_param_config
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="paramKey != null and paramKey != ''">param_key,</if>
            <if test="paramValue != null and paramValue != ''">param_value,</if>
            <if test="remark != null">remark,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="paramKey != null and paramKey != ''">#{paramKey},</if>
            <if test="paramValue != null and paramValue != ''">#{paramValue},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updateDramaRoomParamConfig" parameterType="DramaRoomParamConfig">
        update drama_room_param_config
        <trim prefix="SET" suffixOverrides=",">
            <if test="paramKey != null and paramKey != ''">param_key = #{paramKey},</if>
            <if test="paramValue != null and paramValue != ''">param_value = #{paramValue},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteDramaRoomParamConfigById" parameterType="Long">
        delete from drama_room_param_config where id = #{id}
    </delete>

    <delete id="deleteDramaRoomParamConfigByIds" parameterType="String">
        delete from drama_room_param_config where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
