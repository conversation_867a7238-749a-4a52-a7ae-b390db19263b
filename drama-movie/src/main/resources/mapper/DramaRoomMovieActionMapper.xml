<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.drama.mapper.DramaRoomMovieActionMapper">

    <resultMap type="DramaRoomMovieAction" id="DramaRoomMovieActionResult">
        <result property="id"    column="id"    />
        <result property="playCount"    column="play_count"    />
        <result property="collectCount"    column="collect_count"    />
        <result property="likeCount"    column="like_count"    />
        <result property="searchCount"    column="search_count"    />
    </resultMap>

    <insert id="insertDramaRoomMovieAction" parameterType="DramaRoomMovieAction" useGeneratedKeys="true" keyProperty="id">
        insert into drama_room_movie_action
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="playCount != null">play_count,</if>
            <if test="collectCount != null">collect_count,</if>
            <if test="likeCount != null">like_count,</if>
            <if test="searchCount != null">search_count,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="playCount != null">#{playCount},</if>
            <if test="collectCount != null">#{collectCount},</if>
            <if test="likeCount != null">#{likeCount},</if>
            <if test="searchCount != null">#{searchCount},</if>
        </trim>
    </insert>
    <update id="updateDramaRoomMovieAction">
        update drama_room_movie_action
        <set>
            <if test="playCount != null">play_count = #{playCount},</if>
            <if test="collectCount != null">collect_count = #{collectCount},</if>
            <if test="likeCount != null">like_count = #{likeCount},</if>
            <if test="searchCount != null">search_count = #{searchCount},</if>
        </set>
        where id = #{id}
    </update>
    <select id="selectByMovieId" resultMap="DramaRoomMovieActionResult">
        select * from drama_room_movie_action where id = #{movieId}
    </select>
</mapper>