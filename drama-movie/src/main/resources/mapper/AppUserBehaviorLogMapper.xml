<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.drama.mapper.AppUserBehaviorLogMapper">

    <resultMap type="AppUserBehaviorLog" id="AppUserBehaviorLogResult">
        <result property="id" column="id"/>
        <result property="userId" column="user_id"/>
        <result property="behaviorType" column="behavior_type"/>
        <result property="stayTime" column="stay_time"/>
        <result property="pageUrl" column="page_url"/>
        <result property="deviceType" column="device_type"/>
        <result property="deviceOs" column="device_os"/>
        <result property="deviceBrand" column="device_brand"/>
        <result property="actionDetail" column="action_detail"/>
        <result property="behaviorDesc" column="behavior_desc"/>
        <result property="behaviorTime" column="behavior_time"/>
        <result property="remark" column="remark"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="selectAppUserBehaviorLogVo">
        select id, user_id, behavior_type, stay_time, page_url, device_type, device_os, device_brand,
               action_detail, behavior_desc, behavior_time, remark, create_by, create_time, update_by, update_time
        from app_user_behavior_log
    </sql>

    <select id="selectAppUserBehaviorLogList" parameterType="AppUserBehaviorLog" resultMap="AppUserBehaviorLogResult">
        <include refid="selectAppUserBehaviorLogVo"/>
        <where>
            <if test="userId != null"> and user_id = #{userId}</if>
            <if test="behaviorType != null and behaviorType != ''"> and behavior_type = #{behaviorType}</if>
            <if test="stayTime != null"> and stay_time = #{stayTime}</if>
            <if test="pageUrl != null"> and page_url = #{pageUrl}</if>
            <if test="deviceType != null"> and device_type = #{deviceType}</if>
            <if test="deviceOs != null"> and device_os = #{deviceOs}</if>
            <if test="deviceBrand != null"> and device_brand = #{deviceBrand}</if>
            <if test="actionDetail != null and actionDetail != ''"> and action_detail = #{actionDetail}</if>
            <if test="behaviorDesc != null and behaviorDesc != ''"> and behavior_desc = #{behaviorDesc}</if>
            <if test="behaviorTime != null"> and behavior_time = #{behaviorTime}</if>
        </where>
        order by id desc
    </select>

    <select id="selectAppUserBehaviorLogById" parameterType="Long" resultMap="AppUserBehaviorLogResult">
        <include refid="selectAppUserBehaviorLogVo"/>
        where id = #{id}
    </select>
    <select id="getSumMinutes" resultType="java.lang.Integer">
        SELECT
            SUM(stay_time) / (1000 * 60)
        FROM
            app_user_behavior_log
        WHERE
            behavior_type = #{type}
          and user_id =#{userId}
          AND DATE(create_time) = CURRENT_DATE()
    </select>

    <insert id="insertAppUserBehaviorLog" parameterType="AppUserBehaviorLog" useGeneratedKeys="true" keyProperty="id">
        insert into app_user_behavior_log
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">user_id,</if>
            <if test="behaviorType != null">behavior_type,</if>
            <if test="stayTime != null">stay_time,</if>
            <if test="pageUrl != null">page_url,</if>
            <if test="deviceType != null">device_type,</if>
            <if test="deviceOs != null">device_os,</if>
            <if test="deviceBrand != null">device_brand,</if>
            <if test="actionDetail != null">action_detail,</if>
            <if test="behaviorDesc != null">behavior_desc,</if>
            <if test="behaviorTime != null">behavior_time,</if>
            <if test="remark != null">remark,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">#{userId},</if>
            <if test="behaviorType != null">#{behaviorType},</if>
            <if test="stayTime != null">#{stayTime},</if>
            <if test="pageUrl != null">#{pageUrl},</if>
            <if test="deviceType != null">#{deviceType},</if>
            <if test="deviceOs != null">#{deviceOs},</if>
            <if test="deviceBrand != null">#{deviceBrand},</if>
            <if test="actionDetail != null">#{actionDetail},</if>
            <if test="behaviorDesc != null">#{behaviorDesc},</if>
            <if test="behaviorTime != null">#{behaviorTime},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updateAppUserBehaviorLog" parameterType="AppUserBehaviorLog">
        update app_user_behavior_log
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="behaviorType != null">behavior_type = #{behaviorType},</if>
            <if test="stayTime != null">stay_time = #{stayTime},</if>
            <if test="pageUrl != null">page_url = #{pageUrl},</if>
            <if test="deviceType != null">device_type = #{deviceType},</if>
            <if test="deviceOs != null">device_os = #{deviceOs},</if>
            <if test="deviceBrand != null">device_brand = #{deviceBrand},</if>
            <if test="actionDetail != null">action_detail = #{actionDetail},</if>
            <if test="behaviorDesc != null">behavior_desc = #{behaviorDesc},</if>
            <if test="behaviorTime != null">behavior_time = #{behaviorTime},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteAppUserBehaviorLogById" parameterType="Long">
        delete from app_user_behavior_log where id = #{id}
    </delete>

    <delete id="deleteAppUserBehaviorLogByIds" parameterType="String">
        delete from app_user_behavior_log where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
