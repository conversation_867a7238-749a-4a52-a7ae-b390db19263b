<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.drama.mapper.DramaRoomVipMapper">

    <resultMap type="DramaRoomVip" id="DramaRoomVipResult">
        <result property="id"    column="id"    />
        <result property="payTemplateId"    column="pay_template_id"    />
        <result property="payTemplateAmountId"    column="pay_template_amount_id"    />
        <result property="payTemplateSubscriptionId"    column="pay_template_subscription_id"    />
        <result property="name"    column="name"    />
        <result property="sendCoin"    column="send_coin"    />
        <result property="payType"    column="pay_type"    />
        <result property="subscriptionType"    column="subscription_type"    />
        <result property="sort"    column="sort"    />
        <result property="status"    column="status"    />
        <result property="payPanel"    column="pay_panel"    />
        <result property="defaultRecommend"    column="default_recommend"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
        <result property="payTemplateName" column="pay_template_name" />
        <result property="price"    column="price"    />
        <result property="coin"    column="coin"    />
    </resultMap>

    <sql id="selectDramaRoomVipVo">
        select id, pay_template_id, pay_template_amount_id, pay_template_subscription_id, name, send_coin, pay_type, subscription_type, sort, status, pay_panel, default_recommend, create_by, create_time, update_by, update_time, remark from drama_room_vip
    </sql>

    <select id="selectDramaRoomVipList" parameterType="DramaRoomVip" resultMap="DramaRoomVipResult">
        <include refid="selectDramaRoomVipVo"/>
        <where>
            <if test="payTemplateId != null "> and pay_template_id = #{payTemplateId}</if>
            <if test="payTemplateAmountId != null "> and pay_template_amount_id = #{payTemplateAmountId}</if>
             <if test="payTemplateAmountId != null "> and pay_template_amount_id = #{payTemplateAmountId}</if>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="sendCoin != null "> and send_coin = #{sendCoin}</if>
            <if test="payType != null  and payType != ''"> and pay_type = #{payType}</if>
            <if test="subscriptionType != null  and subscriptionType != ''"> and subscription_type = #{subscriptionType}</if>
            <if test="sort != null "> and sort = #{sort}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="payPanel != null  and payPanel != ''"> and pay_panel = #{payPanel}</if>
            <if test="defaultRecommend != null "> and default_recommend = #{defaultRecommend}</if>
        </where>
    </select>

    <select id="selectDramaRoomVipById" parameterType="Long" resultMap="DramaRoomVipResult">
        <include refid="selectDramaRoomVipVo"/>
        where id = #{id}
    </select>

    <select id="queryAllDramaRoomVip" resultMap="DramaRoomVipResult">
        SELECT
        t1.id,
        t1.pay_type,
        t1.subscription_type,
        t1.`name`,
        t1.status,
        t1.default_recommend,
        t2.`name` pay_template_name,
        t2.id pay_template_id,
        t3.id pay_template_amount_id,
        t3.id pay_template_subscription_id,
        t2.`name` pay_template_amount_name,
        t3.price,
        t3.coin,
        t1.send_coin,
        t1.pay_panel,
        t1.sort,
        t1.create_time create_time,
        t1.update_time update_time
        FROM drama_room_vip t1
        LEFT JOIN drama_room_pay_template t2 ON t1.pay_template_id=t2.id
        LEFT JOIN drama_room_pay_template_amt t3 ON t1.pay_template_amount_id=t3.id
        <where>
            <if test="payTemplateId != null "> and t1.pay_template_id = #{payTemplateId}</if>
            <if test="payTemplateAmountId != null "> and t1.pay_template_amount_id = #{payTemplateAmountId}</if>
            <if test="payTemplateSubscriptionId != null "> and t1.pay_template_subscription_id = #{payTemplateSubscriptionId}</if>
            <if test="name != null  and name != ''"> and t2.name like concat('%', #{name}, '%')</if>
            <if test="sendCoin != null "> and t1.send_coin = #{sendCoin}</if>
            <if test="payType != null  and payType != ''"> and t1.pay_type = #{payType}</if>
            <if test="price != null  and price != ''"> and t3.price = #{price}</if>
            <if test="subscriptionType != null  and subscriptionType != ''"> and t1.subscription_type = #{subscriptionType}</if>
            <if test="sort != null "> and t1.sort = #{sort}</if>
            <if test="createBy != null "> and t1.create_by = #{createBy}</if>
            <if test="status != null  and status != ''"> and t1.status = #{status}</if>
            <if test="payPanel != null  and payPanel != ''"> and t1.pay_panel = #{payPanel}</if>
        </where>
        ORDER BY t1.id DESC
    </select>

    <insert id="insertDramaRoomVip" parameterType="DramaRoomVip" useGeneratedKeys="true" keyProperty="id">
        insert into drama_room_vip
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="payTemplateId != null">pay_template_id,</if>
            <if test="payTemplateAmountId != null">pay_template_amount_id,</if>
            <if test="payTemplateSubscriptionId != null">pay_template_subscription_id,</if>
            <if test="name != null">name,</if>
            <if test="sendCoin != null">send_coin,</if>
            <if test="payType != null">pay_type,</if>
            <if test="subscriptionType != null">subscription_type,</if>
            <if test="sort != null">sort,</if>
            <if test="status != null">status,</if>
            <if test="payPanel != null">pay_panel,</if>
            <if test="defaultRecommend != null">default_recommend,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="payTemplateId != null">#{payTemplateId},</if>
            <if test="payTemplateAmountId != null">#{payTemplateAmountId},</if>
            <if test="payTemplateSubscriptionId != null">#{payTemplateSubscriptionId},</if>
            <if test="name != null">#{name},</if>
            <if test="sendCoin != null">#{sendCoin},</if>
            <if test="payType != null">#{payType},</if>
            <if test="subscriptionType != null">#{subscriptionType},</if>
            <if test="sort != null">#{sort},</if>
            <if test="status != null">#{status},</if>
            <if test="payPanel != null">#{payPanel},</if>
            <if test="defaultRecommend != null">#{defaultRecommend},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
    </insert>

    <update id="updateDramaRoomVip" parameterType="DramaRoomVip">
        update drama_room_vip
        <trim prefix="SET" suffixOverrides=",">
            <if test="payTemplateId != null">pay_template_id = #{payTemplateId},</if>
            <if test="payTemplateAmountId != null">pay_template_amount_id = #{payTemplateAmountId},</if>
            <if test="payTemplateSubscriptionId != null">pay_template_subscription_id = #{payTemplateSubscriptionId},</if>
            <if test="name != null">name = #{name},</if>
            <if test="sendCoin != null">send_coin = #{sendCoin},</if>
            <if test="payType != null">pay_type = #{payType},</if>
            <if test="subscriptionType != null">subscription_type = #{subscriptionType},</if>
            <if test="sort != null">sort = #{sort},</if>
            <if test="status != null">status = #{status},</if>
            <if test="payPanel != null">pay_panel = #{payPanel},</if>
            <if test="defaultRecommend != null">default_recommend = #{defaultRecommend},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>
    <update id="updateDefaultRecommendById">
        update drama_room_vip set default_recommend = 0 where id in
        <foreach item="id" collection="list" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <delete id="deleteDramaRoomVipById" parameterType="Long">
        delete from drama_room_vip where id = #{id}
    </delete>

    <delete id="deleteDramaRoomVipByIds" parameterType="String">
        delete from drama_room_vip where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
