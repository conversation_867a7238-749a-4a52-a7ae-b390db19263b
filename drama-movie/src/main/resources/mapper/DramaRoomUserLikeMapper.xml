<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.drama.mapper.DramaRoomUserLikeMapper">

    <resultMap type="DramaRoomUserLike" id="DramaRoomUserLikeResult">
        <result property="id"    column="id"    />
        <result property="userId"    column="user_id"    />
        <result property="movieId"    column="movie_id"    />
        <result property="videoId"    column="video_id"    />
        <result property="status"    column="status"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectDramaRoomUserLikeVo">
        select id, user_id, movie_id, video_id, status, create_time, update_time from drama_room_user_like
    </sql>

    <select id="selectDramaRoomUserLikeList" parameterType="DramaRoomUserLike" resultMap="DramaRoomUserLikeResult">
        <include refid="selectDramaRoomUserLikeVo"/>
        <where>
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="movieId != null "> and movie_id = #{movieId}</if>
            <if test="videoId != null "> and video_id = #{videoId}</if>
            <if test="status != null "> and status = #{status}</if>
        </where>
    </select>

    <select id="selectDramaRoomUserLikeById" parameterType="Long" resultMap="DramaRoomUserLikeResult">
        <include refid="selectDramaRoomUserLikeVo"/>
        where id = #{id}
    </select>

    <insert id="insertDramaRoomUserLike" parameterType="DramaRoomUserLike" useGeneratedKeys="true" keyProperty="id">
        insert into drama_room_user_like
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">user_id,</if>
            <if test="movieId != null">movie_id,</if>
            <if test="videoId != null">video_id,</if>
            <if test="status != null">status,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">#{userId},</if>
            <if test="movieId != null">#{movieId},</if>
            <if test="videoId != null">#{videoId},</if>
            <if test="status != null">#{status},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updateDramaRoomUserLike" parameterType="DramaRoomUserLike">
        update drama_room_user_like
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="movieId != null">movie_id = #{movieId},</if>
            <if test="videoId != null">video_id = #{videoId},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteDramaRoomUserLikeById" parameterType="Long">
        delete from drama_room_user_like where id = #{id}
    </delete>

    <delete id="deleteDramaRoomUserLikeByIds" parameterType="String">
        delete from drama_room_user_like where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectUserLikesByVideoIds" resultMap="DramaRoomUserLikeResult">
        <include refid="selectDramaRoomUserLikeVo"/>
        where user_id = #{userId} and video_id in
        <foreach item="videoId" collection="videoIds" open="(" separator="," close=")">
            #{videoId}
        </foreach>
        and status = 1
    </select>
</mapper>
