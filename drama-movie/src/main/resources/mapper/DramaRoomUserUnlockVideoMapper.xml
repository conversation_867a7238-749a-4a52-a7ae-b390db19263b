<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.drama.mapper.DramaRoomUserUnlockVideoMapper">

    <resultMap type="DramaRoomUserUnlockVideo" id="DramaRoomUserUnlockVideoResult">
        <result property="id"           column="id"             />
        <result property="userId"       column="user_id"        />
        <result property="movieId"      column="movie_id"       />
        <result property="videoId"      column="video_id"       />
        <result property="unlockType"   column="unlock_type"    />
        <result property="costCoin"     column="cost_coin"      />
        <result property="createTime"   column="create_time"    />
        <result property="updateTime"   column="update_time"    />
        <result property="createBy"     column="create_by"      />
        <result property="updateBy"     column="update_by"      />
        <result property="remark"       column="remark"         />
    </resultMap>

    <sql id="selectDramaRoomUserUnlockVideoVo">
        select id, user_id, movie_id, video_id, unlock_type, cost_coin, create_time, update_time, create_by, update_by, remark from drama_room_user_unlock_video
    </sql>

    <select id="selectDramaRoomUserUnlockVideoList" parameterType="DramaRoomUserUnlockVideo" resultMap="DramaRoomUserUnlockVideoResult">
        <include refid="selectDramaRoomUserUnlockVideoVo"/>
        <where>
            <if test="userId != null"> and user_id = #{userId}</if>
            <if test="movieId != null"> and movie_id = #{movieId}</if>
            <if test="videoId != null"> and video_id = #{videoId}</if>
            <if test="unlockType != null"> and unlock_type = #{unlockType}</if>
            <if test="costCoin != null"> and cost_coin = #{costCoin}</if>
        </where>
        order by create_time desc
    </select>

    <select id="selectDramaRoomUserUnlockVideoById" parameterType="Long" resultMap="DramaRoomUserUnlockVideoResult">
        <include refid="selectDramaRoomUserUnlockVideoVo"/>
        where id = #{id}
    </select>

    <select id="selectDramaRoomUserUnlockVideoByUserId" parameterType="Long" resultMap="DramaRoomUserUnlockVideoResult">
        <include refid="selectDramaRoomUserUnlockVideoVo"/>
        where user_id = #{userId}
        order by create_time desc
    </select>

    <select id="selectDramaRoomUserUnlockVideoByUserIdAndMovieId" resultMap="DramaRoomUserUnlockVideoResult">
        <include refid="selectDramaRoomUserUnlockVideoVo"/>
        where user_id = #{userId} and movie_id = #{movieId}
        order by create_time desc
    </select>

    <select id="selectByUserIdAndMovieIdAndVideoId" resultMap="DramaRoomUserUnlockVideoResult">
        <include refid="selectDramaRoomUserUnlockVideoVo"/>
        where movie_id = #{movieId} and video_id = #{videoId} and user_id = #{userId}
        limit 1
    </select>

    <select id="unlockEpisodeRecords" resultType="com.ruoyi.common.drama.vo.UnlockEpisodeRecordsVO">
        SELECT
            druu.id,
            druu.video_id AS videoId,
            drv.video_num AS episodeNum,
            drm.title AS videoName,
            druu.create_time AS unlockTime,
            druu.unlock_type AS unlockType,
            druu.cost_coin AS unlockCoin
        FROM
            drama_room_user_unlock_video druu
                LEFT JOIN drama_room_video drv ON druu.video_id = drv.id
                LEFT JOIN drama_room_movie drm ON druu.movie_id = drm.id
        WHERE
            druu.user_id = #{userId}
        ORDER BY
            druu.create_time DESC
    </select>

    <select id="selectDramaRoomUserUnlockVideoByUserIdAndVideoId" resultMap="DramaRoomUserUnlockVideoResult">
        <include refid="selectDramaRoomUserUnlockVideoVo"/>
        where user_id = #{userId} and video_id = #{videoId}
        limit 1
    </select>

    <select id="countUnlockVideosByUserId" parameterType="Long" resultType="int">
        select count(1) from drama_room_user_unlock_video
        where user_id = #{userId}
    </select>

    <select id="countUnlockVideosByUserIdAndMovieId" resultType="int">
        select count(1) from drama_room_user_unlock_video
        where user_id = #{userId} and movie_id = #{movieId}
    </select>

    <insert id="insertDramaRoomUserUnlockVideo" parameterType="DramaRoomUserUnlockVideo">
        insert into drama_room_user_unlock_video
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="userId != null">user_id,</if>
            <if test="movieId != null">movie_id,</if>
            <if test="videoId != null">video_id,</if>
            <if test="unlockType != null">unlock_type,</if>
            <if test="costCoin != null">cost_coin,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="remark != null">remark,</if>
            create_time,
            update_time
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="userId != null">#{userId},</if>
            <if test="movieId != null">#{movieId},</if>
            <if test="videoId != null">#{videoId},</if>
            <if test="unlockType != null">#{unlockType},</if>
            <if test="costCoin != null">#{costCoin},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="remark != null">#{remark},</if>
            CURRENT_TIMESTAMP(3),
            CURRENT_TIMESTAMP(3)
        </trim>
    </insert>

    <update id="updateDramaRoomUserUnlockVideo" parameterType="DramaRoomUserUnlockVideo">
        update drama_room_user_unlock_video
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="movieId != null">movie_id = #{movieId},</if>
            <if test="videoId != null">video_id = #{videoId},</if>
            <if test="unlockType != null">unlock_type = #{unlockType},</if>
            <if test="costCoin != null">cost_coin = #{costCoin},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="remark != null">remark = #{remark},</if>
            update_time = CURRENT_TIMESTAMP(3)
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteDramaRoomUserUnlockVideoById" parameterType="Long">
        delete from drama_room_user_unlock_video where id = #{id}
    </delete>

    <delete id="deleteDramaRoomUserUnlockVideoByIds" parameterType="String">
        delete from drama_room_user_unlock_video where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteDramaRoomUserUnlockVideoByUserId" parameterType="Long">
        delete from drama_room_user_unlock_video where user_id = #{userId}
    </delete>

    <delete id="deleteDramaRoomUserUnlockVideoByMovieId" parameterType="Long">
        delete from drama_room_user_unlock_video where movie_id = #{movieId}
    </delete>

    <delete id="deleteDramaRoomUserUnlockVideoByVideoId" parameterType="Long">
        delete from drama_room_user_unlock_video where video_id = #{videoId}
    </delete>

</mapper>