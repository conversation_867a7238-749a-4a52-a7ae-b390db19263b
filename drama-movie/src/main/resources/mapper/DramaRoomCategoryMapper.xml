<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.drama.mapper.DramaRoomCategoryMapper">

    <resultMap type="DramaRoomCategory" id="DramaRoomCategoryResult">
        <result property="id"    column="id"    />
        <result property="categoryName"    column="category_name"    />
        <result property="sort"    column="sort"    />
        <result property="status"    column="status"    />
        <result property="remark"    column="remark"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectDramaRoomCategoryVo">
        select id, category_name, sort, status, remark, create_by, create_time, update_by, update_time from drama_room_category
    </sql>

    <select id="selectDramaRoomCategoryList" parameterType="DramaRoomCategory" resultMap="DramaRoomCategoryResult">
        <include refid="selectDramaRoomCategoryVo"/>
        <where>
            <if test="categoryName != null  and categoryName != ''"> and category_name like concat('%', #{categoryName}, '%')</if>
            <if test="sort != null "> and sort = #{sort}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
        </where>
    </select>

    <select id="selectDramaRoomCategoryById" parameterType="Long" resultMap="DramaRoomCategoryResult">
        <include refid="selectDramaRoomCategoryVo"/>
        where id = #{id} AND status = '1'
    </select>

    <select id="selectDramaRoomCategoryByIds" resultMap="DramaRoomCategoryResult">
        <include refid="selectDramaRoomCategoryVo"/>
        where id in
        <foreach item="id" collection="array" separator="," open="(" close=")">
            #{id}
        </foreach>
        AND status = '1'
    </select>

    <insert id="insertDramaRoomCategory" parameterType="DramaRoomCategory" useGeneratedKeys="true" keyProperty="id">
        insert into drama_room_category
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="categoryName != null and categoryName != ''">category_name,</if>
            <if test="sort != null">sort,</if>
            <if test="status != null">status,</if>
            <if test="remark != null">remark,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="categoryName != null and categoryName != ''">#{categoryName},</if>
            <if test="sort != null">#{sort},</if>
            <if test="status != null">#{status},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updateDramaRoomCategory" parameterType="DramaRoomCategory">
        update drama_room_category
        <trim prefix="SET" suffixOverrides=",">
            <if test="categoryName != null and categoryName != ''">category_name = #{categoryName},</if>
            <if test="sort != null">sort = #{sort},</if>
            <if test="status != null">status = #{status},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteDramaRoomCategoryById" parameterType="Long">
        delete from drama_room_category where id = #{id}
    </delete>

    <delete id="deleteDramaRoomCategoryByIds" parameterType="String">
        delete from drama_room_category where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
