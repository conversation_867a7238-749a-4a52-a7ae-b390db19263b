<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.drama.mapper.SystemErrorLogMapper">

    <resultMap type="SystemErrorLog" id="SystemErrorLogResult">
        <result property="id"    column="id"    />
        <result property="traceId"    column="trace_id"    />
        <result property="errorCode"    column="error_code"    />
        <result property="errorName"    column="error_name"    />
        <result property="errorMessage"    column="error_message"    />
        <result property="errorStack"    column="error_stack"    />
        <result property="requestUrl"    column="request_url"    />
        <result property="requestMethod"    column="request_method"    />
        <result property="requestParams"    column="request_params"    />
        <result property="ipAddress"    column="ip_address"    />
        <result property="deviceInfo"    column="device_info"    />
        <result property="userAgent"    column="user_agent"    />
        <result property="userId"    column="user_id"    />
        <result property="createTime"    column="create_time"    />
        <result property="status"    column="status"    />
        <result property="systemType" column="system_type" />
    </resultMap>

    <sql id="selectSystemErrorLogVo">
        select id, trace_id, error_code, error_name, error_message, error_stack, request_url, request_method, request_params, ip_address, device_info, user_agent, user_id, create_time, status,system_type from system_error_log
    </sql>

    <select id="selectSystemErrorLogList" parameterType="SystemErrorLog" resultMap="SystemErrorLogResult">
        <include refid="selectSystemErrorLogVo"/>
        <where>
            <if test="traceId != null  and traceId != ''"> and trace_id = #{traceId}</if>
            <if test="errorCode != null  and errorCode != ''"> and error_code = #{errorCode}</if>
            <if test="errorName != null  and errorName != ''"> and error_name like concat('%', #{errorName}, '%')</if>
            <if test="errorMessage != null  and errorMessage != ''"> and error_message = #{errorMessage}</if>
            <if test="errorStack != null  and errorStack != ''"> and error_stack = #{errorStack}</if>
            <if test="requestUrl != null  and requestUrl != ''"> and request_url = #{requestUrl}</if>
            <if test="requestMethod != null  and requestMethod != ''"> and request_method = #{requestMethod}</if>
            <if test="requestParams != null  and requestParams != ''"> and request_params = #{requestParams}</if>
            <if test="ipAddress != null  and ipAddress != ''"> and ip_address = #{ipAddress}</if>
            <if test="deviceInfo != null  and deviceInfo != ''"> and device_info = #{deviceInfo}</if>
            <if test="userAgent != null  and userAgent != ''"> and user_agent = #{userAgent}</if>
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="status != null "> and status = #{status}</if>
            <if test="systemType != null  and systemType != ''"> and system_type = #{systemType}</if>
        </where>
    </select>

    <select id="selectSystemErrorLogById" parameterType="Long" resultMap="SystemErrorLogResult">
        <include refid="selectSystemErrorLogVo"/>
        where id = #{id}
    </select>

    <insert id="insertSystemErrorLog" parameterType="SystemErrorLog" useGeneratedKeys="true" keyProperty="id">
        insert into system_error_log
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="traceId != null">trace_id,</if>
            <if test="errorCode != null">error_code,</if>
            <if test="errorName != null">error_name,</if>
            <if test="errorMessage != null">error_message,</if>
            <if test="errorStack != null">error_stack,</if>
            <if test="requestUrl != null">request_url,</if>
            <if test="requestMethod != null">request_method,</if>
            <if test="requestParams != null">request_params,</if>
            <if test="ipAddress != null">ip_address,</if>
            <if test="deviceInfo != null">device_info,</if>
            <if test="userAgent != null">user_agent,</if>
            <if test="userId != null">user_id,</if>
            <if test="createTime != null">create_time,</if>
            <if test="status != null">status,</if>
            <if test="systemType != null">system_type</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="traceId != null">#{traceId},</if>
            <if test="errorCode != null">#{errorCode},</if>
            <if test="errorName != null">#{errorName},</if>
            <if test="errorMessage != null">#{errorMessage},</if>
            <if test="errorStack != null">#{errorStack},</if>
            <if test="requestUrl != null">#{requestUrl},</if>
            <if test="requestMethod != null">#{requestMethod},</if>
            <if test="requestParams != null">#{requestParams},</if>
            <if test="ipAddress != null">#{ipAddress},</if>
            <if test="deviceInfo != null">#{deviceInfo},</if>
            <if test="userAgent != null">#{userAgent},</if>
            <if test="userId != null">#{userId},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="status != null">#{status},</if>
            <if test="systemType != null">#{systemType}</if>
        </trim>
    </insert>

    <update id="updateSystemErrorLog" parameterType="SystemErrorLog">
        update system_error_log
        <trim prefix="SET" suffixOverrides=",">
            <if test="traceId != null">trace_id = #{traceId},</if>
            <if test="errorCode != null">error_code = #{errorCode},</if>
            <if test="errorName != null">error_name = #{errorName},</if>
            <if test="errorMessage != null">error_message = #{errorMessage},</if>
            <if test="errorStack != null">error_stack = #{errorStack},</if>
            <if test="requestUrl != null">request_url = #{requestUrl},</if>
            <if test="requestMethod != null">request_method = #{requestMethod},</if>
            <if test="requestParams != null">request_params = #{requestParams},</if>
            <if test="ipAddress != null">ip_address = #{ipAddress},</if>
            <if test="deviceInfo != null">device_info = #{deviceInfo},</if>
            <if test="userAgent != null">user_agent = #{userAgent},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="status != null">status = #{status},</if>
            <if test="systemType != null">system_type = #{systemType},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteSystemErrorLogById" parameterType="Long">
        delete from system_error_log where id = #{id}
    </delete>

    <delete id="deleteSystemErrorLogByIds" parameterType="String">
        delete from system_error_log where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>