<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.drama.mapper.DramaRoomCategoryI18nMapper">

    <resultMap type="DramaRoomCategoryI18n" id="DramaRoomCategoryI18nResult">
        <result property="id"    column="id"    />
        <result property="categoryId"    column="category_id"    />
        <result property="categoryName"    column="category_name"    />
        <result property="languageCode"    column="language_code"    />
        <result property="remark"    column="remark"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectDramaRoomCategoryI18nVo">
        select id, category_id, category_name, language_code, remark, create_by, create_time, update_by, update_time from drama_room_category_i18n
    </sql>

    <select id="selectDramaRoomCategoryI18nList" parameterType="DramaRoomCategoryI18n" resultMap="DramaRoomCategoryI18nResult">
        <include refid="selectDramaRoomCategoryI18nVo"/>
        <where>
            <if test="categoryId != null "> and category_id = #{categoryId}</if>
            <if test="categoryName != null  and categoryName != ''"> and category_name like concat('%', #{categoryName}, '%')</if>
             <if test="languageCode != null and languageCode != ''"> and language_code = #{languageCode}</if>
        </where>
    </select>

    <select id="selectDramaRoomCategoryI18nById" parameterType="Long" resultMap="DramaRoomCategoryI18nResult">
        <include refid="selectDramaRoomCategoryI18nVo"/>
        where id = #{id}
    </select>
    <select id="selectByCategoryIdListAndLanguageCode" resultMap="DramaRoomCategoryI18nResult">
        <include refid="selectDramaRoomCategoryI18nVo"/>
        where category_id in
        <foreach item="item" collection="list" separator="," open="(" close=")">
            #{item}
        </foreach>
        and language_code = #{languageCode}
    </select>
    <select id="selectDramaRoomCategoryI18nListByCategoryIds" resultMap="DramaRoomCategoryI18nResult">
        <include refid="selectDramaRoomCategoryI18nVo"/>
        where category_id in
        <foreach item="item" collection="categoryIds" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>

    <insert id="insertDramaRoomCategoryI18n" parameterType="DramaRoomCategoryI18n" useGeneratedKeys="true" keyProperty="id">
        insert into drama_room_category_i18n
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="categoryId != null">category_id,</if>
            <if test="categoryName != null and categoryName != ''">category_name,</if>
            <if test="languageCode != null and languageCode != ''">language_code,</if>
            <if test="remark != null">remark,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="categoryId != null">#{categoryId},</if>
            <if test="categoryName != null and categoryName != ''">#{categoryName},</if>
            <if test="languageCode != null and languageCode != ''">#{languageCode},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <insert id="insertDramaRoomCategoryI18nList">
        insert into drama_room_category_i18n (category_id, category_name, language_code, remark, create_by, create_time, update_by, update_time) values
        <foreach item="item" index="index" collection="i18ns" separator=",">
            (#{item.categoryId}, #{item.categoryName}, #{item.languageCode}, #{item.remark}, #{item.createBy}, #{item.createTime}, #{item.updateBy}, #{item.updateTime})
        </foreach>
    </insert>

    <update id="updateDramaRoomCategoryI18n" parameterType="DramaRoomCategoryI18n">
        update drama_room_category_i18n
        <trim prefix="SET" suffixOverrides=",">
            <if test="categoryId != null">category_id = #{categoryId},</if>
            <if test="categoryName != null and categoryName != ''">category_name = #{categoryName},</if>
            <if test="languageCode != null and languageCode != ''">language_code = #{languageCode},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteDramaRoomCategoryI18nById" parameterType="Long">
        delete from drama_room_category_i18n where id = #{id}
    </delete>

    <delete id="deleteDramaRoomCategoryI18nByIds" parameterType="String">
        delete from drama_room_category_i18n where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    <delete id="deleteDramaRoomCategoryI18nByCategoryIds">
        delete from drama_room_category_i18n where category_id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    <delete id="deleteDramaRoomCategoryI18nByCategoryId">
        delete from drama_room_category_i18n where category_id = #{id}
    </delete>
</mapper>