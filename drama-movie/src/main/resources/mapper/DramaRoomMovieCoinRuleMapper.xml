<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.drama.mapper.DramaRoomMovieCoinRuleMapper">

    <resultMap type="DramaRoomMovieCoinRule" id="DramaRoomMovieCoinRuleResult">
        <result property="id"    column="id"    />
        <result property="name"    column="name"    />
        <result property="movieId"    column="movie_id"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
        <result property="popNum"    column="pop_num"    />
    </resultMap>

    <sql id="selectDramaRoomMovieCoinRuleVo">
        select id, name, movie_id, create_by, create_time, update_by, update_time, remark, pop_num from drama_room_movie_coin_rule
    </sql>

    <select id="selectDramaRoomMovieCoinRuleList" parameterType="DramaRoomMovieCoinRule" resultMap="DramaRoomMovieCoinRuleResult">
        <include refid="selectDramaRoomMovieCoinRuleVo"/>
        <where>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="movieId != null "> and movie_id = #{movieId}</if>
            <if test="popNum != null "> and pop_num = #{popNum}</if>
        </where>
    </select>

    <select id="selectDramaRoomMovieCoinRuleById" parameterType="Long" resultMap="DramaRoomMovieCoinRuleResult">
        <include refid="selectDramaRoomMovieCoinRuleVo"/>
        where id = #{id}
    </select>

    <select id="selectDramaRoomMovieCoinRuleByMoiveId" parameterType="Long" resultMap="DramaRoomMovieCoinRuleResult">
        <include refid="selectDramaRoomMovieCoinRuleVo"/>
        where movie_id = #{movieId}
    </select>

    <insert id="insertDramaRoomMovieCoinRule" parameterType="DramaRoomMovieCoinRule" useGeneratedKeys="true" keyProperty="id">
        insert into drama_room_movie_coin_rule
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null">name,</if>
            <if test="movieId != null">movie_id,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
            <if test="popNum != null">pop_num,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="name != null">#{name},</if>
            <if test="movieId != null">#{movieId},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
            <if test="popNum != null">#{popNum},</if>
        </trim>
    </insert>

    <update id="updateDramaRoomMovieCoinRule" parameterType="DramaRoomMovieCoinRule">
        update drama_room_movie_coin_rule
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null">name = #{name},</if>
            <if test="movieId != null">movie_id = #{movieId},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="popNum != null">pop_num = #{popNum},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteDramaRoomMovieCoinRuleById" parameterType="Long">
        delete from drama_room_movie_coin_rule where id = #{id}
    </delete>

    <delete id="deleteDramaRoomMovieCoinRuleByIds" parameterType="String">
        delete from drama_room_movie_coin_rule where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
