<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.drama.mapper.DramaRoomTagMapper">

    <resultMap type="DramaRoomTag" id="DramaRoomTagResult">
        <result property="tagId"    column="tag_id"    />
        <result property="tagName"    column="tag_name"    />
        <result property="orderNum"    column="order_num"    />
        <result property="status"    column="status"    />
        <result property="remark"    column="remark"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectDramaRoomTagVo">
        select tag_id, tag_name, order_num, status, remark, create_by, create_time, update_by, update_time from drama_room_tag
    </sql>

    <select id="selectDramaRoomTagList" parameterType="DramaRoomTag" resultMap="DramaRoomTagResult">
        <include refid="selectDramaRoomTagVo"/>
        <where>
            <if test="tagName != null  and tagName != ''"> and tag_name like concat('%', #{tagName}, '%')</if>
            <if test="orderNum != null "> and order_num = #{orderNum}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
        </where>
    </select>

    <select id="selectDramaRoomTagByTagId" parameterType="Long" resultMap="DramaRoomTagResult">
        <include refid="selectDramaRoomTagVo"/>
        where tag_id = #{tagId}
    </select>

    <select id="selectTagListByMovieId" resultMap="DramaRoomTagResult">
        select t.tag_id,
               t.tag_name,
               t.order_num,
               t.status,
               t.remark,
               t.create_by,
               t.create_time,
               t.update_by,
               t.update_time
        from drama_room_tag t
                 LEFT JOIN drama_room_movie_tag tm on tm.tag_id = t.tag_id
        WHERE tm.movie_id = #{movieId}
          AND t.status = '1'
    </select>

    <insert id="insertDramaRoomTag" parameterType="DramaRoomTag" useGeneratedKeys="true" keyProperty="tagId">
        insert into drama_room_tag
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="tagName != null and tagName != ''">tag_name,</if>
            <if test="orderNum != null">order_num,</if>
            <if test="status != null">status,</if>
            <if test="remark != null">remark,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="tagName != null and tagName != ''">#{tagName},</if>
            <if test="orderNum != null">#{orderNum},</if>
            <if test="status != null">#{status},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updateDramaRoomTag" parameterType="DramaRoomTag">
        update drama_room_tag
        <trim prefix="SET" suffixOverrides=",">
            <if test="tagName != null and tagName != ''">tag_name = #{tagName},</if>
            <if test="orderNum != null">order_num = #{orderNum},</if>
            <if test="status != null">status = #{status},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where tag_id = #{tagId}
    </update>

    <delete id="deleteDramaRoomTagByTagId" parameterType="Long">
        delete from drama_room_tag where tag_id = #{tagId}
    </delete>

    <delete id="deleteDramaRoomTagByTagIds" parameterType="String">
        delete from drama_room_tag where tag_id in
        <foreach item="tagId" collection="array" open="(" separator="," close=")">
            #{tagId}
        </foreach>
    </delete>
</mapper>
