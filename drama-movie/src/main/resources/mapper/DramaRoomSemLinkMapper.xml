<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.drama.mapper.DramaRoomSemLinkMapper">

    <resultMap type="DramaRoomSemLink" id="DramaRoomSemLinkResult">
        <result property="id"    column="id"    />
        <result property="platform"    column="platform"    />
        <result property="name"    column="name"    />
        <result property="target"    column="target"    />
        <result property="callbackType"    column="callback_type"    />
        <result property="pixelCode"    column="pixel_code"    />
        <result property="landingTemplate"    column="landing_template"    />
        <result property="movieId"    column="movie_id"    />
        <result property="payTemplateId"    column="pay_template_id"    />
        <result property="link"    column="link"    />
        <result property="system"    column="system"    />
        <result property="url"    column="url"    />
        <result property="videoCoinPlanId"    column="video_coin_plan_id"    />
        <result property="pdata"    column="pdata"    />
        <result property="linkAppId"    column="link_app_id"    />
        <result property="pixelId"    column="pixel_id"    />
        <result property="language"    column="language"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectDramaRoomSemLinkVo">
        select id, platform, name, target, callback_type, pixel_code, landing_template, movie_id, pay_template_id, link, system, url, video_coin_plan_id, pdata, link_app_id, pixel_id, language, create_by, create_time, update_by, update_time, remark from drama_room_sem_link
    </sql>

    <select id="selectDramaRoomSemLinkList" parameterType="DramaRoomSemLink" resultMap="DramaRoomSemLinkResult">
        <include refid="selectDramaRoomSemLinkVo"/>
        <where>
            <if test="platform != null  and platform != ''"> and platform = #{platform}</if>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="target != null  and target != ''"> and target = #{target}</if>
            <if test="callbackType != null  and callbackType != ''"> and callback_type = #{callbackType}</if>
            <if test="pixelCode != null  and pixelCode != ''"> and pixel_code = #{pixelCode}</if>
            <if test="landingTemplate != null  and landingTemplate != ''"> and landing_template = #{landingTemplate}</if>
            <if test="movieId != null "> and movie_id = #{movieId}</if>
            <if test="payTemplateId != null "> and pay_template_id = #{payTemplateId}</if>
            <if test="link != null  and link != ''"> and link = #{link}</if>
            <if test="system != null  and system != ''"> and system = #{system}</if>
            <if test="url != null  and url != ''"> and url = #{url}</if>
            <if test="videoCoinPlanId != null "> and video_coin_plan_id = #{videoCoinPlanId}</if>
            <if test="pdata != null  and pdata != ''"> and pdata = #{pdata}</if>
            <if test="linkAppId != null "> and link_app_id = #{linkAppId}</if>
            <if test="pixelId != null "> and pixel_id = #{pixelId}</if>
            <if test="language != null  and language != ''"> and language = #{language}</if>
        </where>
    </select>

    <select id="selectDramaRoomSemLinkById" parameterType="Long" resultMap="DramaRoomSemLinkResult">
        <include refid="selectDramaRoomSemLinkVo"/>
        where id = #{id}
    </select>
    <select id="queryDramaRoomSemLink" resultType="com.ruoyi.drama.domain.vo.DramaRoomSemLinkVO">
        select t1.*,
               t2.title as movieName,
               t3.`name` as payTemplateName,
               t4.`name` as videoCoinPlanName
        from drama_room_sem_link t1
                 left join drama_room_movie t2 on t1.movie_id = t2.id
                 left join drama_room_pay_template t3 on t1.pay_template_id = t3.id
                 left join drama_room_movie_coin_rule t4 on t1.video_coin_plan_id = t4.id
        <where>
            <if test="platform != null  and platform != ''"> and t1.platform = #{platform}</if>
            <if test="name != null  and name != ''"> and t1.name like concat('%', #{name}, '%')</if>
            <if test="landingTemplate != null  and landingTemplate != ''"> and t1.landing_template = #{landingTemplate}</if>
            <if test="movieId != null "> and t1.movie_id = #{movieId}</if>
            <if test="payTemplateId != null "> and t1.pay_template_id = #{payTemplateId}</if>
            <if test="videoCoinPlanId != null "> and t1.video_coin_plan_id = #{videoCoinPlanId}</if>
            <if test="language != null  and language != ''"> and t1.language = #{language}</if>
        </where>
    </select>

    <insert id="insertDramaRoomSemLink" parameterType="DramaRoomSemLink" useGeneratedKeys="true" keyProperty="id">
        insert into drama_room_sem_link
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="platform != null">platform,</if>
            <if test="name != null">name,</if>
            <if test="target != null">target,</if>
            <if test="callbackType != null">callback_type,</if>
            <if test="pixelCode != null">pixel_code,</if>
            <if test="landingTemplate != null">landing_template,</if>
            <if test="movieId != null">movie_id,</if>
            <if test="payTemplateId != null">pay_template_id,</if>
            <if test="link != null">link,</if>
            <if test="system != null">system,</if>
            <if test="url != null">url,</if>
            <if test="videoCoinPlanId != null">video_coin_plan_id,</if>
            <if test="pdata != null">pdata,</if>
            <if test="linkAppId != null">link_app_id,</if>
            <if test="pixelId != null">pixel_id,</if>
            <if test="language != null">language,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="platform != null">#{platform},</if>
            <if test="name != null">#{name},</if>
            <if test="target != null">#{target},</if>
            <if test="callbackType != null">#{callbackType},</if>
            <if test="pixelCode != null">#{pixelCode},</if>
            <if test="landingTemplate != null">#{landingTemplate},</if>
            <if test="movieId != null">#{movieId},</if>
            <if test="payTemplateId != null">#{payTemplateId},</if>
            <if test="link != null">#{link},</if>
            <if test="system != null">#{system},</if>
            <if test="url != null">#{url},</if>
            <if test="videoCoinPlanId != null">#{videoCoinPlanId},</if>
            <if test="pdata != null">#{pdata},</if>
            <if test="linkAppId != null">#{linkAppId},</if>
            <if test="pixelId != null">#{pixelId},</if>
            <if test="language != null">#{language},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
    </insert>

    <update id="updateDramaRoomSemLink" parameterType="DramaRoomSemLink">
        update drama_room_sem_link
        <trim prefix="SET" suffixOverrides=",">
            <if test="platform != null">platform = #{platform},</if>
            <if test="name != null">name = #{name},</if>
            <if test="target != null">target = #{target},</if>
            <if test="callbackType != null">callback_type = #{callbackType},</if>
            <if test="pixelCode != null">pixel_code = #{pixelCode},</if>
            <if test="landingTemplate != null">landing_template = #{landingTemplate},</if>
            <if test="movieId != null">movie_id = #{movieId},</if>
            <if test="payTemplateId != null">pay_template_id = #{payTemplateId},</if>
            <if test="link != null">link = #{link},</if>
            <if test="system != null">system = #{system},</if>
            <if test="url != null">url = #{url},</if>
            <if test="videoCoinPlanId != null">video_coin_plan_id = #{videoCoinPlanId},</if>
            <if test="pdata != null">pdata = #{pdata},</if>
            <if test="linkAppId != null">link_app_id = #{linkAppId},</if>
            <if test="pixelId != null">pixel_id = #{pixelId},</if>
            <if test="language != null">language = #{language},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteDramaRoomSemLinkById" parameterType="Long">
        delete from drama_room_sem_link where id = #{id}
    </delete>

    <delete id="deleteDramaRoomSemLinkByIds" parameterType="String">
        delete from drama_room_sem_link where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>