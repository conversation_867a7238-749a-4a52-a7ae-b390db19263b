<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.drama.mapper.AppUserDeviceMapper">

    <resultMap type="com.ruoyi.common.core.domain.entity.AppUserDevice" id="AppUserDeviceResult">
        <result property="id"                   column="id"                     />
        <result property="userId"               column="user_id"                />
        <result property="deviceId"             column="device_id"              />
        <result property="deviceManufacturer"   column="device_manufacturer"    />
        <result property="devicePlatformType"   column="device_platform_type"   />
        <result property="deviceSystemVersion"  column="device_system_version"  />
        <result property="createBy"             column="create_by"              />
        <result property="createTime"           column="create_time"            />
        <result property="updateBy"             column="update_by"              />
        <result property="updateTime"           column="update_time"            />
        <result property="remark"               column="remark"                 />
    </resultMap>

    <sql id="selectAppUserDeviceVo">
        select id, user_id, device_id, device_manufacturer, device_platform_type, device_system_version, create_by, create_time, update_by, update_time, remark from app_user_device
    </sql>

    <select id="selectAppUserDeviceById" parameterType="Long" resultMap="AppUserDeviceResult">
        <include refid="selectAppUserDeviceVo"/>
        where id = #{id}
    </select>

    <select id="selectAppUserDevicesByUserId" parameterType="Long" resultMap="AppUserDeviceResult">
        <include refid="selectAppUserDeviceVo"/>
        where user_id = #{userId}
        order by create_time desc
    </select>

    <select id="selectAppUserDeviceByDeviceId" parameterType="Long" resultMap="AppUserDeviceResult">
        <include refid="selectAppUserDeviceVo"/>
        where device_id = #{deviceId}
        limit 1
    </select>

    <select id="selectAppUserDeviceList" parameterType="com.ruoyi.common.core.domain.entity.AppUserDevice" resultMap="AppUserDeviceResult">
        <include refid="selectAppUserDeviceVo"/>
        <where>
            <if test="userId != null">
                and user_id = #{userId}
            </if>
            <if test="deviceId != null">
                and device_id = #{deviceId}
            </if>
            <if test="deviceManufacturer != null and deviceManufacturer != ''">
                and device_manufacturer like concat('%', #{deviceManufacturer}, '%')
            </if>
            <if test="devicePlatformType != null and devicePlatformType != ''">
                and device_platform_type = #{devicePlatformType}
            </if>
            <if test="deviceSystemVersion != null and deviceSystemVersion != ''">
                and device_system_version = #{deviceSystemVersion}
            </if>
        </where>
        order by create_time desc
    </select>

    <insert id="insertAppUserDevice" parameterType="com.ruoyi.common.core.domain.entity.AppUserDevice">
        insert into app_user_device
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="userId != null">user_id,</if>
            <if test="deviceId != null">device_id,</if>
            <if test="deviceManufacturer != null and deviceManufacturer != ''">device_manufacturer,</if>
            <if test="devicePlatformType != null and devicePlatformType != ''">device_platform_type,</if>
            <if test="deviceSystemVersion != null and deviceSystemVersion != ''">device_system_version,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="remark != null and remark != ''">remark,</if>
            create_time,
            update_time
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="userId != null">#{userId},</if>
            <if test="deviceId != null">#{deviceId},</if>
            <if test="deviceManufacturer != null and deviceManufacturer != ''">#{deviceManufacturer},</if>
            <if test="devicePlatformType != null and devicePlatformType != ''">#{devicePlatformType},</if>
            <if test="deviceSystemVersion != null and deviceSystemVersion != ''">#{deviceSystemVersion},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="remark != null and remark != ''">#{remark},</if>
            CURRENT_TIMESTAMP(3),
            CURRENT_TIMESTAMP(3)
        </trim>
    </insert>

    <update id="updateAppUserDevice" parameterType="com.ruoyi.common.core.domain.entity.AppUserDevice">
        update app_user_device
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="deviceId != null">device_id = #{deviceId},</if>
            <if test="deviceManufacturer != null and deviceManufacturer != ''">device_manufacturer = #{deviceManufacturer},</if>
            <if test="devicePlatformType != null and devicePlatformType != ''">device_platform_type = #{devicePlatformType},</if>
            <if test="deviceSystemVersion != null and deviceSystemVersion != ''">device_system_version = #{deviceSystemVersion},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            <if test="remark != null">remark = #{remark},</if>
            update_time = CURRENT_TIMESTAMP(3)
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteAppUserDeviceById" parameterType="Long">
        delete from app_user_device where id = #{id}
    </delete>

    <delete id="deleteAppUserDeviceByIds" parameterType="Long">
        delete from app_user_device where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteAppUserDeviceByUserId" parameterType="Long">
        delete from app_user_device where user_id = #{userId}
    </delete>

    <select id="checkDeviceBinding" resultType="int">
        select count(1) from app_user_device
        where device_id = #{deviceId}
        <if test="userId != null">
            and user_id != #{userId}
        </if>
        limit 1
    </select>

    <select id="countAppUserDevices" parameterType="com.ruoyi.common.core.domain.entity.AppUserDevice" resultType="int">
        select count(1) from app_user_device
        <where>
            <if test="userId != null">
                and user_id = #{userId}
            </if>
            <if test="devicePlatformType != null and devicePlatformType != ''">
                and device_platform_type = #{devicePlatformType}
            </if>
        </where>
    </select>

    <select id="selectAppUserDeviceByUserIdAndDeviceId" resultMap="AppUserDeviceResult">
        <include refid="selectAppUserDeviceVo"/>
        where user_id = #{userId} and device_id = #{deviceId}
        limit 1
    </select>

    <select id="countAppUserDevicesByUserId" parameterType="Long" resultType="int">
        select count(1) from app_user_device
        where user_id = #{userId}
    </select>

    <select id="selectAppUserDeviceListByUserId" parameterType="Long" resultMap="AppUserDeviceResult">
        <include refid="selectAppUserDeviceVo"/>
        where user_id = #{userId}
        order by create_time
    </select>

    <select id="selectAppUserDeviceListInUserIds" resultMap="AppUserDeviceResult">
        <include refid="selectAppUserDeviceVo"/>
        where user_id in
        <foreach item="userId" collection="userIds" open="(" separator="," close=")">
            #{userId}
        </foreach>
        order by create_time desc
    </select>

</mapper>