<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.drama.mapper.DramaRoomMovieCategoryMapper">

    <resultMap type="DramaRoomMovieCategory" id="DramaRoomMovieCategoryResult">
        <result property="id" column="id"/>
        <result property="movieId" column="movie_id"/>
        <result property="categoryId" column="category_id"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="selectDramaRoomMovieCategoryVo">
        select id, movie_id, category_id, create_by, create_time, update_by, update_time
        from drama_room_movie_category
    </sql>

    <select id="selectDramaRoomMovieCategoryList" parameterType="DramaRoomMovieCategory"
            resultMap="DramaRoomMovieCategoryResult">
        <include refid="selectDramaRoomMovieCategoryVo"/>
        <where>
            <if test="movieId != null ">and movie_id = #{movieId}</if>
            <if test="categoryId != null ">and category_id = #{categoryId}</if>
        </where>
    </select>

    <select id="selectDramaRoomMovieCategoryById" parameterType="Long" resultMap="DramaRoomMovieCategoryResult">
        <include refid="selectDramaRoomMovieCategoryVo"/>
        where id = #{id}
    </select>
    <select id="selectByCategoryIdList" resultType="com.ruoyi.drama.domain.DramaRoomMovieCategory">
        select * from drama_room_movie_category where category_id in
        <foreach item="categoryId" collection="list" open="(" separator="," close=")">
            #{categoryId}
        </foreach>
    </select>
    <select id="selectDramaRoomMovieCategoryByCategoryId" resultMap="DramaRoomMovieCategoryResult">
        select *
        from drama_room_movie_category
        where category_id = #{categoryId}
    </select>

    <insert id="insertDramaRoomMovieCategory" parameterType="DramaRoomMovieCategory">
        insert into drama_room_movie_category
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="movieId != null">movie_id,</if>
            <if test="categoryId != null">category_id,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="movieId != null">#{movieId},</if>
            <if test="categoryId != null">#{categoryId},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>
    <insert id="insertDramaRoomMovieCategoryList">
        insert into drama_room_movie_category (movie_id, category_id, create_by, create_time, update_by, update_time)
        values
        <foreach item="item" collection="list" separator=",">
            (#{item.movieId}, #{item.categoryId}, #{item.createBy}, #{item.createTime}, #{item.updateBy},
            #{item.updateTime})
        </foreach>
    </insert>

    <update id="updateDramaRoomMovieCategory" parameterType="DramaRoomMovieCategory">
        update drama_room_movie_category
        <trim prefix="SET" suffixOverrides=",">
            <if test="movieId != null">movie_id = #{movieId},</if>
            <if test="categoryId != null">category_id = #{categoryId},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteDramaRoomMovieCategoryById" parameterType="Long">
        delete
        from drama_room_movie_category
        where id = #{id}
    </delete>

    <delete id="deleteDramaRoomMovieCategoryByIds" parameterType="String">
        delete from drama_room_movie_category where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    <delete id="deleteDramaRoomMovieCategoryByCategoryId">
        delete
        from drama_room_movie_category
        where category_id = #{categoryId}
    </delete>
</mapper>
