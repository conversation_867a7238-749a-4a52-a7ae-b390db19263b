<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.drama.mapper.DramaRoomVideoI18nMapper">

    <resultMap type="DramaRoomVideoI18n" id="DramaRoomVideoI18nResult">
        <result property="id" column="id"/>
        <result property="videoId" column="video_id"/>
        <result property="languageCode" column="language_code"/>
        <result property="subtitleUrl" column="subtitle_url"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
        <result property="status" column="status"/>
    </resultMap>

    <sql id="selectDramaRoomVideoI18nVo">
        select id,
               video_id,
               language_code,
               subtitle_url,
               create_by,
               create_time,
               update_by,
               update_time,
               remark,
               status
        from drama_room_video_i18n
    </sql>

    <select id="selectDramaRoomVideoI18nList" parameterType="DramaRoomVideoI18n" resultMap="DramaRoomVideoI18nResult">
        <include refid="selectDramaRoomVideoI18nVo"/>
        <where>
            <if test="videoId != null ">and video_id = #{videoId}</if>
            <if test="languageCode != null  and languageCode != ''">and language_code = #{languageCode}</if>
            <if test="subtitleUrl != null  and subtitleUrl != ''">and subtitle_url = #{subtitleUrl}</if>
            <if test="status != null  and status != ''">and status = #{status}</if>
        </where>
    </select>

    <select id="selectDramaRoomVideoI18nById" parameterType="Long" resultMap="DramaRoomVideoI18nResult">
        <include refid="selectDramaRoomVideoI18nVo"/>
        where id = #{id}
    </select>
    <select id="selectDramaRoomVideoI18nByVideoIds" resultMap="DramaRoomVideoI18nResult">
        <include refid="selectDramaRoomVideoI18nVo"/>
        where 1=1
        <if test="videoIdList != null and videoIdList.size > 0">
            and video_id in
            <foreach item="videoId" collection="videoIdList" separator="," open="(" close=")">
                #{videoId}
            </foreach>
        </if>
        <if test="null != languageCodeList and languageCodeList.size > 0">
            and language_code in
            <foreach item="languageCode" collection="languageCodeList" separator="," open="("  close=")">
                #{languageCode}
            </foreach>
        </if>
    </select>

    <insert id="insertDramaRoomVideoI18n" parameterType="DramaRoomVideoI18n" useGeneratedKeys="true" keyProperty="id">
        insert into drama_room_video_i18n
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="videoId != null">video_id,</if>
            <if test="languageCode != null and languageCode != ''">language_code,</if>
            <if test="subtitleUrl != null">subtitle_url,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
            <if test="status != null and status != ''">status,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="videoId != null">#{videoId},</if>
            <if test="languageCode != null and languageCode != ''">#{languageCode},</if>
            <if test="subtitleUrl != null">#{subtitleUrl},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
            <if test="status != null and status != ''">#{status},</if>
        </trim>
    </insert>

    <update id="updateDramaRoomVideoI18n" parameterType="DramaRoomVideoI18n">
        update drama_room_video_i18n
        <trim prefix="SET" suffixOverrides=",">
            <if test="videoId != null">video_id = #{videoId},</if>
            <if test="languageCode != null and languageCode != ''">language_code = #{languageCode},</if>
            <if test="subtitleUrl != null">subtitle_url = #{subtitleUrl},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="status != null and status != ''">status = #{status},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteDramaRoomVideoI18nById" parameterType="Long">
        delete
        from drama_room_video_i18n
        where id = #{id}
    </delete>

    <delete id="deleteDramaRoomVideoI18nByIds" parameterType="String">
        delete from drama_room_video_i18n where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>