<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.drama.mapper.DramaRoomMovieTagMapper">

    <resultMap type="DramaRoomMovieTag" id="DramaRoomMovieTagResult">
        <result property="id" column="id"/>
        <result property="movieId" column="movie_id"/>
        <result property="tagId" column="tag_id"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="selectDramaRoomMovieTagVo">
        select id, movie_id, tag_id, create_by, create_time, update_by, update_time
        from drama_room_movie_tag
    </sql>

    <select id="selectDramaRoomMovieTagList" parameterType="DramaRoomMovieTag" resultMap="DramaRoomMovieTagResult">
        <include refid="selectDramaRoomMovieTagVo"/>
        <where>
            <if test="movieId != null ">and movie_id = #{movieId}</if>
            <if test="tagId != null ">and tag_id = #{tagId}</if>
        </where>
    </select>

    <select id="selectDramaRoomMovieTagById" parameterType="Long" resultMap="DramaRoomMovieTagResult">
        <include refid="selectDramaRoomMovieTagVo"/>
        where id = #{id}
    </select>
    <select id="selectDramaRoomMovieTagByTagId" resultMap="DramaRoomMovieTagResult">
        <include refid="selectDramaRoomMovieTagVo"/>
        where tag_id = #{tagId}
    </select>

    <insert id="insertDramaRoomMovieTag" parameterType="DramaRoomMovieTag">
        insert into drama_room_movie_tag
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="movieId != null">movie_id,</if>
            <if test="tagId != null">tag_id,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="movieId != null">#{movieId},</if>
            <if test="tagId != null">#{tagId},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <insert id="insertDramaRoomMovieTagList">
        insert into drama_room_movie_tag (movie_id, tag_id, create_by, create_time, update_by, update_time)
        values
        <foreach item="item" collection="list" separator=",">
            (#{item.movieId}, #{item.tagId}, #{item.createBy}, #{item.createTime}, #{item.updateBy}, #{item.updateTime})
        </foreach>
    </insert>

    <update id="updateDramaRoomMovieTag" parameterType="DramaRoomMovieTag">
        update drama_room_movie_tag
        <trim prefix="SET" suffixOverrides=",">
            <if test="movieId != null">movie_id = #{movieId},</if>
            <if test="tagId != null">tag_id = #{tagId},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteDramaRoomMovieTagById" parameterType="Long">
        delete
        from drama_room_movie_tag
        where id = #{id}
    </delete>

    <delete id="deleteDramaRoomMovieTagByIds" parameterType="String">
        delete from drama_room_movie_tag where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    <delete id="deleteDramaRoomMovieTagByTagId">
        delete
        from drama_room_movie_tag
        where tag_id = #{tagId}
    </delete>
</mapper>
