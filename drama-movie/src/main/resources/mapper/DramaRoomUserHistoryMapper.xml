<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.drama.mapper.DramaRoomUserHistoryMapper">

    <resultMap type="DramaRoomUserHistory" id="DramaRoomUserHistoryResult">
        <result property="id"    column="id"    />
        <result property="userId"    column="user_id"    />
        <result property="movieId"    column="movie_id"    />
        <result property="videoId"    column="video_id"    />
        <result property="videoNum"    column="video_num"    />
        <result property="viewDate"    column="view_date"    />
        <result property="progress"    column="progress"    />
        <result property="remark"    column="remark"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectDramaRoomUserHistoryVo">
        select id, user_id, movie_id, video_id,video_num, view_date, progress, remark, create_time, update_time from drama_room_user_history
    </sql>

    <select id="selectDramaRoomUserHistoryList" parameterType="DramaRoomUserHistory" resultMap="DramaRoomUserHistoryResult">
        <include refid="selectDramaRoomUserHistoryVo"/>
        <where>
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="movieId != null "> and movie_id = #{movieId}</if>
            <if test="videoId != null "> and video_id = #{videoId}</if>
            <if test="viewDate != null "> and view_date = #{viewDate}</if>
            <if test="progress != null "> and progress = #{progress}</if>
        </where>
    </select>

    <select id="selectDramaRoomUserHistoryById" parameterType="Long" resultMap="DramaRoomUserHistoryResult">
        <include refid="selectDramaRoomUserHistoryVo"/>
        where id = #{id}
    </select>
    <select id="queryUserHistoryByUserId" resultType="com.ruoyi.drama.domain.vo.DramaRoomUserHistoryVO">
        <include refid="selectDramaRoomUserHistoryVo"/>
        where user_id = #{userId}
    </select>
    <select id="selectDramaRoomUserHistoryByUserId" resultType="com.ruoyi.common.drama.vo.UserHistoryVO">
        select t1.id,
               t2.title,
               t2.cover_image as coverImage,
               t2.release_date as releaseDate,
               t2.total_videos as totalVideos,
               t1.movie_id as movieId,
               t1.video_id as videoId ,
               t1.view_date as viewDate,
               t1.progress as progress,
               t1.remark as remark,
               t1.create_time as createTime,
               t1.update_time as updateTime,
               t1.video_num as videoNum
        from drama_room_user_history t1
        left join drama_room_movie t2
        on t1.movie_id = t2.id
        where t1.user_id = #{userId}
    </select>

    <insert id="insertDramaRoomUserHistory" parameterType="DramaRoomUserHistory" useGeneratedKeys="true" keyProperty="id">
        insert into drama_room_user_history
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">user_id,</if>
            <if test="movieId != null">movie_id,</if>
            <if test="videoId != null">video_id,</if>
            <if test="viewDate != null">view_date,</if>
            <if test="progress != null">progress,</if>
            <if test="remark != null">remark,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="videoNum != null">video_num,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">#{userId},</if>
            <if test="movieId != null">#{movieId},</if>
            <if test="videoId != null">#{videoId},</if>
            <if test="viewDate != null">#{viewDate},</if>
            <if test="progress != null">#{progress},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="videoNum != null">#{videoNum},</if>
        </trim>
    </insert>

    <update id="updateDramaRoomUserHistory" parameterType="DramaRoomUserHistory">
        update drama_room_user_history
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="movieId != null">movie_id = #{movieId},</if>
            <if test="videoId != null">video_id = #{videoId},</if>
            <if test="viewDate != null">view_date = #{viewDate},</if>
            <if test="progress != null">progress = #{progress},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="videoNum != null">video_num = #{videoNum},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteDramaRoomUserHistoryById" parameterType="Long">
        delete from drama_room_user_history where id = #{id}
    </delete>

    <delete id="deleteDramaRoomUserHistoryByIds" parameterType="String">
        delete from drama_room_user_history where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
