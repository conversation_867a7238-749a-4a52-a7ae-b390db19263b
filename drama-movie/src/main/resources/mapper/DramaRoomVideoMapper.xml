<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.drama.mapper.DramaRoomVideoMapper">

    <resultMap type="DramaRoomVideo" id="DramaRoomVideoResult">
        <result property="id"    column="id"    />
        <result property="movieId"    column="movie_id"    />
        <result property="coverImage"    column="cover_image"    />
        <result property="videoNum"    column="video_num"    />
        <result property="videoUrl"    column="video_url"    />
        <result property="duration"    column="duration"    />
        <result property="playCount"    column="play_count"    />
        <result property="likeCount"    column="like_count"    />
        <result property="coin"    column="coin"    />
        <result property="status"    column="status"    />
        <result property="remark"    column="remark"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectDramaRoomVideoVo">
        select id, movie_id, cover_image, video_num, video_url, duration, play_count, like_count, coin, status, remark, create_by, create_time, update_by, update_time from drama_room_video
    </sql>

    <select id="selectDramaRoomVideoList" parameterType="DramaRoomVideo" resultMap="DramaRoomVideoResult">
        <include refid="selectDramaRoomVideoVo"/>
        <where>
            <if test="movieId != null "> and movie_id = #{movieId}</if>
            <if test="videoNum != null "> and video_num = #{videoNum}</if>
            <if test="videoUrl != null  and videoUrl != ''"> and video_url = #{videoUrl}</if>
            <if test="duration != null "> and duration = #{duration}</if>
            <if test="playCount != null "> and play_count = #{playCount}</if>
            <if test="likeCount != null "> and like_count = #{likeCount}</if>
            <if test="coin != null "> and coin = #{coin}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
        </where>
    </select>

    <select id="selectDramaRoomVideoById" parameterType="Long" resultMap="DramaRoomVideoResult">
        <include refid="selectDramaRoomVideoVo"/>
        where id = #{id}
    </select>

    <select id="selectDramaRoomVideoByMovieId" resultMap="DramaRoomVideoResult">
        <include refid="selectDramaRoomVideoVo"/>
        where video_num = 1
        and movie_id = #{movieId}
    </select>

    <insert id="insertDramaRoomVideo" parameterType="DramaRoomVideo" useGeneratedKeys="true" keyProperty="id">
        insert into drama_room_video
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="movieId != null">movie_id,</if>
            <if test="coverImage != null">cover_image,</if>
            <if test="videoNum != null">video_num,</if>
            <if test="videoUrl != null and videoUrl != ''">video_url,</if>
            <if test="duration != null">duration,</if>
            <if test="playCount != null">play_count,</if>
            <if test="likeCount != null">like_count,</if>
            <if test="coin != null">coin,</if>
            <if test="status != null">status,</if>
            <if test="remark != null">remark,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="movieId != null">#{movieId},</if>
            <if test="coverImage != null">#{coverImage},</if>
            <if test="videoNum != null">#{videoNum},</if>
            <if test="videoUrl != null and videoUrl != ''">#{videoUrl},</if>
            <if test="duration != null">#{duration},</if>
            <if test="playCount != null">#{playCount},</if>
            <if test="collectCount != null">#{collectCount},</if>
            <if test="likeCount != null">#{likeCount},</if>
            <if test="coin != null">#{coin},</if>
            <if test="status != null">#{status},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updateDramaRoomVideo" parameterType="DramaRoomVideo">
        update drama_room_video
        <trim prefix="SET" suffixOverrides=",">
            <if test="movieId != null">movie_id = #{movieId},</if>
            <if test="coverImage != null">cover_image = #{coverImage},</if>
            <if test="videoNum != null">video_num = #{videoNum},</if>
            <if test="videoUrl != null and videoUrl != ''">video_url = #{videoUrl},</if>
            <if test="duration != null">duration = #{duration},</if>
            <if test="playCount != null">play_count = #{playCount},</if>
            <if test="likeCount != null">like_count = #{likeCount},</if>
            <if test="coin != null">coin = #{coin},</if>
            <if test="status != null">status = #{status},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteDramaRoomVideoById" parameterType="Long">
        delete from drama_room_video where id = #{id}
    </delete>

    <delete id="deleteDramaRoomVideoByIds" parameterType="String">
        delete from drama_room_video where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
