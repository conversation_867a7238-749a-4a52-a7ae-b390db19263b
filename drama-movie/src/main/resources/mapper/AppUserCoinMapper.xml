<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.drama.mapper.AppUserCoinMapper">

    <resultMap type="AppUserCoin" id="AppUserCoinResult">
        <result property="id"    column="id"    />
        <result property="userId"    column="user_id"    />
        <result property="coins"    column="coins"    />
        <result property="rewardCoins"    column="reward_coins"    />
        <result property="status"    column="status"    />
        <result property="remark"    column="remark"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectAppUserCoinVo">
        select id, user_id, coins,reward_coins, status, remark, create_by, create_time, update_by, update_time from app_user_coin
    </sql>

    <select id="selectAppUserCoinList" parameterType="AppUserCoin" resultMap="AppUserCoinResult">
        <include refid="selectAppUserCoinVo"/>
        <where>
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="coins != null "> and coins = #{coins}</if>
            <if test="rewardCoins != null "> and reward_coins = #{rewardCoins}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
        </where>
    </select>

    <select id="selectAppUserCoinInUserIds" resultMap="AppUserCoinResult">
        <include refid="selectAppUserCoinVo"/>
        <where>
            <if test="list != null and list.size() > 0">
                and user_id in
                <foreach item="userId" collection="list" open="(" separator="," close=")">
                    #{userId}
                </foreach>
            </if>
        </where>
    </select>


    <select id="selectAppUserCoinById" parameterType="Long" resultMap="AppUserCoinResult">
        <include refid="selectAppUserCoinVo"/>
        where id = #{id}
    </select>
    <select id="getUserCoinByUserId" resultType="com.ruoyi.common.drama.vo.UserCoinVO">
        SELECT
            id,
            user_id userId,
            coins,
            reward_coins rewardCoins,
            STATUS
        FROM
            app_user_coin
        WHERE
        `status` = 1
        AND user_id = #{userId}
    </select>

    <select id="selectUserCoinByUserId" resultMap="AppUserCoinResult">
            <include refid="selectAppUserCoinVo"/>
            where user_id = #{userId} and status = 1
    </select>



    <insert id="insertAppUserCoin" parameterType="AppUserCoin" useGeneratedKeys="true" keyProperty="id">
        insert into app_user_coin
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">user_id,</if>
            <if test="coins != null">coins,</if>
            <if test="rewardCoins != null">reward_coins,</if>
            <if test="status != null and status != ''">status,</if>
            <if test="remark != null">remark,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">#{userId},</if>
            <if test="coins != null">#{coins},</if>
            <if test="rewardCoins != null">#{rewardCoins},</if>
            <if test="status != null and status != ''">#{status},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updateAppUserCoin" parameterType="AppUserCoin">
        update app_user_coin
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="coins != null">coins = #{coins},</if>
            <if test="rewardCoins != null">reward_coins = #{rewardCoins},</if>
            <if test="status != null and status != ''">status = #{status},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteAppUserCoinById" parameterType="Long">
        delete from app_user_coin where id = #{id}
    </delete>

    <delete id="deleteAppUserCoinByIds" parameterType="String">
        delete from app_user_coin where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>