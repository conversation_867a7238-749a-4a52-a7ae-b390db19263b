<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.drama.mapper.DramaRoomPayTemplateAmtMapper">

    <resultMap type="DramaRoomPayTemplateAmt" id="DramaRoomPayTemplateAmtResult">
        <result property="id"    column="id"    />
        <result property="price"    column="price"    />
        <result property="coin"    column="coin"    />
        <result property="appleProductId"    column="apple_product_id"    />
        <result property="googleProductId"    column="google_product_id"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectDramaRoomPayTemplateAmtVo">
        select id, price, coin, apple_product_id, google_product_id, create_by, create_time, update_by, update_time, remark from drama_room_pay_template_amt
    </sql>

    <select id="selectDramaRoomPayTemplateAmtList" parameterType="DramaRoomPayTemplateAmt" resultMap="DramaRoomPayTemplateAmtResult">
        <include refid="selectDramaRoomPayTemplateAmtVo"/>
        <where>
            <if test="price != null ">and price = #{price}</if>
            <if test="coin != null ">and coin = #{coin}</if>
            <if test="appleProductId != null ">and apple_product_id = #{appleProductId}</if>
            <if test="googleProductId != null ">and google_product_id = #{googleProductId}</if>
            <if test="remark != null ">and remark = #{remark}</if>
        </where>
    </select>

    <select id="selectDramaRoomPayTemplateAmtById" parameterType="Long" resultMap="DramaRoomPayTemplateAmtResult">
        <include refid="selectDramaRoomPayTemplateAmtVo"/>
        where id = #{id}
    </select>

    <insert id="insertDramaRoomPayTemplateAmt" parameterType="DramaRoomPayTemplateAmt" useGeneratedKeys="true" keyProperty="id">
        insert into drama_room_pay_template_amt
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="price != null">price,</if>
            <if test="coin != null">coin,</if>
            <if test="appleProductId != null">apple_product_id,</if>
            <if test="googleProductId != null">google_product_id,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="price != null">#{price},</if>
            <if test="coin != null">#{coin},</if>
            <if test="appleProductId != null">#{appleProductId},</if>
            <if test="googleProductId != null">#{googleProductId},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
    </insert>

    <update id="updateDramaRoomPayTemplateAmt" parameterType="DramaRoomPayTemplateAmt">
        update drama_room_pay_template_amt
        <trim prefix="SET" suffixOverrides=",">
            <if test="price != null">price = #{price},</if>
            <if test="coin != null">coin = #{coin},</if>
            <if test="appleProductId != null">apple_product_id = #{appleProductId},</if>
            <if test="googleProductId != null">google_product_id = #{googleProductId},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteDramaRoomPayTemplateAmtById" parameterType="Long">
        delete from drama_room_pay_template_amt where id = #{id}
    </delete>

    <delete id="deleteDramaRoomPayTemplateAmtByIds" parameterType="String">
        delete from drama_room_pay_template_amt where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>