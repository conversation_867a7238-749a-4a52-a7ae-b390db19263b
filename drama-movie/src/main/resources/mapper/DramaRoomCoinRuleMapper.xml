<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.drama.mapper.DramaRoomCoinRuleMapper">

    <resultMap type="DramaRoomCoinRule" id="DramaRoomCoinRuleResult">
        <result property="id"    column="id"    />
        <result property="type"    column="type"    />
        <result property="name"    column="name"    />
        <result property="content"    column="content"    />
        <result property="rechargeAmount"    column="recharge_amount"    />
        <result property="coins"    column="coins"    />
        <result property="rewardCoins"    column="reward_coins"    />
        <result property="status"    column="status"    />
        <result property="remark"    column="remark"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectDramaRoomCoinRuleVo">
        select id, type, name, content, recharge_amount, coins, reward_coins, status, remark, create_by, create_time, update_by, update_time from drama_room_coin_rule
    </sql>

    <select id="selectDramaRoomCoinRuleList" parameterType="DramaRoomCoinRule" resultMap="DramaRoomCoinRuleResult">
        <include refid="selectDramaRoomCoinRuleVo"/>
        <where>
            <if test="type != null "> and type = #{type}</if>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="content != null  and content != ''"> and content = #{content}</if>
            <if test="rechargeAmount != null "> and recharge_amount = #{rechargeAmount}</if>
            <if test="coins != null "> and coins = #{coins}</if>
            <if test="rewardCoins != null "> and reward_coins = #{rewardCoins}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
        </where>
    </select>

    <select id="selectDramaRoomCoinRuleById" parameterType="Long" resultMap="DramaRoomCoinRuleResult">
        <include refid="selectDramaRoomCoinRuleVo"/>
        where id = #{id}
    </select>
    <select id="getCoinRuleList" resultType="com.ruoyi.common.drama.vo.CoinRuleVO">
        <include refid="selectDramaRoomCoinRuleVo"/>
        WHERE
        STATUS = 1
        <if test="type != null "> and type = #{type}</if>
    </select>

    <insert id="insertDramaRoomCoinRule" parameterType="DramaRoomCoinRule" useGeneratedKeys="true" keyProperty="id">
        insert into drama_room_coin_rule
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="type != null">type,</if>
            <if test="name != null">name,</if>
            <if test="content != null">content,</if>
            <if test="rechargeAmount != null">recharge_amount,</if>
            <if test="coins != null">coins,</if>
            <if test="rewardCoins != null">reward_coins,</if>
            <if test="status != null and status != ''">status,</if>
            <if test="remark != null">remark,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="type != null">#{type},</if>
            <if test="name != null">#{name},</if>
            <if test="content != null">#{content},</if>
            <if test="rechargeAmount != null">#{rechargeAmount},</if>
            <if test="coins != null">#{coins},</if>
            <if test="rewardCoins != null">#{rewardCoins},</if>
            <if test="status != null and status != ''">#{status},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updateDramaRoomCoinRule" parameterType="DramaRoomCoinRule">
        update drama_room_coin_rule
        <trim prefix="SET" suffixOverrides=",">
            <if test="type != null">type = #{type},</if>
            <if test="name != null">name = #{name},</if>
            <if test="content != null">content = #{content},</if>
            <if test="rechargeAmount != null">recharge_amount = #{rechargeAmount},</if>
            <if test="coins != null">coins = #{coins},</if>
            <if test="rewardCoins != null">reward_coins = #{rewardCoins},</if>
            <if test="status != null and status != ''">status = #{status},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteDramaRoomCoinRuleById" parameterType="Long">
        delete from drama_room_coin_rule where id = #{id}
    </delete>

    <delete id="deleteDramaRoomCoinRuleByIds" parameterType="String">
        delete from drama_room_coin_rule where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>