<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.drama.mapper.DramaRoomAudioMapper">

    <resultMap type="DramaRoomAudio" id="DramaRoomAudioResult">
        <result property="id"    column="id"    />
        <result property="audio"    column="audio"    />
        <result property="createTime"    column="create_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectDramaRoomAudioVo">
        select id, audio, create_time, create_by, update_time, update_by, remark from drama_room_audio
    </sql>

    <select id="selectDramaRoomAudioList" parameterType="DramaRoomAudio" resultMap="DramaRoomAudioResult">
        <include refid="selectDramaRoomAudioVo"/>
        <where>
            <if test="audio != null  and audio != ''"> and audio = #{audio}</if>
        </where>
    </select>

    <select id="selectDramaRoomAudioById" parameterType="Long" resultMap="DramaRoomAudioResult">
        <include refid="selectDramaRoomAudioVo"/>
        where id = #{id}
    </select>

    <insert id="insertDramaRoomAudio" parameterType="DramaRoomAudio" useGeneratedKeys="true" keyProperty="id">
        insert into drama_room_audio
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="audio != null">audio,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="audio != null">#{audio},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
    </insert>

    <update id="updateDramaRoomAudio" parameterType="DramaRoomAudio">
        update drama_room_audio
        <trim prefix="SET" suffixOverrides=",">
            <if test="audio != null">audio = #{audio},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteDramaRoomAudioById" parameterType="Long">
        delete from drama_room_audio where id = #{id}
    </delete>

    <delete id="deleteDramaRoomAudioByIds" parameterType="String">
        delete from drama_room_audio where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>