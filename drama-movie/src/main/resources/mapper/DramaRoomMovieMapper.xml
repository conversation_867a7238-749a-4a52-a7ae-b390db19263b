<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.drama.mapper.DramaRoomMovieMapper">

    <resultMap type="DramaRoomMovie" id="DramaRoomMovieResult">
        <result property="id"    column="id"    />
        <result property="title"    column="title"    />
        <result property="name"    column="name"    />
        <result property="description"    column="description"    />
        <result property="coverImage"    column="cover_image"    />
        <result property="director"    column="director"    />
        <result property="actors"    column="actors"    />
        <result property="releaseDate"    column="release_date"    />
        <result property="totalVideos"    column="total_videos"    />
        <result property="status"    column="status"    />
        <result property="remark"    column="remark"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="areaId"    column="area_id"    />
        <result property="audioId"    column="audio_id"    />
        <result property="playCount"    column="play_count"    />
        <result property="collectCount"    column="collect_count"    />
        <result property="likeCount"    column="like_count"    />
        <result property="searchCount"    column="search_count"    />
    </resultMap>

    <sql id="selectDramaRoomMovieVo">
        select m.id            id,
               m.title         title,
               m.name          name,
               m.description   description,
               m.cover_image   cover_image,
               m.director      director,
               m.actors        actors,
               m.release_date  release_date,
               m.total_videos  total_videos,
               m.status        status,
               m.remark        remark,
               m.create_by     create_by,
               m.create_time   create_time,
               m.update_by     update_by,
               m.update_time   update_time,
               m.area_id       area_id,
               m.audio_id      audio_id,
               a.play_count    play_count,
               a.collect_count collect_count,
               a.like_count    like_count,
               a.search_count  search_count
        from drama_room_movie m
                 left join drama_room_movie_action a on a.id = m.id
    </sql>

    <select id="selectDramaRoomMovieList" parameterType="DramaRoomMovie" resultMap="DramaRoomMovieResult">
        <include refid="selectDramaRoomMovieVo"/>
        <where>
            <if test="title != null  and title != ''"> and title = #{title}</if>
            <if test="name != null  and name != ''"> and name = #{name}</if>
            <if test="description != null  and description != ''"> and description = #{description}</if>
            <if test="coverImage != null  and coverImage != ''"> and cover_image = #{coverImage}</if>
            <if test="director != null  and director != ''"> and director = #{director}</if>
            <if test="actors != null  and actors != ''"> and actors = #{actors}</if>
            <if test="releaseDate != null "> and release_date = #{releaseDate}</if>
            <if test="totalVideos != null "> and total_videos = #{totalVideos}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
        </where>
    </select>

    <select id="selectDramaRoomMovieById" parameterType="Long" resultMap="DramaRoomMovieResult">
        <include refid="selectDramaRoomMovieVo"/>
        where m.id = #{id}
    </select>

    <select id="selectDramaRoomMovieByIds" parameterType="DramaRoomMovie" resultMap="DramaRoomMovieResult">
        <include refid="selectDramaRoomMovieVo"/>
        where m.id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="selectDramaRoomMovieNewList" resultMap="DramaRoomMovieResult">
        <include refid="selectDramaRoomMovieVo"/>
        where m.status = '1'
        order by m.create_time desc
        limit #{i}
    </select>
    <select id="queryMovie" resultMap="DramaRoomMovieResult">
        <include refid="selectDramaRoomMovieVo"/>
        <where>
            <if test="movieIdList != null">
                and m.id in
                <foreach item="id" collection="movieIdList" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
            <if test="areaId != null">
                and m.area_id = #{areaId}
            </if>
            <if test="audioId != null">
                and m.audio_id = #{audioId}
            </if>
        </where>
        <if test="sort != null and sort == 'hot'">
            order by a.play_count desc
        </if>
        <if test="sort != null and sort == 'new'">
            order by m.id desc
        </if>
    </select>

    <select id="selectDramaRoomMovieByCategoryId" resultMap="DramaRoomMovieResult">
        SELECT
            m.id,
            m.title,
            m.name,
            m.description,
            m.cover_image,
            m.director,
            m.actors,
            m.release_date,
            m.total_videos,
            m.STATUS,
            m.remark,
            m.create_by,
            m.create_time,
            m.update_by,
            m.update_time,
            m.area_id,
            m.audio_id,
            a.play_count,
            a.collect_count,
            a.like_count,
            a.search_count
        FROM
            drama_room_movie m
                LEFT JOIN drama_room_movie_action a on a.id = m.id
                LEFT JOIN drama_room_movie_category c on c.movie_id = m.id
        where c.category_id = #{categoryId}
        and m.status = '1'
    </select>

    <select id="getHotRecommend" resultMap="DramaRoomMovieResult">
        <include refid="selectDramaRoomMovieVo"/>
        where m.status = '1'
        order by a.play_count desc
        limit #{i}
    </select>

    <select id="getHotSearch" resultMap="DramaRoomMovieResult">
        <include refid="selectDramaRoomMovieVo"/>
        where m.status = '1'
        order by a.search_count desc
        limit #{i}
    </select>

    <insert id="insertDramaRoomMovie" parameterType="DramaRoomMovie" useGeneratedKeys="true" keyProperty="id">
        insert into drama_room_movie
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="title != null and title != ''">title,</if>
            <if test="name != null and name != ''">name,</if>
            <if test="description != null">description,</if>
            <if test="coverImage != null">cover_image,</if>
            <if test="director != null">director,</if>
            <if test="actors != null">actors,</if>
            <if test="releaseDate != null">release_date,</if>
            <if test="totalVideos != null">total_videos,</if>
            <if test="status != null">status,</if>
            <if test="remark != null">remark,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="areaId != null">area_id,</if>
            <if test="audioId != null">audio_id,</if>
<!--            <if test="playCount != null">play_count,</if>-->
<!--            <if test="collectCount != null">collect_count,</if>-->
<!--            <if test="likeCount != null">like_count,</if>-->
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="title != null and title != ''">#{title},</if>
            <if test="name != null and name != ''">#{name},</if>
            <if test="description != null">#{description},</if>
            <if test="coverImage != null">#{coverImage},</if>
            <if test="director != null">#{director},</if>
            <if test="actors != null">#{actors},</if>
            <if test="releaseDate != null">#{releaseDate},</if>
            <if test="totalVideos != null">#{totalVideos},</if>
            <if test="status != null">#{status},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="areaId != null">#{areaId},</if>
            <if test="audioId != null">#{audioId},</if>
<!--            <if test="playCount != null">#{playCount},</if>-->
<!--            <if test="collectCount != null">#{collectCount},</if>-->
<!--            <if test="likeCount != null">#{likeCount},</if>-->
        </trim>
    </insert>

    <update id="updateDramaRoomMovie" parameterType="DramaRoomMovie">
        update drama_room_movie
        <trim prefix="SET" suffixOverrides=",">
            <if test="title != null and title != ''">title = #{title},</if>
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="description != null">description = #{description},</if>
            <if test="coverImage != null">cover_image = #{coverImage},</if>
            <if test="director != null">director = #{director},</if>
            <if test="actors != null">actors = #{actors},</if>
            <if test="releaseDate != null">release_date = #{releaseDate},</if>
            <if test="totalVideos != null">total_videos = #{totalVideos},</if>
            <if test="status != null">status = #{status},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="areaId != null">area_id = #{areaId},</if>
            <if test="audioId != null">audio_id = #{audioId},</if>
<!--            <if test="playCount != null">play_count = #{playCount},</if>-->
<!--            <if test="collectCount != null">collect_count = #{collectCount},</if>-->
<!--            <if test="likeCount != null">like_count = #{likeCount},</if>-->
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteDramaRoomMovieById" parameterType="Long">
        delete from drama_room_movie where id = #{id}
    </delete>

    <delete id="deleteDramaRoomMovieByIds" parameterType="String">
        delete from drama_room_movie where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
