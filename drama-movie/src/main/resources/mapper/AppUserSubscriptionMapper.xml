<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.drama.mapper.AppUserSubscriptionMapper">

    <resultMap type="AppUserSubscription" id="AppUserSubscriptionResult">
        <result property="id"    column="id"    />
        <result property="userId"    column="user_id"    />
        <result property="vipId"    column="vip_id"    />
        <result property="subscriptionId"    column="subscription_id"    />
        <result property="orderId"    column="order_id"    />
        <result property="subscriptionStatus"    column="subscription_status"    />
        <result property="subscribeTime"    column="subscribe_time"    />
        <result property="startTime"    column="start_time"    />
        <result property="endTime"    column="end_time"    />
        <result property="durationDays"    column="duration_days"    />
        <result property="autoRenew"    column="auto_renew"    />
        <result property="lastRenewTime"    column="last_renew_time"    />
        <result property="cancelTime"    column="cancel_time"    />
        <result property="remark"    column="remark"    />
        <result property="createTime"    column="create_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="updateBy"    column="update_by"    />
    </resultMap>

    <sql id="selectAppUserSubscriptionVo">
        select id, user_id, vip_id, subscription_id, order_id, subscription_status, subscribe_time, start_time, end_time, duration_days, auto_renew, last_renew_time, cancel_time, remark, create_time, create_by, update_time, update_by from app_user_subscription
    </sql>

    <select id="selectAppUserSubscriptionList" parameterType="AppUserSubscription" resultMap="AppUserSubscriptionResult">
        <include refid="selectAppUserSubscriptionVo"/>
        <where>
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="vipId != null "> and vip_id = #{vipId}</if>
            <if test="subscriptionId != null "> and subscription_id = #{subscriptionId}</if>
            <if test="orderId != null "> and order_id = #{orderId}</if>
            <if test="subscriptionStatus != null "> and subscription_status = #{subscriptionStatus}</if>
            <if test="subscribeTime != null "> and subscribe_time = #{subscribeTime}</if>
            <if test="startTime != null "> and start_time = #{startTime}</if>
            <if test="endTime != null "> and end_time = #{endTime}</if>
            <if test="durationDays != null "> and duration_days = #{durationDays}</if>
            <if test="autoRenew != null "> and auto_renew = #{autoRenew}</if>
            <if test="lastRenewTime != null "> and last_renew_time = #{lastRenewTime}</if>
            <if test="cancelTime != null "> and cancel_time = #{cancelTime}</if>
        </where>
    </select>

    <select id="selectAppUserSubscriptionById" parameterType="Long" resultMap="AppUserSubscriptionResult">
        <include refid="selectAppUserSubscriptionVo"/>
        where id = #{id}
    </select>

    <insert id="insertAppUserSubscription" parameterType="AppUserSubscription" useGeneratedKeys="true" keyProperty="id">
        insert into app_user_subscription
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">user_id,</if>
            <if test="vipId != null">vip_id,</if>
            <if test="subscriptionId != null">subscription_id,</if>
            <if test="orderId != null">order_id,</if>
            <if test="subscriptionStatus != null">subscription_status,</if>
            <if test="subscribeTime != null">subscribe_time,</if>
            <if test="startTime != null">start_time,</if>
            <if test="endTime != null">end_time,</if>
            <if test="durationDays != null">duration_days,</if>
            <if test="autoRenew != null">auto_renew,</if>
            <if test="lastRenewTime != null">last_renew_time,</if>
            <if test="cancelTime != null">cancel_time,</if>
            <if test="remark != null">remark,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null">update_by,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">#{userId},</if>
            <if test="vipId != null">#{vipId},</if>
            <if test="subscriptionId != null">#{subscriptionId},</if>
            <if test="orderId != null">#{orderId},</if>
            <if test="subscriptionStatus != null">#{subscriptionStatus},</if>
            <if test="subscribeTime != null">#{subscribeTime},</if>
            <if test="startTime != null">#{startTime},</if>
            <if test="endTime != null">#{endTime},</if>
            <if test="durationDays != null">#{durationDays},</if>
            <if test="autoRenew != null">#{autoRenew},</if>
            <if test="lastRenewTime != null">#{lastRenewTime},</if>
            <if test="cancelTime != null">#{cancelTime},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
        </trim>
    </insert>

    <update id="updateAppUserSubscription" parameterType="AppUserSubscription">
        update app_user_subscription
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="vipId != null">vip_id = #{vipId},</if>
            <if test="subscriptionId != null">subscription_id = #{subscriptionId},</if>
            <if test="orderId != null">order_id = #{orderId},</if>
            <if test="subscriptionStatus != null">subscription_status = #{subscriptionStatus},</if>
            <if test="subscribeTime != null">subscribe_time = #{subscribeTime},</if>
            <if test="startTime != null">start_time = #{startTime},</if>
            <if test="endTime != null">end_time = #{endTime},</if>
            <if test="durationDays != null">duration_days = #{durationDays},</if>
            <if test="autoRenew != null">auto_renew = #{autoRenew},</if>
            <if test="lastRenewTime != null">last_renew_time = #{lastRenewTime},</if>
            <if test="cancelTime != null">cancel_time = #{cancelTime},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteAppUserSubscriptionById" parameterType="Long">
        delete from app_user_subscription where id = #{id}
    </delete>

    <delete id="deleteAppUserSubscriptionByIds" parameterType="String">
        delete from app_user_subscription where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>