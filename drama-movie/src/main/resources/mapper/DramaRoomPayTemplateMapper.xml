<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.drama.mapper.DramaRoomPayTemplateMapper">

    <resultMap type="DramaRoomPayTemplate" id="DramaRoomPayTemplateResult">
        <result property="id"    column="id"    />
        <result property="name"    column="name"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectDramaRoomPayTemplateVo">
        select id, name, create_by, create_time, update_by, update_time, remark from drama_room_pay_template
    </sql>

    <select id="selectDramaRoomPayTemplateList" parameterType="DramaRoomPayTemplate" resultMap="DramaRoomPayTemplateResult">
        <include refid="selectDramaRoomPayTemplateVo"/>
        <where>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
        </where>
    </select>

    <select id="selectDramaRoomPayTemplateById" parameterType="Long" resultMap="DramaRoomPayTemplateResult">
        <include refid="selectDramaRoomPayTemplateVo"/>
        where id = #{id}
    </select>

    <insert id="insertDramaRoomPayTemplate" parameterType="DramaRoomPayTemplate" useGeneratedKeys="true" keyProperty="id">
        insert into drama_room_pay_template
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null">name,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="name != null">#{name},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
    </insert>

    <update id="updateDramaRoomPayTemplate" parameterType="DramaRoomPayTemplate">
        update drama_room_pay_template
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null">name = #{name},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteDramaRoomPayTemplateById" parameterType="Long">
        delete from drama_room_pay_template where id = #{id}
    </delete>

    <delete id="deleteDramaRoomPayTemplateByIds" parameterType="String">
        delete from drama_room_pay_template where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>