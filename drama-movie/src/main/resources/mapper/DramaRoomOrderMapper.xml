<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.drama.mapper.DramaRoomOrderMapper">

    <resultMap type="DramaRoomOrder" id="DramaRoomOrderResult">
        <result property="id"    column="id"    />
        <result property="orderNo"    column="order_no"    />
        <result property="userId"    column="user_id"    />
        <result property="productId"    column="product_id"    />
        <result property="productType"    column="product_type"    />
        <result property="amount"    column="amount"    />
        <result property="status"    column="status"    />
        <result property="expireTime"    column="expire_time"    />
        <result property="closeReason"    column="close_reason"    />
        <result property="refundStatus"    column="refund_status"    />
        <result property="channelCode"    column="channel_code"    />
        <result property="paymentMethod"    column="payment_method"    />
        <result property="paymentTime"    column="payment_time"    />
        <result property="channelOrderId"    column="channel_order_id"    />
        <result property="channelTransactionId"    column="channel_transaction_id"    />
        <result property="payEnvironment"    column="pay_environment"    />
        <result property="remark"    column="remark"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectDramaRoomOrderVo">
        select id, order_no, user_id, product_id, product_type, amount, status, expire_time, close_reason, refund_status, channel_code, payment_method, payment_time, channel_order_id, channel_transaction_id, pay_environment, remark, create_by, create_time, update_by, update_time from drama_room_order
    </sql>

    <select id="selectDramaRoomOrderList" parameterType="DramaRoomOrder" resultMap="DramaRoomOrderResult">
        <include refid="selectDramaRoomOrderVo"/>
        <where>
            <if test="orderNo != null  and orderNo != ''">and order_no = #{orderNo}</if>
            <if test="userId != null ">and user_id = #{userId}</if>
            <if test="productId != null ">and product_id = #{productId}</if>
            <if test="productType != null ">and product_type = #{productType}</if>
            <if test="amount != null ">and amount = #{amount}</if>
            <if test="status != null ">and status = #{status}</if>
            <if test="expireTime != null ">and expire_time = #{expireTime}</if>
            <if test="closeReason != null  and closeReason != ''">and close_reason = #{closeReason}</if>
            <if test="refundStatus != null ">and refund_status = #{refundStatus}</if>
            <if test="channelCode != null  and channelCode != ''">and channel_code = #{channelCode}</if>
            <if test="paymentMethod != null  and paymentMethod != ''">and payment_method = #{paymentMethod}</if>
            <if test="paymentTime != null ">and payment_time = #{paymentTime}</if>
            <if test="channelOrderId != null  and channelOrderId != ''">and channel_order_id = #{channelOrderId}</if>
            <if test="channelTransactionId != null  and channelTransactionId != ''">and channel_transaction_id =
                #{channelTransactionId}
            </if>
            <if test="payEnvironment != null ">and pay_environment = #{payEnvironment}</if>
        </where>
    </select>

    <select id="selectDramaRoomOrderById" parameterType="Long" resultMap="DramaRoomOrderResult">
        <include refid="selectDramaRoomOrderVo"/>
        where id = #{id}
    </select>

    <select id="selectPendingPayOrder" resultType="com.ruoyi.drama.domain.DramaRoomOrder">
        select * from drama_room_order where user_id = #{userId} and product_id = #{productId} and status = #{status}
    </select>

    <insert id="insertDramaRoomOrder" parameterType="DramaRoomOrder" useGeneratedKeys="true" keyProperty="id">
        insert into drama_room_order
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="orderNo != null and orderNo != ''">order_no,</if>
            <if test="userId != null">user_id,</if>
            <if test="productId != null">product_id,</if>
            <if test="productType != null">product_type,</if>
            <if test="amount != null">amount,</if>
            <if test="status != null">status,</if>
            <if test="expireTime != null">expire_time,</if>
            <if test="closeReason != null">close_reason,</if>
            <if test="refundStatus != null">refund_status,</if>
            <if test="channelCode != null">channel_code,</if>
            <if test="paymentMethod != null">payment_method,</if>
            <if test="paymentTime != null">payment_time,</if>
            <if test="channelOrderId != null">channel_order_id,</if>
            <if test="channelTransactionId != null">channel_transaction_id,</if>
            <if test="payEnvironment != null">pay_environment,</if>
            <if test="remark != null">remark,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="orderNo != null and orderNo != ''">#{orderNo},</if>
            <if test="userId != null">#{userId},</if>
            <if test="productId != null">#{productId},</if>
            <if test="productType != null">#{productType},</if>
            <if test="amount != null">#{amount},</if>
            <if test="status != null">#{status},</if>
            <if test="expireTime != null">#{expireTime},</if>
            <if test="closeReason != null">#{closeReason},</if>
            <if test="refundStatus != null">#{refundStatus},</if>
            <if test="channelCode != null">#{channelCode},</if>
            <if test="paymentMethod != null">#{paymentMethod},</if>
            <if test="paymentTime != null">#{paymentTime},</if>
            <if test="channelOrderId != null">#{channelOrderId},</if>
            <if test="channelTransactionId != null">#{channelTransactionId},</if>
            <if test="payEnvironment != null">#{payEnvironment},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updateDramaRoomOrder" parameterType="DramaRoomOrder">
        update drama_room_order
        <trim prefix="SET" suffixOverrides=",">
            <if test="orderNo != null and orderNo != ''">order_no = #{orderNo},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="productId != null">product_id = #{productId},</if>
            <if test="productType != null">product_type = #{productType},</if>
            <if test="amount != null">amount = #{amount},</if>
            <if test="status != null">status = #{status},</if>
            <if test="expireTime != null">expire_time = #{expireTime},</if>
            <if test="closeReason != null">close_reason = #{closeReason},</if>
            <if test="refundStatus != null">refund_status = #{refundStatus},</if>
            <if test="channelCode != null">channel_code = #{channelCode},</if>
            <if test="paymentMethod != null">payment_method = #{paymentMethod},</if>
            <if test="paymentTime != null">payment_time = #{paymentTime},</if>
            <if test="channelOrderId != null">channel_order_id = #{channelOrderId},</if>
            <if test="channelTransactionId != null">channel_transaction_id = #{channelTransactionId},</if>
            <if test="payEnvironment != null">pay_environment = #{payEnvironment},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteDramaRoomOrderById" parameterType="Long">
        delete from drama_room_order where id = #{id}
    </delete>

    <delete id="deleteDramaRoomOrderByIds" parameterType="String">
        delete from drama_room_order where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>