<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.drama.mapper.DramaRoomUserCollectMapper">

    <resultMap type="DramaRoomUserCollect" id="DramaRoomUserCollectResult">
        <result property="id"    column="id"    />
        <result property="userId"    column="user_id"    />
        <result property="movieId"    column="movie_id"    />
        <result property="status"    column="status"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectDramaRoomUserCollectVo">
        select id, user_id, movie_id, status, create_time, update_time from drama_room_user_collect
    </sql>

    <select id="selectDramaRoomUserCollectList" parameterType="DramaRoomUserCollect" resultMap="DramaRoomUserCollectResult">
        <include refid="selectDramaRoomUserCollectVo"/>
        <where>
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="movieId != null "> and movie_id = #{movieId}</if>
            <if test="status != null "> and status = #{status}</if>
        </where>
    </select>

    <select id="selectDramaRoomUserCollectById" parameterType="Long" resultMap="DramaRoomUserCollectResult">
        <include refid="selectDramaRoomUserCollectVo"/>
        where id = #{id}
    </select>
    <select id="selectDramaRoomUserCollectByUserId" resultType="com.ruoyi.common.drama.vo.UserCollectVO">
        select
        t1.id,
        t1.movie_id as movieId,
        t1.status as status,
        t1.create_time as createTime,
        t1.update_time as updateTime,
        t2.title,
        t2.cover_image as coverImage,
        t2.release_date as releaseDate,
        t2.total_videos as totalVideos,
        t3.view_date as viewDate,
        t3.progress,
        t3.video_id as videoId
        from drama_room_user_collect t1
        left join drama_room_movie t2
        on t1.movie_id = t2.id
        left join drama_room_user_history t3
        on t1.movie_id = t3.movie_id
        where t1.user_id = #{userId} and t1.status = 1
    </select>

    <insert id="insertDramaRoomUserCollect" parameterType="DramaRoomUserCollect" useGeneratedKeys="true" keyProperty="id">
        insert into drama_room_user_collect
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">user_id,</if>
            <if test="movieId != null">movie_id,</if>
            <if test="status != null">status,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">#{userId},</if>
            <if test="movieId != null">#{movieId},</if>
            <if test="status != null">#{status},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updateDramaRoomUserCollect" parameterType="DramaRoomUserCollect">
        update drama_room_user_collect
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="movieId != null">movie_id = #{movieId},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteDramaRoomUserCollectById" parameterType="Long">
        delete from drama_room_user_collect where id = #{id}
    </delete>

    <delete id="deleteDramaRoomUserCollectByIds" parameterType="String">
        delete from drama_room_user_collect where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
