<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.drama.mapper.DramaRoomAreaMapper">

    <resultMap type="DramaRoomArea" id="DramaRoomAreaResult">
        <result property="id"    column="id"    />
        <result property="area"    column="area"    />
        <result property="createTime"    column="create_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectDramaRoomAreaVo">
        select id, area, create_time, create_by, update_time, update_by, remark from drama_room_area
    </sql>

    <select id="selectDramaRoomAreaList" parameterType="DramaRoomArea" resultMap="DramaRoomAreaResult">
        <include refid="selectDramaRoomAreaVo"/>
        <where>
            <if test="area != null  and area != ''"> and area = #{area}</if>
        </where>
    </select>

    <select id="selectDramaRoomAreaById" parameterType="Long" resultMap="DramaRoomAreaResult">
        <include refid="selectDramaRoomAreaVo"/>
        where id = #{id}
    </select>

    <insert id="insertDramaRoomArea" parameterType="DramaRoomArea" useGeneratedKeys="true" keyProperty="id">
        insert into drama_room_area
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="area != null">area,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="area != null">#{area},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
    </insert>

    <update id="updateDramaRoomArea" parameterType="DramaRoomArea">
        update drama_room_area
        <trim prefix="SET" suffixOverrides=",">
            <if test="area != null">area = #{area},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteDramaRoomAreaById" parameterType="Long">
        delete from drama_room_area where id = #{id}
    </delete>

    <delete id="deleteDramaRoomAreaByIds" parameterType="String">
        delete from drama_room_area where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>