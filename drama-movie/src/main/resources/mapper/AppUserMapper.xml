<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.drama.mapper.AppUserMapper">

    <resultMap type="com.ruoyi.common.core.domain.entity.AppUser" id="AppUserResult">
        <id     property="id"               column="id"                 />
        <result property="userTempId"       column="user_temp_id"       />
        <result property="nickname"         column="nickname"           />
        <result property="avatar"           column="avatar"             />
        <result property="email"            column="email"              />
        <result property="accountType"      column="account_type"       />
        <result property="status"           column="status"             />
        <result property="authType"         column="auth_type"          />
        <result property="platformOpenId"   column="platform_open_id"   />
        <result property="credential"       column="credential"         />
        <result property="lastLoginIp"      column="last_login_ip"      />
        <result property="lastLoginDate"    column="last_login_date"    />
        <result property="firstDeviceId"    column="first_device_id"    />
        <result property="semLinkId"        column="sem_link_id"        />
        <result property="source"           column="source"             />
        <result property="pixelId"          column="pixel_id"           />
        <result property="adId"             column="ad_id"              />
        <result property="adInfo"           column="ad_info"            />
        <result property="createBy"         column="create_by"          />
        <result property="createTime"       column="create_time"        />
        <result property="updateBy"         column="update_by"          />
        <result property="updateTime"       column="update_time"        />
        <result property="remark"           column="remark"             />
    </resultMap>

    <sql id="selectAppUserVo">
        select id, user_temp_id, nickname, avatar, email, account_type, status, auth_type, platform_open_id, credential, last_login_ip, last_login_date, first_device_id, sem_link_id, source, pixel_id, ad_id, ad_info, create_by, create_time, update_by, update_time, remark from app_user
    </sql>

    <select id="selectAppUserByDeviceId" parameterType="String" resultMap="AppUserResult">
        select u.id, u.user_temp_id, u.nickname, u.avatar, u.email, u.account_type, u.status, u.auth_type, u.platform_open_id, u.credential, u.last_login_ip, u.last_login_date, u.first_device_id, u.sem_link_id, u.source, u.pixel_id, u.ad_id, u.ad_info, u.create_by, u.create_time, u.update_by, u.update_time, u.remark 
        from app_user u 
        inner join app_user_device d on u.id = d.user_id 
        where d.device_id = #{deviceId}
    </select>

    <select id="selectAppUserList" parameterType="com.ruoyi.common.core.domain.entity.AppUser" resultMap="AppUserResult">
        <include refid="selectAppUserVo"/>
        <where>
            <if test="userTempId != null and userTempId != ''">
                and user_temp_id = #{userTempId}
            </if>
            <if test="nickname != null and nickname != ''">
                and nickname like concat('%', #{nickname}, '%')
            </if>
            <if test="email != null and email != ''">
                and email = #{email}
            </if>
            <if test="accountType != null and accountType != ''">
                and account_type = #{accountType}
            </if>
            <if test="status != null and status != ''">
                and status = #{status}
            </if>
            <if test="authType != null and authType != ''">
                and auth_type = #{authType}
            </if>
            <if test="platformOpenId != null and platformOpenId != ''">
                and platform_open_id = #{platformOpenId}
            </if>
            <if test="firstDeviceId != null and firstDeviceId != ''">
                and first_device_id = #{firstDeviceId}
            </if>
            <if test="semLinkId != null">
                and sem_link_id = #{semLinkId}
            </if>
            <if test="source != null and source != ''">
                and source = #{source}
            </if>
            <if test="pixelId != null and pixelId != ''">
                and pixel_id = #{pixelId}
            </if>
            <if test="adId != null and adId != ''">
                and ad_id = #{adId}
            </if>
        </where>
        order by create_time desc
    </select>

    <select id="selectAppUserById" parameterType="Long" resultMap="AppUserResult">
        <include refid="selectAppUserVo"/>
        where id = #{id}
    </select>

    <select id="selectAppUserByEmail" parameterType="String" resultMap="AppUserResult">
        <include refid="selectAppUserVo"/>
        where email = #{email}
    </select>

    <select id="selectAppUserByPlatformOpenId" parameterType="String" resultMap="AppUserResult">
        <include refid="selectAppUserVo"/>
        where platform_open_id = #{platformOpenId}
    </select>

    <select id="checkEmailUnique" resultType="int">
        select count(1) from app_user
        where email = #{email}
        <if test="id != null">
            and id != #{id}
        </if>
        limit 1
    </select>

    <select id="checkPlatformOpenIdUnique" resultType="int">
        select count(1) from app_user
        where platform_open_id = #{platformOpenId}
        <if test="id != null">
            and id != #{id}
        </if>
        limit 1
    </select>

    <select id="countAppUsers" parameterType="com.ruoyi.common.core.domain.entity.AppUser" resultType="int">
        select count(1) from app_user
        <where>
            <if test="accountType != null and accountType != ''">
                and account_type = #{accountType}
            </if>
            <if test="status != null and status != ''">
                and status = #{status}
            </if>
            <if test="authType != null and authType != ''">
                and auth_type = #{authType}
            </if>
        </where>
    </select>

    <insert id="insertAppUser" parameterType="com.ruoyi.common.core.domain.entity.AppUser">
        insert into app_user
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="userTempId != null and userTempId != ''">user_temp_id,</if>
            <if test="nickname != null and nickname != ''">nickname,</if>
            <if test="avatar != null and avatar != ''">avatar,</if>
            <if test="email != null and email != ''">email,</if>
            <if test="accountType != null and accountType != ''">account_type,</if>
            <if test="status != null and status != ''">status,</if>
            <if test="authType != null and authType != ''">auth_type,</if>
            <if test="platformOpenId != null and platformOpenId != ''">platform_open_id,</if>
            <if test="credential != null and credential != ''">credential,</if>
            <if test="lastLoginIp != null and lastLoginIp != ''">last_login_ip,</if>
            <if test="lastLoginDate != null">last_login_date,</if>
            <if test="firstDeviceId != null and firstDeviceId != ''">first_device_id,</if>
            <if test="semLinkId != null">sem_link_id,</if>
            <if test="source != null and source != ''">source,</if>
            <if test="pixelId != null and pixelId != ''">pixel_id,</if>
            <if test="adId != null and adId != ''">ad_id,</if>
            <if test="adInfo != null and adInfo != ''">ad_info,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="remark != null and remark != ''">remark,</if>
            create_time,
            update_time
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="userTempId != null and userTempId != ''">#{userTempId},</if>
            <if test="nickname != null and nickname != ''">#{nickname},</if>
            <if test="avatar != null and avatar != ''">#{avatar},</if>
            <if test="email != null and email != ''">#{email},</if>
            <if test="accountType != null and accountType != ''">#{accountType},</if>
            <if test="status != null and status != ''">#{status},</if>
            <if test="authType != null and authType != ''">#{authType},</if>
            <if test="platformOpenId != null and platformOpenId != ''">#{platformOpenId},</if>
            <if test="credential != null and credential != ''">#{credential},</if>
            <if test="lastLoginIp != null and lastLoginIp != ''">#{lastLoginIp},</if>
            <if test="lastLoginDate != null">#{lastLoginDate},</if>
            <if test="firstDeviceId != null and firstDeviceId != ''">#{firstDeviceId},</if>
            <if test="semLinkId != null">#{semLinkId},</if>
            <if test="source != null and source != ''">#{source},</if>
            <if test="pixelId != null and pixelId != ''">#{pixelId},</if>
            <if test="adId != null and adId != ''">#{adId},</if>
            <if test="adInfo != null and adInfo != ''">#{adInfo},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="remark != null and remark != ''">#{remark},</if>
            CURRENT_TIMESTAMP(3),
            CURRENT_TIMESTAMP(3)
        </trim>
    </insert>

    <update id="updateAppUser" parameterType="com.ruoyi.common.core.domain.entity.AppUser">
        update app_user
        <trim prefix="SET" suffixOverrides=",">
            <if test="userTempId != null and userTempId != ''">user_temp_id = #{userTempId},</if>
            <if test="nickname != null and nickname != ''">nickname = #{nickname},</if>
            <if test="avatar != null">avatar = #{avatar},</if>
            <if test="email != null">email = #{email},</if>
            <if test="accountType != null and accountType != ''">account_type = #{accountType},</if>
            <if test="status != null and status != ''">status = #{status},</if>
            <if test="authType != null and authType != ''">auth_type = #{authType},</if>
            <if test="platformOpenId != null">platform_open_id = #{platformOpenId},</if>
            <if test="credential != null">credential = #{credential},</if>
            <if test="lastLoginIp != null">last_login_ip = #{lastLoginIp},</if>
            <if test="lastLoginDate != null">last_login_date = #{lastLoginDate},</if>
            <if test="firstDeviceId != null">first_device_id = #{firstDeviceId},</if>
            <if test="semLinkId != null">sem_link_id = #{semLinkId},</if>
            <if test="source != null">source = #{source},</if>
            <if test="pixelId != null">pixel_id = #{pixelId},</if>
            <if test="adId != null">ad_id = #{adId},</if>
            <if test="adInfo != null">ad_info = #{adInfo},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            <if test="remark != null">remark = #{remark},</if>
            update_time = CURRENT_TIMESTAMP(3)
        </trim>
        where id = #{id}
    </update>

    <update id="updateLoginInfo" parameterType="com.ruoyi.common.core.domain.entity.AppUser">
        update app_user set 
            last_login_ip = #{lastLoginIp},
            last_login_date = #{lastLoginDate},
            update_time = CURRENT_TIMESTAMP(3)
        where id = #{id}
    </update>

    <delete id="deleteAppUserById" parameterType="Long">
        delete from app_user where id = #{id}
    </delete>

    <delete id="deleteAppUserByIds" parameterType="Long">
        delete from app_user where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectByPlatformOpenId" parameterType="String" resultMap="AppUserResult">
        <include refid="selectAppUserVo"/>
        where platform_open_id = #{platformOpenId} and auth_type = #{authType}
    </select>

    <select id="selectAppUserByFirstDeviceId" parameterType="String" resultMap="AppUserResult">
        <include refid="selectAppUserVo"/>
        where first_device_id = #{firstDeviceId} order by create_time limit 1
    </select>


</mapper>