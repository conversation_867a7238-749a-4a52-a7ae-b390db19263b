<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.drama.mapper.DramaRoomLanguageConfigMapper">

    <resultMap type="DramaRoomLanguageConfig" id="DramaRoomLanguageConfigResult">
        <result property="id"    column="id"    />
        <result property="name"    column="name"    />
        <result property="zhName"    column="zh_name"    />
        <result property="code"    column="code"    />
        <result property="icon"    column="icon"    />
        <result property="sort"    column="sort"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectDramaRoomLanguageConfigVo">
        select id, name, zh_name, code, icon, sort, create_time, update_time, remark from drama_room_language_config
    </sql>

    <select id="selectDramaRoomLanguageConfigList" parameterType="DramaRoomLanguageConfig" resultMap="DramaRoomLanguageConfigResult">
        <include refid="selectDramaRoomLanguageConfigVo"/>
        <where>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="zhName != null  and zhName != ''"> and zh_name like concat('%', #{zhName}, '%')</if>
            <if test="code != null  and code != ''"> and code = #{code}</if>
            <if test="icon != null  and icon != ''"> and icon = #{icon}</if>
            <if test="sort != null "> and sort = #{sort}</if>
        </where>
    </select>

    <select id="selectDramaRoomLanguageConfigById" parameterType="Long" resultMap="DramaRoomLanguageConfigResult">
        <include refid="selectDramaRoomLanguageConfigVo"/>
        where id = #{id}
    </select>

    <insert id="insertDramaRoomLanguageConfig" parameterType="DramaRoomLanguageConfig" useGeneratedKeys="true" keyProperty="id">
        insert into drama_room_language_config
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null and name != ''">name,</if>
            <if test="zhName != null and zhName != ''">zh_name,</if>
            <if test="code != null and code != ''">code,</if>
            <if test="icon != null and icon != ''">icon,</if>
            <if test="sort != null">sort,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="name != null and name != ''">#{name},</if>
            <if test="zhName != null and zhName != ''">#{zhName},</if>
            <if test="code != null and code != ''">#{code},</if>
            <if test="icon != null and icon != ''">#{icon},</if>
            <if test="sort != null">#{sort},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
    </insert>

    <update id="updateDramaRoomLanguageConfig" parameterType="DramaRoomLanguageConfig">
        update drama_room_language_config
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="zhName != null and zhName != ''">zh_name = #{zhName},</if>
            <if test="code != null and code != ''">code = #{code},</if>
            <if test="icon != null and icon != ''">icon = #{icon},</if>
            <if test="sort != null">sort = #{sort},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteDramaRoomLanguageConfigById" parameterType="Long">
        delete from drama_room_language_config where id = #{id}
    </delete>

    <delete id="deleteDramaRoomLanguageConfigByIds" parameterType="String">
        delete from drama_room_language_config where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>