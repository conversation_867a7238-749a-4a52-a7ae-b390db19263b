<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.drama.mapper.DramaRoomFeedbackMapper">

    <resultMap type="DramaRoomFeedback" id="DramaRoomFeedbackResult">
        <result property="id"    column="id"    />
        <result property="userId"    column="user_id"    />
        <result property="content"    column="content"    />
        <result property="status"    column="status"    />
        <result property="remark"    column="remark"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="email" column="email" />
        <result property="type" column="type" />
    </resultMap>

    <sql id="selectDramaRoomFeedbackVo">
        select id, user_id, content, email, status, remark, create_by, create_time, update_by, update_time,type from drama_room_feedback
    </sql>

    <select id="selectDramaRoomFeedbackList" parameterType="DramaRoomFeedback" resultMap="DramaRoomFeedbackResult">
        <include refid="selectDramaRoomFeedbackVo"/>
        <where>
            <if test="userId != null ">and user_id = #{userId}</if>
            <if test="content != null  and content != ''"> and content = #{content}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="email != null and email != ''">and email = #{email}</if>
            <if test="type != null and type != ''">and type = #{type}</if>
        </where>
    </select>

    <select id="selectDramaRoomFeedbackById" parameterType="Long" resultMap="DramaRoomFeedbackResult">
        <include refid="selectDramaRoomFeedbackVo"/>
        where id = #{id}
    </select>

    <insert id="insertDramaRoomFeedback" parameterType="DramaRoomFeedback" useGeneratedKeys="true" keyProperty="id">
        insert into drama_room_feedback
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">user_id,</if>
            <if test="content != null and content != ''">content,</if>
            <if test="status != null">status,</if>
            <if test="remark != null">remark,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="email != null">email,</if>
            <if test="type != null">type,</if>

        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">#{userId},</if>
            <if test="content != null and content != ''">#{content},</if>
            <if test="status != null">#{status},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="email != null">#{email},</if>
            <if test="type != null">#{type},</if>
        </trim>
    </insert>

    <update id="updateDramaRoomFeedback" parameterType="DramaRoomFeedback">
        update drama_room_feedback
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="content != null and content != ''">content = #{content},</if>
            <if test="status != null">status = #{status},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="email != null">email = #{email},</if>
            <if test="type != null">type = #{type},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteDramaRoomFeedbackById" parameterType="Long">
        delete from drama_room_feedback where id = #{id}
    </delete>

    <delete id="deleteDramaRoomFeedbackByIds" parameterType="String">
        delete from drama_room_feedback where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>