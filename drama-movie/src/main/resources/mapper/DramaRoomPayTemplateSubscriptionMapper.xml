<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.drama.mapper.DramaRoomPayTemplateSubscriptionMapper">

    <resultMap type="DramaRoomPayTemplateSubscription" id="DramaRoomPayTemplateSubscriptionResult">
        <result property="id"    column="id"    />
        <result property="periodType"    column="period_type"    />
        <result property="level"    column="level"    />
        <result property="durationDays"    column="duration_days"    />
        <result property="dailyCoins"    column="daily_coins"    />
        <result property="totalCoins"    column="total_coins"    />
        <result property="displayName"    column="display_name"    />
        <result property="description"    column="description"    />
        <result property="price"    column="price"    />
        <result property="isActive"    column="is_active"    />
        <result property="sortOrder"    column="sort_order"    />
        <result property="appleProductId"    column="apple_product_id"    />
        <result property="googleProductId"    column="google_product_id"    />
        <result property="remark"    column="remark"    />
        <result property="createTime"    column="create_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="updateBy"    column="update_by"    />
    </resultMap>

    <sql id="selectDramaRoomPayTemplateSubscriptionVo">
        select id, period_type, level, duration_days, daily_coins, total_coins, daily_send_coins, total_send_coins, display_name, description, price, is_active, sort_order, apple_product_id, google_product_id, remark, create_time, create_by, update_time, update_by from drama_room_pay_template_subscription
    </sql>

    <select id="selectDramaRoomPayTemplateSubscriptionList" parameterType="DramaRoomPayTemplateSubscription" resultMap="DramaRoomPayTemplateSubscriptionResult">
        <include refid="selectDramaRoomPayTemplateSubscriptionVo"/>
        <where>
            <if test="periodType != null  and periodType != ''"> and period_type = #{periodType}</if>
            <if test="level != null  and level != ''"> and level = #{level}</if>
            <if test="durationDays != null  and durationDays != ''"> and duration_days = #{durationDays}</if>
            <if test="dailyCoins != null  and dailyCoins != ''"> and daily_coins = #{dailyCoins}</if>
            <if test="totalCoins != null  and totalCoins != ''"> and total_coins = #{totalCoins}</if>
            <if test="displayName != null  and displayName != ''"> and display_name like concat('%', #{displayName}, '%')</if>
            <if test="description != null  and description != ''"> and description = #{description}</if>
            <if test="price != null "> and price = #{price}</if>
            <if test="isActive != null "> and is_active = #{isActive}</if>
            <if test="sortOrder != null  and sortOrder != ''"> and sort_order = #{sortOrder}</if>
            <if test="appleProductId != null  and appleProductId != ''"> and apple_product_id = #{appleProductId}</if>
            <if test="googleProductId != null  and googleProductId != ''"> and google_product_id = #{googleProductId}</if>
        </where>
    </select>

    <select id="selectDramaRoomPayTemplateSubscriptionById" parameterType="String" resultMap="DramaRoomPayTemplateSubscriptionResult">
        <include refid="selectDramaRoomPayTemplateSubscriptionVo"/>
        where id = #{id}
    </select>

    <insert id="insertDramaRoomPayTemplateSubscription" parameterType="DramaRoomPayTemplateSubscription" useGeneratedKeys="true" keyProperty="id">
        insert into drama_room_pay_template_subscription
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="periodType != null and periodType != ''">period_type,</if>
            <if test="level != null and level != ''">level,</if>
            <if test="durationDays != null and durationDays != ''">duration_days,</if>
            <if test="dailyCoins != null and dailyCoins != ''">daily_coins,</if>
            <if test="totalCoins != null and totalCoins != ''">total_coins,</if>
            <if test="displayName != null and displayName != ''">display_name,</if>
            <if test="description != null">description,</if>
            <if test="price != null">price,</if>
            <if test="isActive != null">is_active,</if>
            <if test="sortOrder != null and sortOrder != ''">sort_order,</if>
            <if test="appleProductId != null">apple_product_id,</if>
            <if test="googleProductId != null">google_product_id,</if>
            <if test="remark != null">remark,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null">update_by,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="periodType != null and periodType != ''">#{periodType},</if>
            <if test="level != null and level != ''">#{level},</if>
            <if test="durationDays != null and durationDays != ''">#{durationDays},</if>
            <if test="dailyCoins != null and dailyCoins != ''">#{dailyCoins},</if>
            <if test="totalCoins != null and totalCoins != ''">#{totalCoins},</if>
            <if test="displayName != null and displayName != ''">#{displayName},</if>
            <if test="description != null">#{description},</if>
            <if test="price != null">#{price},</if>
            <if test="isActive != null">#{isActive},</if>
            <if test="sortOrder != null and sortOrder != ''">#{sortOrder},</if>
            <if test="appleProductId != null">#{appleProductId},</if>
            <if test="googleProductId != null">#{googleProductId},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
        </trim>
    </insert>

    <update id="updateDramaRoomPayTemplateSubscription" parameterType="DramaRoomPayTemplateSubscription">
        update drama_room_pay_template_subscription
        <trim prefix="SET" suffixOverrides=",">
            <if test="periodType != null and periodType != ''">period_type = #{periodType},</if>
            <if test="level != null and level != ''">level = #{level},</if>
            <if test="durationDays != null and durationDays != ''">duration_days = #{durationDays},</if>
            <if test="dailyCoins != null and dailyCoins != ''">daily_coins = #{dailyCoins},</if>
            <if test="totalCoins != null and totalCoins != ''">total_coins = #{totalCoins},</if>
            <if test="displayName != null and displayName != ''">display_name = #{displayName},</if>
            <if test="description != null">description = #{description},</if>
            <if test="price != null">price = #{price},</if>
            <if test="isActive != null">is_active = #{isActive},</if>
            <if test="sortOrder != null and sortOrder != ''">sort_order = #{sortOrder},</if>
            <if test="appleProductId != null">apple_product_id = #{appleProductId},</if>
            <if test="googleProductId != null">google_product_id = #{googleProductId},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteDramaRoomPayTemplateSubscriptionById" parameterType="String">
        delete from drama_room_pay_template_subscription where id = #{id}
    </delete>

    <delete id="deleteDramaRoomPayTemplateSubscriptionByIds" parameterType="String">
        delete from drama_room_pay_template_subscription where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>