<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.drama.mapper.IapMapper">

    <resultMap id="DramaRoomPayTemplateAmtVO"
               type="com.ruoyi.common.drama.vo.DramaRoomPayTemplateAmtVO">
        <id property="id" column="id"/>
        <result property="price" column="price"/>
        <result property="coin" column="coin"/>
        <result property="appleProductId" column="apple_product_id"/>
        <result property="googleProductId" column="google_product_id"/>
    </resultMap>

    <resultMap id="DramaRoomPayTemplateSubscriptionVO"
               type="com.ruoyi.common.drama.vo.DramaRoomPayTemplateSubscriptionVO">
        <id property="id" column="id"/>
        <result property="periodType" column="period_type"/>
        <result property="level" column="level"/>
        <result property="durationDays" column="duration_days"/>
        <result property="dailyCoins" column="daily_coins"/>
        <result property="totalCoins" column="total_coins"/>
        <result property="dailySendCoins" column="daily_send_coins"/>
        <result property="totalSendCoins" column="total_send_coins"/>
        <result property="displayName" column="display_name"/>
        <result property="description" column="description"/>
        <result property="price" column="price"/>
        <result property="appleProductId" column="apple_product_id"/>
        <result property="googleProductId" column="google_product_id"/>
    </resultMap>

    <select id="selectAmtByPayTemplateId" resultMap="DramaRoomPayTemplateAmtVO">
        select a.*
        from drama_room_pay_template_amt a
                 left JOIN drama_room_vip v on a.id = v.pay_template_amount_id
        where v.pay_template_id = #{payTemplateId}
          and v.pay_type = 0
          and v.`status` = 1
    </select>

    <select id="selectSubscriptionByPayTemplateId"
            resultMap="DramaRoomPayTemplateSubscriptionVO">
        select a.*
        from drama_room_pay_template_subscription a
                 left JOIN drama_room_vip v on a.id = v.pay_template_subscription_id
        where v.pay_template_id = #{payTemplateId}
          and v.pay_type = 1
          and v.`status` = 1
    </select>
</mapper>