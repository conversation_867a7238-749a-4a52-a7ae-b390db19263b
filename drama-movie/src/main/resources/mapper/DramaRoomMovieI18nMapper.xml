<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.drama.mapper.DramaRoomMovieI18nMapper">

    <resultMap type="DramaRoomMovieI18n" id="DramaRoomMovieI18nResult">
        <result property="id"    column="id"    />
        <result property="movieId"    column="movie_id"    />
        <result property="languageCode"    column="language_code"    />
        <result property="title"    column="title"    />
        <result property="description"    column="description"    />
        <result property="coverImage"    column="cover_image"    />
        <result property="director"    column="director"    />
        <result property="actors"    column="actors"    />
        <result property="status"    column="status"    />
        <result property="remark"    column="remark"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectDramaRoomMovieI18nVo">
        select id, movie_id, language_code, title, description, cover_image, director, actors, status, remark, create_by, create_time, update_by, update_time from drama_room_movie_i18n
    </sql>

    <select id="selectDramaRoomMovieI18nList" parameterType="DramaRoomMovieI18n" resultMap="DramaRoomMovieI18nResult">
        <include refid="selectDramaRoomMovieI18nVo"/>
        <where>
            <if test="movieId != null "> and movie_id = #{movieId}</if>
            <if test="languageCode != null  and languageCode != ''"> and language_code = #{languageCode}</if>
            <if test="title != null  and title != ''"> and title = #{title}</if>
            <if test="description != null  and description != ''"> and description = #{description}</if>
            <if test="coverImage != null  and coverImage != ''"> and cover_image = #{coverImage}</if>
            <if test="director != null  and director != ''"> and director = #{director}</if>
            <if test="actors != null  and actors != ''"> and actors = #{actors}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
        </where>
    </select>

    <select id="selectDramaRoomMovieI18nById" parameterType="Long" resultMap="DramaRoomMovieI18nResult">
        <include refid="selectDramaRoomMovieI18nVo"/>
        where id = #{id}
    </select>
    <select id="selectDramaRoomMovieI18nByMovieIdList" resultMap="DramaRoomMovieI18nResult">
        <include refid="selectDramaRoomMovieI18nVo"/>
        where movie_id in
        <foreach item="item" collection="list" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>
    <select id="selectDramaRoomMovieI18nByMovieIdListAndLanguageCode" resultMap="DramaRoomMovieI18nResult">
        <include refid="selectDramaRoomMovieI18nVo"/>
        where status = '1' and movie_id in
        <foreach item="item" collection="list" separator="," open="(" close=")">
            #{item}
        </foreach>
        <if test="languageCode != null and languageCode != ''">and language_code = #{languageCode}</if>
    </select>
    <select id="selectByMovieName" resultMap="DramaRoomMovieI18nResult">
        <include refid="selectDramaRoomMovieI18nVo"/>  where status = '1' and  title like concat('%',#{name},'%')
    </select>

    <insert id="insertDramaRoomMovieI18n" parameterType="DramaRoomMovieI18n" useGeneratedKeys="true" keyProperty="id">
        insert into drama_room_movie_i18n
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="movieId != null">movie_id,</if>
            <if test="languageCode != null and languageCode != ''">language_code,</if>
            <if test="title != null and title != ''">title,</if>
            <if test="description != null">description,</if>
            <if test="coverImage != null">cover_image,</if>
            <if test="director != null">director,</if>
            <if test="actors != null">actors,</if>
            <if test="status != null and status != ''">status,</if>
            <if test="remark != null">remark,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="movieId != null">#{movieId},</if>
            <if test="languageCode != null and languageCode != ''">#{languageCode},</if>
            <if test="title != null and title != ''">#{title},</if>
            <if test="description != null">#{description},</if>
            <if test="coverImage != null">#{coverImage},</if>
            <if test="director != null">#{director},</if>
            <if test="actors != null">#{actors},</if>
            <if test="status != null and status != ''">#{status},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updateDramaRoomMovieI18n" parameterType="DramaRoomMovieI18n">
        update drama_room_movie_i18n
        <trim prefix="SET" suffixOverrides=",">
            <if test="movieId != null">movie_id = #{movieId},</if>
            <if test="languageCode != null and languageCode != ''">language_code = #{languageCode},</if>
            <if test="title != null and title != ''">title = #{title},</if>
            <if test="description != null">description = #{description},</if>
            <if test="coverImage != null">cover_image = #{coverImage},</if>
            <if test="director != null">director = #{director},</if>
            <if test="actors != null">actors = #{actors},</if>
            <if test="status != null and status != ''">status = #{status},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteDramaRoomMovieI18nById" parameterType="Long">
        delete from drama_room_movie_i18n where id = #{id}
    </delete>

    <delete id="deleteDramaRoomMovieI18nByIds" parameterType="String">
        delete from drama_room_movie_i18n where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    <delete id="deleteMovieI18n">
        delete from drama_room_movie_i18n where movie_id = #{movieId} and language_code = #{languageCode}
    </delete>
</mapper>