<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.drama.mapper.DramaRoomTransactionRecordMapper">

    <resultMap type="DramaRoomTransactionRecord" id="DramaRoomTransactionRecordResult">
        <result property="id"    column="id"    />
        <result property="orderId"    column="order_id"    />
        <result property="transNo"    column="trans_no"    />
        <result property="userId"    column="user_id"    />
        <result property="channelCode"    column="channel_code"    />
        <result property="amount"    column="amount"    />
        <result property="type"    column="type"    />
        <result property="status"    column="status"    />
        <result property="transactionId"    column="transaction_id"    />
        <result property="method"    column="method"    />
        <result property="reason"    column="reason"    />
        <result property="extra"    column="extra"    />
        <result property="remark"    column="remark"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectDramaRoomTransactionRecordVo">
        select id, order_id, trans_no, user_id, channel_code, amount, type, status, transaction_id, method, reason, extra, remark, create_by, create_time, update_by, update_time from drama_room_transaction_record
    </sql>

    <select id="selectDramaRoomTransactionRecordList" parameterType="DramaRoomTransactionRecord" resultMap="DramaRoomTransactionRecordResult">
        <include refid="selectDramaRoomTransactionRecordVo"/>
        <where>
            <if test="orderId != null "> and order_id = #{orderId}</if>
            <if test="transNo != null  and transNo != ''"> and trans_no = #{transNo}</if>
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="channelCode != null  and channelCode != ''"> and channel_code = #{channelCode}</if>
            <if test="amount != null "> and amount = #{amount}</if>
            <if test="type != null "> and type = #{type}</if>
            <if test="status != null "> and status = #{status}</if>
            <if test="transactionId != null  and transactionId != ''"> and transaction_id = #{transactionId}</if>
            <if test="method != null  and method != ''"> and method = #{method}</if>
            <if test="reason != null  and reason != ''"> and reason = #{reason}</if>
            <if test="extra != null  and extra != ''"> and extra = #{extra}</if>
        </where>
    </select>

    <select id="selectDramaRoomTransactionRecordById" parameterType="Long" resultMap="DramaRoomTransactionRecordResult">
        <include refid="selectDramaRoomTransactionRecordVo"/>
        where id = #{id}
    </select>

    <insert id="insertDramaRoomTransactionRecord" parameterType="DramaRoomTransactionRecord" useGeneratedKeys="true" keyProperty="id">
        insert into drama_room_transaction_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="orderId != null">order_id,</if>
            <if test="transNo != null and transNo != ''">trans_no,</if>
            <if test="userId != null">user_id,</if>
            <if test="channelCode != null and channelCode != ''">channel_code,</if>
            <if test="amount != null">amount,</if>
            <if test="type != null">type,</if>
            <if test="status != null">status,</if>
            <if test="transactionId != null">transaction_id,</if>
            <if test="method != null">method,</if>
            <if test="reason != null">reason,</if>
            <if test="extra != null">extra,</if>
            <if test="remark != null">remark,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="orderId != null">#{orderId},</if>
            <if test="transNo != null and transNo != ''">#{transNo},</if>
            <if test="userId != null">#{userId},</if>
            <if test="channelCode != null and channelCode != ''">#{channelCode},</if>
            <if test="amount != null">#{amount},</if>
            <if test="type != null">#{type},</if>
            <if test="status != null">#{status},</if>
            <if test="transactionId != null">#{transactionId},</if>
            <if test="method != null">#{method},</if>
            <if test="reason != null">#{reason},</if>
            <if test="extra != null">#{extra},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updateDramaRoomTransactionRecord" parameterType="DramaRoomTransactionRecord">
        update drama_room_transaction_record
        <trim prefix="SET" suffixOverrides=",">
            <if test="orderId != null">order_id = #{orderId},</if>
            <if test="transNo != null and transNo != ''">trans_no = #{transNo},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="channelCode != null and channelCode != ''">channel_code = #{channelCode},</if>
            <if test="amount != null">amount = #{amount},</if>
            <if test="type != null">type = #{type},</if>
            <if test="status != null">status = #{status},</if>
            <if test="transactionId != null">transaction_id = #{transactionId},</if>
            <if test="method != null">method = #{method},</if>
            <if test="reason != null">reason = #{reason},</if>
            <if test="extra != null">extra = #{extra},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteDramaRoomTransactionRecordById" parameterType="Long">
        delete from drama_room_transaction_record where id = #{id}
    </delete>

    <delete id="deleteDramaRoomTransactionRecordByIds" parameterType="String">
        delete from drama_room_transaction_record where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>