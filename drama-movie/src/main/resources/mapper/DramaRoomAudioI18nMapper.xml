<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.drama.mapper.DramaRoomAudioI18nMapper">

    <resultMap type="DramaRoomAudioI18n" id="DramaRoomAudioI18nResult">
        <result property="id"    column="id"    />
        <result property="audioId"    column="audio_id"    />
        <result property="audio"    column="audio"    />
        <result property="languageCode"    column="language_code"    />
        <result property="createTime"    column="create_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectDramaRoomAudioI18nVo">
        select id, audio_id, audio, language_code, create_time, create_by, update_time, update_by, remark from drama_room_audio_i18n
    </sql>

    <select id="selectDramaRoomAudioI18nList" parameterType="DramaRoomAudioI18n" resultMap="DramaRoomAudioI18nResult">
        <include refid="selectDramaRoomAudioI18nVo"/>
        <where>
            <if test="audioId != null "> and audio_id = #{audioId}</if>
            <if test="audio != null  and audio != ''"> and audio = #{audio}</if>
            <if test="languageCode != null  and languageCode != ''"> and language_code = #{languageCode}</if>
        </where>
    </select>

    <select id="selectDramaRoomAudioI18nById" parameterType="Long" resultMap="DramaRoomAudioI18nResult">
        <include refid="selectDramaRoomAudioI18nVo"/>
        where id = #{id}
    </select>

    <select id="selectDramaRoomAudioI18nByAudioIds" resultMap="DramaRoomAudioI18nResult">
        <include refid="selectDramaRoomAudioI18nVo"/>
        where audio_id in
        <foreach item="id" collection="audioIds" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <insert id="insertDramaRoomAudioI18n" parameterType="DramaRoomAudioI18n" useGeneratedKeys="true" keyProperty="id">
        insert into drama_room_audio_i18n
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="audioId != null">audio_id,</if>
            <if test="audio != null">audio,</if>
            <if test="languageCode != null">language_code,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="audioId != null">#{audioId},</if>
            <if test="audio != null">#{audio},</if>
            <if test="languageCode != null">#{languageCode},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
    </insert>

    <insert id="insertDramaRoomAudioI18nList">
        insert into drama_room_audio_i18n (audio_id, audio, language_code, create_time, create_by, update_time, update_by, remark) values
        <foreach item="item" index="index" collection="i18ns" separator=",">
            (#{item.audioId}, #{item.audio}, #{item.languageCode}, #{item.createTime}, #{item.createBy}, #{item.updateTime}, #{item.updateBy}, #{item.remark})
        </foreach>
    </insert>

    <update id="updateDramaRoomAudioI18n" parameterType="DramaRoomAudioI18n">
        update drama_room_audio_i18n
        <trim prefix="SET" suffixOverrides=",">
            <if test="audioId != null">audio_id = #{audioId},</if>
            <if test="audio != null">audio = #{audio},</if>
            <if test="languageCode != null">language_code = #{languageCode},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteDramaRoomAudioI18nById" parameterType="Long">
        delete from drama_room_audio_i18n where id = #{id}
    </delete>

    <delete id="deleteDramaRoomAudioI18nByIds" parameterType="String">
        delete from drama_room_audio_i18n where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    <delete id="deleteDramaRoomAudioI18nByAudioIds">
        delete from drama_room_audio_i18n where audio_id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    <delete id="deleteDramaRoomAudioI18nByAudioId">
        delete from drama_room_audio_i18n where audio_id = #{id}
    </delete>
</mapper>