<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.drama.mapper.DramaRoomSignInRuleMapper">

    <resultMap type="DramaRoomSignInRule" id="DramaRoomSignInRuleResult">
        <result property="id"    column="id"    />
        <result property="type"    column="type"    />
        <result property="name"    column="name"    />
        <result property="status"    column="status"    />
        <result property="remark"    column="remark"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="icon"    column="icon"    />
        <result property="jumpUrl"    column="jump_url"    />

    </resultMap>

    <sql id="selectDramaRoomSignInRuleVo">
        select id, type, name, status, remark, create_by, create_time, update_by, update_time,icon,jump_url from drama_room_sign_in_rule
    </sql>

    <select id="selectDramaRoomSignInRuleList" parameterType="DramaRoomSignInRule" resultMap="DramaRoomSignInRuleResult">
        <include refid="selectDramaRoomSignInRuleVo"/>
        <where>
            <if test="type != null "> and type = #{type}</if>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
        </where>
    </select>

    <select id="selectDramaRoomSignInRuleById" parameterType="Long" resultMap="DramaRoomSignInRuleResult">
        <include refid="selectDramaRoomSignInRuleVo"/>
        where id = #{id}
    </select>

    <insert id="insertDramaRoomSignInRule" parameterType="DramaRoomSignInRule" useGeneratedKeys="true" keyProperty="id">
        insert into drama_room_sign_in_rule
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="type != null">type,</if>
            <if test="name != null">name,</if>
            <if test="status != null and status != ''">status,</if>
            <if test="remark != null">remark,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="icon != null">icon,</if>
            <if test="jumpUrl != null">jump_url,</if>

        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="type != null">#{type},</if>
            <if test="name != null">#{name},</if>
            <if test="status != null and status != ''">#{status},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="icon != null">#{icon},</if>
            <if test="jumpUrl != null">#{jumpUrl},</if>
        </trim>
    </insert>

    <update id="updateDramaRoomSignInRule" parameterType="DramaRoomSignInRule">
        update drama_room_sign_in_rule
        <trim prefix="SET" suffixOverrides=",">
            <if test="type != null">type = #{type},</if>
            <if test="name != null">name = #{name},</if>
            <if test="status != null and status != ''">status = #{status},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="icon != null">update_time = #{icon},</if>
            <if test="jumpUrl != null">jump_url = #{jumpUrl},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteDramaRoomSignInRuleById" parameterType="Long">
        delete from drama_room_sign_in_rule where id = #{id}
    </delete>

    <delete id="deleteDramaRoomSignInRuleByIds" parameterType="String">
        delete from drama_room_sign_in_rule where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>