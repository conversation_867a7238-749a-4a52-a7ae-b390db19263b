<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.drama.mapper.DramaRoomVideoChannelCoinMapper">

    <resultMap type="DramaRoomVideoChannelCoin" id="DramaRoomVideoChannelCoinResult">
        <result property="id"    column="id"    />
        <result property="coin"    column="coin"    />
        <result property="coinRuleId"    column="coin_rule_id"    />
        <result property="movieId"    column="movie_id"    />
        <result property="videoId"    column="video_id"    />
        <result property="num"    column="num"    />
        <result property="state"    column="state"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectDramaRoomVideoChannelCoinVo">
        select id, coin, coin_rule_id, movie_id, video_id, num, state, create_by, create_time, update_by, update_time, remark from drama_room_video_channel_coin
    </sql>

    <select id="selectDramaRoomVideoChannelCoinList" parameterType="DramaRoomVideoChannelCoin" resultMap="DramaRoomVideoChannelCoinResult">
        <include refid="selectDramaRoomVideoChannelCoinVo"/>
        <where>
            <if test="coin != null "> and coin = #{coin}</if>
            <if test="coinRuleId != null "> and coin_rule_id = #{coinRuleId}</if>
            <if test="movieId != null "> and movie_id = #{movieId}</if>
            <if test="videoId != null "> and video_id = #{videoId}</if>
            <if test="num != null "> and num = #{num}</if>
            <if test="state != null  and state != ''"> and state = #{state}</if>
        </where>
    </select>

    <select id="selectDramaRoomVideoChannelCoinById" parameterType="Long" resultMap="DramaRoomVideoChannelCoinResult">
        <include refid="selectDramaRoomVideoChannelCoinVo"/>
        where id = #{id}
    </select>

    <select id="selectDramaRoomVideoChannelCoinByCoinRuleIdAndMovieIdAndVideoId" parameterType="DramaRoomVideoChannelCoin" resultMap="DramaRoomVideoChannelCoinResult">
        <include refid="selectDramaRoomVideoChannelCoinVo"/>
        where coin_rule_id = #{coinRuleId} and movie_id = #{movieId} and video_id = #{videoId} limit 1
    </select>

    <select id="selectDramaRoomVideoChannelCoinByCoinRuleIdAndMovieId" parameterType="DramaRoomVideoChannelCoin" resultMap="DramaRoomVideoChannelCoinResult">
        <include refid="selectDramaRoomVideoChannelCoinVo"/>
        where coin_rule_id = #{coinRuleId} and movie_id = #{movieId}
    </select>

    <select id="selectDramaRoomVideoChannelCoinByCoinRuleIdAndMovieI" parameterType="DramaRoomVideoChannelCoin" resultMap="DramaRoomVideoChannelCoinResult">
        <include refid="selectDramaRoomVideoChannelCoinVo"/>
        where coin_rule_id = #{coinRuleId} and movie_id = #{movieId}
    </select>

    <select id="selectDramaRoomVideoChannelCoinByMovieId" parameterType="DramaRoomVideoChannelCoin" resultMap="DramaRoomVideoChannelCoinResult">
        <include refid="selectDramaRoomVideoChannelCoinVo"/>
        where movie_id = #{movieId}
    </select>

    <insert id="insertDramaRoomVideoChannelCoin" parameterType="DramaRoomVideoChannelCoin" useGeneratedKeys="true" keyProperty="id">
        insert into drama_room_video_channel_coin
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="coin != null">coin,</if>
            <if test="coinRuleId != null">coin_rule_id,</if>
            <if test="movieId != null">movie_id,</if>
            <if test="videoId != null">video_id,</if>
            <if test="num != null">num,</if>
            <if test="state != null">state,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="coin != null">#{coin},</if>
            <if test="coinRuleId != null">#{coinRuleId},</if>
            <if test="movieId != null">#{movieId},</if>
            <if test="videoId != null">#{videoId},</if>
            <if test="num != null">#{num},</if>
            <if test="state != null">#{state},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
    </insert>
    <insert id="insertDramaRoomVideoChannelCoinBatch">
        insert into drama_room_video_channel_coin (coin, coin_rule_id, movie_id, video_id, num, state, create_by, create_time, update_by, update_time, remark) values
        <foreach item="item" index="index" collection="list" separator=",">
            (#{item.coin}, #{item.coinRuleId}, #{item.movieId}, #{item.videoId}, #{item.num}, #{item.state}, #{item.createBy}, #{item.createTime}, #{item.updateBy}, #{item.updateTime}, #{item.remark})
        </foreach>
    </insert>

    <update id="updateDramaRoomVideoChannelCoin" parameterType="DramaRoomVideoChannelCoin">
        update drama_room_video_channel_coin
        <trim prefix="SET" suffixOverrides=",">
            <if test="coin != null">coin = #{coin},</if>
            <if test="coinRuleId != null">coin_rule_id = #{coinRuleId},</if>
            <if test="movieId != null">movie_id = #{movieId},</if>
            <if test="videoId != null">video_id = #{videoId},</if>
            <if test="num != null">num = #{num},</if>
            <if test="state != null">state = #{state},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>
    <update id="updateDramaRoomVideoChannelCoinBatch">
        <foreach item="item" index="index" collection="list" separator=";">
            update drama_room_video_channel_coin
            <trim prefix="SET" suffixOverrides=",">
                <if test="item.coin != null">coin = #{item.coin},</if>
                <if test="item.coinRuleId != null">coin_rule_id = #{item.coinRuleId},</if>
                <if test="item.movieId != null">movie_id = #{item.movieId},</if>
                <if test="item.videoId != null">video_id = #{item.videoId},</if>
                <if test="item.num != null">num = #{item.num},</if>
            </trim>
        where id = #{item.id}
        </foreach>
    </update>

    <delete id="deleteDramaRoomVideoChannelCoinById" parameterType="Long">
        delete from drama_room_video_channel_coin where id = #{id}
    </delete>

    <delete id="deleteDramaRoomVideoChannelCoinByIds" parameterType="String">
        delete from drama_room_video_channel_coin where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
