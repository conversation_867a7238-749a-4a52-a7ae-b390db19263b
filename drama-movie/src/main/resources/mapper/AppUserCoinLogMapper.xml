<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.drama.mapper.AppUserCoinLogMapper">

    <resultMap type="AppUserCoinLog" id="AppUserCoinLogResult">
        <result property="id"    column="id"    />
        <result property="type"    column="type"    />
        <result property="userId"    column="user_id"    />
        <result property="coins"    column="coins"    />
        <result property="beforeCoins"    column="before_coins"    />
        <result property="afterCoins"    column="after_coins"    />
        <result property="content"    column="content"    />
        <result property="status"    column="status"    />
        <result property="remark"    column="remark"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="sourceId"    column="source_id"    />
        <result property="tradeType"    column="trade_type"    />
        <result property="incomeType"    column="income_type"    />
    </resultMap>

    <sql id="selectAppUserCoinLogVo">
        select id, type, user_id, coins, before_coins, after_coins, content, status, remark, create_by, create_time, update_by, update_time,source_id, trade_type, income_type from app_user_coin_log
    </sql>

    <select id="selectAppUserCoinLogList" parameterType="AppUserCoinLog" resultMap="AppUserCoinLogResult">
        <include refid="selectAppUserCoinLogVo"/>
        <where>
            <if test="type != null "> and type = #{type}</if>
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="coins != null "> and coins = #{coins}</if>
            <if test="beforeCoins != null "> and before_coins = #{beforeCoins}</if>
            <if test="afterCoins != null "> and after_coins = #{afterCoins}</if>
            <if test="content != null  and content != ''"> and content = #{content}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="sourceId != null  and sourceId != ''"> and source_id = #{sourceId}</if>
            <if test="tradeType != null "> and trade_type = #{tradeType}</if>
            <if test="incomeType != null "> and income_type = #{incomeType}</if>
        </where>

    </select>

    <select id="selectAppUserCoinLogById" parameterType="Long" resultMap="AppUserCoinLogResult">
        <include refid="selectAppUserCoinLogVo"/>
        where id = #{id}
    </select>

    <select id="selectUserCoinLogByUserIdAndTypeIncomeType" resultMap="AppUserCoinLogResult">
        <include refid="selectAppUserCoinLogVo"/>
        where
            user_id = #{userId} and income_type = #{incomeType}
        ORDER BY
            create_time DESC
    </select>

    <select id="getSignInByUserId" resultType="com.ruoyi.common.drama.vo.UserCoinLogVO">
        SELECT
        @user_id as userId,
        CASE
        WHEN DATE(DATE_ADD(@week_start, INTERVAL d.days - 1 DAY)) = CURRENT_DATE() THEN
        'Today'
        ELSE
        DATE_FORMAT(DATE_ADD(@week_start, INTERVAL d.days - 1 DAY), '%m.%d')
        END AS dateTime,
        CASE
        WHEN s.id IS NOT NULL THEN '1' -- 已签到
        WHEN s.id IS NULL AND d.days &lt;= @current_weekday THEN '2' -- 漏签（过去的日子）
        ELSE '0' -- 未签到（未来的日子）
        END AS signStatus,
        r.num AS coins,
        d.days,
        r.id as signInRuleInfoId
        FROM
        (SELECT 1 AS days UNION SELECT 2 UNION SELECT 3 UNION SELECT 4
        UNION SELECT 5 UNION SELECT 6 UNION SELECT 7) d
        CROSS JOIN (SELECT @user_id := #{userId}) user_param
        CROSS JOIN (SELECT
        @week_start := DATE_SUB(CURRENT_DATE(), INTERVAL WEEKDAY(CURRENT_DATE()) DAY),
        @current_weekday := WEEKDAY(CURRENT_DATE()) + 1
        ) week_params
        LEFT JOIN app_user_coin_log s ON s.user_id = @user_id
        AND s.type = 2
        AND YEARWEEK(s.create_time, 1) = YEARWEEK(CURRENT_DATE(), 1)
        AND WEEKDAY(s.create_time) + 1 = d.days
        LEFT JOIN drama_room_sign_in_rule_info r ON r.days = d.days
        AND r.sign_in_rule_id = 1
        AND r.STATUS = 1
        ORDER BY d.days
    </select>
    <select id="selectByTypeAndUserIdAndSourceId" resultType="java.lang.Integer">
        SELECT
            COUNT(log.id)
        FROM
            app_user_coin_log log
                INNER JOIN drama_room_sign_in_rule_info info ON log.source_id = info.id
                INNER JOIN drama_room_sign_in_rule rule ON rule.id = info.sign_in_rule_id
        WHERE
            log.user_id = #{userId}
          AND rule.type = #{type}
          AND log.source_id = #{signInRuleInfoId}
          <if test="type != 2">
            AND DATE(log.create_time) = CURRENT_DATE()
          </if>
    </select>

    <insert id="insertAppUserCoinLog" parameterType="AppUserCoinLog" useGeneratedKeys="true" keyProperty="id">
        insert into app_user_coin_log
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="type != null">type,</if>
            <if test="userId != null">user_id,</if>
            <if test="coins != null">coins,</if>
            <if test="beforeCoins != null">before_coins,</if>
            <if test="afterCoins != null">after_coins,</if>
            <if test="content != null">content,</if>
            <if test="status != null and status != ''">status,</if>
            <if test="remark != null">remark,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="sourceId != null">source_id,</if>
            <if test="tradeType != null">trade_type,</if>
            <if test="incomeType != null">income_type,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="type != null">#{type},</if>
            <if test="userId != null">#{userId},</if>
            <if test="coins != null">#{coins},</if>
            <if test="beforeCoins != null">#{beforeCoins},</if>
            <if test="afterCoins != null">#{afterCoins},</if>
            <if test="content != null">#{content},</if>
            <if test="status != null and status != ''">#{status},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="sourceId != null">#{sourceId},</if>
            <if test="tradeType != null">#{tradeType},</if>
            <if test="incomeType != null">#{incomeType},</if>
        </trim>
    </insert>

    <update id="updateAppUserCoinLog" parameterType="AppUserCoinLog">
        update app_user_coin_log
        <trim prefix="SET" suffixOverrides=",">
            <if test="type != null">type = #{type},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="coins != null">coins = #{coins},</if>
            <if test="beforeCoins != null">before_coins = #{beforeCoins},</if>
            <if test="afterCoins != null">after_coins = #{afterCoins},</if>
            <if test="content != null">content = #{content},</if>
            <if test="status != null and status != ''">status = #{status},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="sourceId != null">source_id = #{sourceId},</if>
            <if test="tradeType != null">trade_type = #{tradeType},</if>
            <if test="incomeType != null">income_type = #{incomeType},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteAppUserCoinLogById" parameterType="Long">
        delete from app_user_coin_log where id = #{id}
    </delete>

    <delete id="deleteAppUserCoinLogByIds" parameterType="String">
        delete from app_user_coin_log where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>