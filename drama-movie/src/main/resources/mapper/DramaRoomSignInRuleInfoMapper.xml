<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.drama.mapper.DramaRoomSignInRuleInfoMapper">

    <resultMap type="DramaRoomSignInRuleInfo" id="DramaRoomSignInRuleInfoResult">
        <result property="id"    column="id"    />
        <result property="signInRuleId"    column="sign_in_rule_id"    />
        <result property="days"    column="days"    />
        <result property="num"    column="num"    />
        <result property="status"    column="status"    />
        <result property="remark"    column="remark"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectDramaRoomSignInRuleInfoVo">
        select id, sign_in_rule_id, days, num, status, remark, create_by, create_time, update_by, update_time from drama_room_sign_in_rule_info
    </sql>

    <select id="selectDramaRoomSignInRuleInfoList" parameterType="DramaRoomSignInRuleInfo" resultMap="DramaRoomSignInRuleInfoResult">
        <include refid="selectDramaRoomSignInRuleInfoVo"/>
        <where>
            <if test="signInRuleId != null "> and sign_in_rule_id = #{signInRuleId}</if>
            <if test="days != null "> and days = #{days}</if>
            <if test="num != null "> and num = #{num}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
        </where>
    </select>

    <select id="selectDramaRoomSignInRuleInfoById" parameterType="Long" resultMap="DramaRoomSignInRuleInfoResult">
        <include refid="selectDramaRoomSignInRuleInfoVo"/>
        where id = #{id}
    </select>
    <select id="getUserBenefits" resultType="com.ruoyi.common.drama.vo.SignInRuleVO">
        SELECT
            sr.`name` name,
            sr.type type,
            si.num num,
            sr.icon icon,
            si.id signInRuleInfoId,
            sr.jump_url jumpUrl,
            CASE WHEN au.id IS NOT NULL THEN 1 ELSE 0 END AS signStatus,
            si.days minutes
        FROM
            drama_room_sign_in_rule sr
                LEFT JOIN drama_room_sign_in_rule_info si ON sr.id = si.sign_in_rule_id
                LEFT JOIN app_user_coin_log au ON au.source_id = si.id AND au.user_id = #{appUserId}
                <if test="type != 2">
                AND DATE(au.create_time) = CURRENT_DATE()
                </if>
        WHERE
            sr.`status` = 1
          AND si.`status` = 1
          AND sr.type = #{type}
    </select>
    <select id="getByRuleType" resultType="com.ruoyi.drama.domain.DramaRoomSignInRuleInfo">
        SELECT
            *
        FROM
            drama_room_sign_in_rule_info info
                INNER JOIN drama_room_sign_in_rule rule ON info.sign_in_rule_id = rule.id
        WHERE
            info.`status` = 1
          AND rule.`status` = 1
          AND rule.type = #{type}
    </select>

    <insert id="insertDramaRoomSignInRuleInfo" parameterType="DramaRoomSignInRuleInfo" useGeneratedKeys="true" keyProperty="id">
        insert into drama_room_sign_in_rule_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="signInRuleId != null">sign_in_rule_id,</if>
            <if test="days != null">days,</if>
            <if test="num != null">num,</if>
            <if test="status != null and status != ''">status,</if>
            <if test="remark != null">remark,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="signInRuleId != null">#{signInRuleId},</if>
            <if test="days != null">#{days},</if>
            <if test="num != null">#{num},</if>
            <if test="status != null and status != ''">#{status},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updateDramaRoomSignInRuleInfo" parameterType="DramaRoomSignInRuleInfo">
        update drama_room_sign_in_rule_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="signInRuleId != null">sign_in_rule_id = #{signInRuleId},</if>
            <if test="days != null">days = #{days},</if>
            <if test="num != null">num = #{num},</if>
            <if test="status != null and status != ''">status = #{status},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteDramaRoomSignInRuleInfoById" parameterType="Long">
        delete from drama_room_sign_in_rule_info where id = #{id}
    </delete>

    <delete id="deleteDramaRoomSignInRuleInfoByIds" parameterType="String">
        delete from drama_room_sign_in_rule_info where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>