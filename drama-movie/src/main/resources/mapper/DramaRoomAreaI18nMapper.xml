<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.drama.mapper.DramaRoomAreaI18nMapper">

    <resultMap type="DramaRoomAreaI18n" id="DramaRoomAreaI18nResult">
        <result property="id"    column="id"    />
        <result property="areaId"    column="area_id"    />
        <result property="area"    column="area"    />
        <result property="languageCode"    column="language_code"    />
        <result property="createTime"    column="create_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectDramaRoomAreaI18nVo">
        select id, area_id, area, language_code, create_time, create_by, update_time, update_by, remark from drama_room_area_i18n
    </sql>

    <select id="selectDramaRoomAreaI18nList" parameterType="DramaRoomAreaI18n" resultMap="DramaRoomAreaI18nResult">
        <include refid="selectDramaRoomAreaI18nVo"/>
        <where>
            <if test="areaId != null "> and area_id = #{areaId}</if>
            <if test="area != null  and area != ''"> and area = #{area}</if>
            <if test="languageCode != null  and languageCode != ''"> and language_code = #{languageCode}</if>
        </where>
    </select>

    <select id="selectDramaRoomAreaI18nById" parameterType="Long" resultMap="DramaRoomAreaI18nResult">
        <include refid="selectDramaRoomAreaI18nVo"/>
        where id = #{id}
    </select>

    <select id="selectDramaRoomAreaI18nListByAreaIds" resultMap="DramaRoomAreaI18nResult">
        <include refid="selectDramaRoomAreaI18nVo"/>
        where area_id in
        <foreach item="id" collection="areaIds" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <insert id="insertDramaRoomAreaI18n" parameterType="DramaRoomAreaI18n" useGeneratedKeys="true" keyProperty="id">
        insert into drama_room_area_i18n
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="areaId != null">area_id,</if>
            <if test="area != null">area,</if>
            <if test="languageCode != null">language_code,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="areaId != null">#{areaId},</if>
            <if test="area != null">#{area},</if>
            <if test="languageCode != null">#{languageCode},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
    </insert>

    <insert id="insertDramaRoomAreaI18nList">
        insert into drama_room_area_i18n (area_id, area, language_code, create_time, create_by, update_time, update_by, remark) values
        <foreach item="item" index="index" collection="i18ns" separator=",">
            (#{item.areaId}, #{item.area}, #{item.languageCode}, #{item.createTime}, #{item.createBy}, #{item.updateTime}, #{item.updateBy}, #{item.remark})
        </foreach>
    </insert>

    <update id="updateDramaRoomAreaI18n" parameterType="DramaRoomAreaI18n">
        update drama_room_area_i18n
        <trim prefix="SET" suffixOverrides=",">
            <if test="areaId != null">area_id = #{areaId},</if>
            <if test="area != null">area = #{area},</if>
            <if test="languageCode != null">language_code = #{languageCode},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteDramaRoomAreaI18nById" parameterType="Long">
        delete from drama_room_area_i18n where id = #{id}
    </delete>

    <delete id="deleteDramaRoomAreaI18nByIds" parameterType="String">
        delete from drama_room_area_i18n where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    <delete id="deleteDramaRoomAreaI18nByAreaId">
        delete from drama_room_area_i18n where area_id = #{areaId}
    </delete>
    <delete id="deleteDramaRoomAreaI18nByAreaIds">
        delete from drama_room_area_i18n where area_id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>