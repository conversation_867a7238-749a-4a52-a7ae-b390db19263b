<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.drama.mapper.DramaRoomSysUserAppMovieMapper">

    <resultMap type="DramaRoomSysUserAppMovie" id="DramaRoomSysUserAppMovieResult">
        <result property="id"    column="id"    />
        <result property="sysUserId"    column="sys_user_id"    />
        <result property="type"    column="type"    />
        <result property="appId"    column="app_id"    />
        <result property="movieId"    column="movie_id"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
        <result property="childId"    column="child_id"    />
    </resultMap>

    <sql id="selectDramaRoomSysUserAppMovieVo">
        select id, sys_user_id, type, app_id, movie_id, create_by, create_time, update_by, update_time, remark, child_id from drama_room_sys_user_app_movie
    </sql>

    <select id="selectDramaRoomSysUserAppMovieList" parameterType="DramaRoomSysUserAppMovie" resultMap="DramaRoomSysUserAppMovieResult">
        <include refid="selectDramaRoomSysUserAppMovieVo"/>
        <where>
            <if test="sysUserId != null "> and sys_user_id = #{sysUserId}</if>
            <if test="type != null  and type != ''"> and type = #{type}</if>
            <if test="appId != null "> and app_id = #{appId}</if>
            <if test="movieId != null "> and movie_id = #{movieId}</if>
            <if test="childId != null "> and child_id = #{childId}</if>
        </where>
    </select>

    <select id="selectDramaRoomSysUserAppMovieById" parameterType="Long" resultMap="DramaRoomSysUserAppMovieResult">
        <include refid="selectDramaRoomSysUserAppMovieVo"/>
        where id = #{id}
    </select>
    <select id="getChildUserList" resultType="com.ruoyi.common.core.domain.entity.SysUser">
        SELECT
            su.*
        FROM
            sys_user su
                LEFT JOIN sys_user_post sup ON su.user_id = sup.user_id
                LEFT JOIN sys_post sp ON sp.post_id = sup.post_id
        WHERE
            su.`status` = 0
          AND sp.pid = #{postId}
    </select>

    <insert id="insertDramaRoomSysUserAppMovie" parameterType="DramaRoomSysUserAppMovie" useGeneratedKeys="true" keyProperty="id">
        insert into drama_room_sys_user_app_movie
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="sysUserId != null">sys_user_id,</if>
            <if test="type != null">type,</if>
            <if test="appId != null">app_id,</if>
            <if test="movieId != null">movie_id,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
            <if test="childId != null">child_id,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="sysUserId != null">#{sysUserId},</if>
            <if test="type != null">#{type},</if>
            <if test="appId != null">#{appId},</if>
            <if test="movieId != null">#{movieId},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
            <if test="childId != null">#{childId},</if>
        </trim>
    </insert>

    <update id="updateDramaRoomSysUserAppMovie" parameterType="DramaRoomSysUserAppMovie">
        update drama_room_sys_user_app_movie
        <trim prefix="SET" suffixOverrides=",">
            <if test="sysUserId != null">sys_user_id = #{sysUserId},</if>
            <if test="type != null">type = #{type},</if>
            <if test="appId != null">app_id = #{appId},</if>
            <if test="movieId != null">movie_id = #{movieId},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="childId != null">child_id = #{childId},</if>
        </trim>
        where id = #{id}
    </update>
    <update id="updateSysUserIdByPIdAndCId">
        UPDATE short_sys_user_app_movie SET sys_user_id = #{cId} WHERE  sys_user_id = #{pId}
    </update>

    <delete id="deleteDramaRoomSysUserAppMovieById" parameterType="Long">
        delete from drama_room_sys_user_app_movie where id = #{id}
    </delete>

    <delete id="deleteDramaRoomSysUserAppMovieByIds" parameterType="String">
        delete from drama_room_sys_user_app_movie where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>